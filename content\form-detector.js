/**
 * Auto Form Fill Pro - Form Detector
 * Intelligently detects forms and form fields on web pages
 */

class FormDetector {
  constructor() {
    this.forms = new Map();
    this.fieldTypes = {
      TEXT: 'text',
      EMAIL: 'email',
      PASSWORD: 'password',
      NUMBER: 'number',
      DATE: 'date',
      SELECT: 'select',
      CHECKBOX: 'checkbox',
      RADIO: 'radio',
      TEXTAREA: 'textarea',
      FILE: 'file'
    };
    
    this.fieldPatterns = this.initializeFieldPatterns();
    this.observer = null;
  }

  /**
   * Initialize field detection patterns
   */
  initializeFieldPatterns() {
    return {
      name: {
        patterns: [/name/i, /fullname/i, /full.name/i, /first.name/i, /last.name/i],
        aliases: ['name', 'fullName', 'firstName', 'lastName', 'username']
      },
      email: {
        patterns: [/email/i, /e.mail/i, /mail/i],
        aliases: ['email', 'emailAddress', 'userEmail']
      },
      phone: {
        patterns: [/phone/i, /tel/i, /mobile/i, /cell/i],
        aliases: ['phone', 'telephone', 'mobile', 'phoneNumber']
      },
      address: {
        patterns: [/address/i, /street/i, /addr/i],
        aliases: ['address', 'streetAddress', 'homeAddress']
      },
      city: {
        patterns: [/city/i, /town/i],
        aliases: ['city', 'town', 'locality']
      },
      state: {
        patterns: [/state/i, /province/i, /region/i],
        aliases: ['state', 'province', 'region']
      },
      zip: {
        patterns: [/zip/i, /postal/i, /postcode/i],
        aliases: ['zipCode', 'postalCode', 'postcode']
      },
      country: {
        patterns: [/country/i, /nation/i],
        aliases: ['country', 'nationality']
      },
      company: {
        patterns: [/company/i, /organization/i, /org/i, /employer/i],
        aliases: ['company', 'organization', 'employer']
      },
      job: {
        patterns: [/job/i, /title/i, /position/i, /role/i],
        aliases: ['jobTitle', 'position', 'role']
      },
      website: {
        patterns: [/website/i, /url/i, /site/i],
        aliases: ['website', 'url', 'homepage']
      },
      age: {
        patterns: [/age/i, /birth/i, /dob/i],
        aliases: ['age', 'dateOfBirth', 'birthDate']
      },
      password: {
        patterns: [/password/i, /pass/i, /pwd/i],
        aliases: ['password', 'currentPassword', 'newPassword']
      },
      username: {
        patterns: [/username/i, /user/i, /login/i],
        aliases: ['username', 'loginName', 'userId']
      }
    };
  }

  /**
   * Start detecting forms on the page
   */
  startDetection() {
    this.detectExistingForms();
    this.observeFormChanges();
  }

  /**
   * Stop form detection
   */
  stopDetection() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }

  /**
   * Detect all existing forms on the page
   */
  detectExistingForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => this.analyzeForm(form));
    
    // Also detect formless fields
    this.detectFormlessFields();
  }

  /**
   * Observe for dynamically added forms
   */
  observeFormChanges() {
    this.observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is a form
            if (node.tagName === 'FORM') {
              this.analyzeForm(node);
            }
            
            // Check for forms within the added node
            const forms = node.querySelectorAll ? node.querySelectorAll('form') : [];
            forms.forEach(form => this.analyzeForm(form));
            
            // Check for new input fields
            const inputs = node.querySelectorAll ? node.querySelectorAll('input, select, textarea') : [];
            if (inputs.length > 0) {
              this.detectFormlessFields();
            }
          }
        });
      });
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Analyze a form and its fields
   */
  analyzeForm(formElement) {
    const formId = this.generateFormId(formElement);
    
    const formData = {
      id: formId,
      element: formElement,
      action: formElement.action || '',
      method: formElement.method || 'GET',
      fields: new Map(),
      signature: '',
      confidence: 0
    };

    // Analyze all form fields
    const fields = formElement.querySelectorAll('input, select, textarea');
    fields.forEach(field => {
      const fieldData = this.analyzeField(field);
      if (fieldData) {
        formData.fields.set(fieldData.id, fieldData);
      }
    });

    // Generate form signature for identification
    formData.signature = this.generateFormSignature(formData);
    formData.confidence = this.calculateFormConfidence(formData);

    this.forms.set(formId, formData);
    return formData;
  }

  /**
   * Analyze an individual field
   */
  analyzeField(fieldElement) {
    if (this.shouldIgnoreField(fieldElement)) {
      return null;
    }

    const fieldData = {
      id: this.generateFieldId(fieldElement),
      element: fieldElement,
      type: this.getFieldType(fieldElement),
      name: fieldElement.name || '',
      placeholder: fieldElement.placeholder || '',
      label: this.getFieldLabel(fieldElement),
      required: fieldElement.required || false,
      semanticType: this.detectSemanticType(fieldElement),
      confidence: 0,
      fillable: true
    };

    fieldData.confidence = this.calculateFieldConfidence(fieldData);
    return fieldData;
  }

  /**
   * Detect fields that aren't inside forms
   */
  detectFormlessFields() {
    const allInputs = document.querySelectorAll('input, select, textarea');
    const formlessFields = Array.from(allInputs).filter(input => !input.closest('form'));
    
    if (formlessFields.length > 0) {
      const formlessFormId = 'formless_' + Date.now();
      const formlessForm = {
        id: formlessFormId,
        element: null,
        action: '',
        method: 'GET',
        fields: new Map(),
        signature: 'formless',
        confidence: 0.5, // Lower confidence for formless fields
        isFormless: true
      };

      formlessFields.forEach(field => {
        const fieldData = this.analyzeField(field);
        if (fieldData) {
          formlessForm.fields.set(fieldData.id, fieldData);
        }
      });

      if (formlessForm.fields.size > 0) {
        this.forms.set(formlessFormId, formlessForm);
      }
    }
  }

  /**
   * Get the semantic type of a field
   */
  detectSemanticType(element) {
    const text = this.getFieldText(element).toLowerCase();
    
    for (const [type, config] of Object.entries(this.fieldPatterns)) {
      for (const pattern of config.patterns) {
        if (pattern.test(text)) {
          return type;
        }
      }
    }

    // Fallback based on input type
    switch (element.type) {
      case 'email':
        return 'email';
      case 'tel':
        return 'phone';
      case 'password':
        return 'password';
      case 'date':
        return 'age';
      case 'url':
        return 'website';
      default:
        return 'text';
    }
  }

  /**
   * Get all text associated with a field for analysis
   */
  getFieldText(element) {
    const texts = [];
    
    // Element attributes
    texts.push(element.name || '');
    texts.push(element.id || '');
    texts.push(element.placeholder || '');
    texts.push(element.className || '');
    
    // Label text
    const label = this.getFieldLabel(element);
    if (label) {
      texts.push(label);
    }
    
    // Surrounding text
    const surroundingText = this.getSurroundingText(element);
    if (surroundingText) {
      texts.push(surroundingText);
    }
    
    return texts.join(' ');
  }

  /**
   * Get the label associated with a field
   */
  getFieldLabel(element) {
    // Try to find label by 'for' attribute
    if (element.id) {
      const label = document.querySelector(`label[for="${element.id}"]`);
      if (label) {
        return label.textContent.trim();
      }
    }
    
    // Try to find parent label
    const parentLabel = element.closest('label');
    if (parentLabel) {
      return parentLabel.textContent.trim();
    }
    
    // Try to find nearby text
    const nearbyText = this.getNearbyText(element);
    if (nearbyText) {
      return nearbyText;
    }
    
    return '';
  }

  /**
   * Get text near the field element
   */
  getNearbyText(element) {
    // Check previous sibling
    let sibling = element.previousElementSibling;
    if (sibling && sibling.textContent.trim()) {
      return sibling.textContent.trim();
    }
    
    // Check parent's previous sibling
    const parent = element.parentElement;
    if (parent) {
      sibling = parent.previousElementSibling;
      if (sibling && sibling.textContent.trim()) {
        return sibling.textContent.trim();
      }
    }
    
    return '';
  }

  /**
   * Get surrounding text context
   */
  getSurroundingText(element) {
    const parent = element.parentElement;
    if (!parent) return '';
    
    const textContent = parent.textContent.trim();
    // Extract meaningful text (not too long)
    return textContent.length > 100 ? textContent.substring(0, 100) : textContent;
  }

  /**
   * Get the type of a field
   */
  getFieldType(element) {
    const tagName = element.tagName.toLowerCase();
    
    switch (tagName) {
      case 'input':
        return element.type || 'text';
      case 'select':
        return 'select';
      case 'textarea':
        return 'textarea';
      default:
        return 'unknown';
    }
  }

  /**
   * Check if a field should be ignored
   */
  shouldIgnoreField(element) {
    // Ignore hidden fields (except password)
    if (element.type === 'hidden') {
      return true;
    }
    
    // Ignore submit/button fields
    if (['submit', 'button', 'reset', 'image'].includes(element.type)) {
      return true;
    }
    
    // Ignore disabled or readonly fields
    if (element.disabled || element.readOnly) {
      return true;
    }
    
    // Ignore fields that are not visible
    if (!this.isElementVisible(element)) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if an element is visible
   */
  isElementVisible(element) {
    const style = window.getComputedStyle(element);
    return (
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      element.offsetWidth > 0 &&
      element.offsetHeight > 0
    );
  }

  /**
   * Generate a unique ID for a form
   */
  generateFormId(formElement) {
    if (formElement.id) {
      return formElement.id;
    }
    
    // Generate based on action and position
    const action = formElement.action || 'no-action';
    const position = Array.from(document.querySelectorAll('form')).indexOf(formElement);
    return `form_${action.replace(/[^a-zA-Z0-9]/g, '_')}_${position}`;
  }

  /**
   * Generate a unique ID for a field
   */
  generateFieldId(fieldElement) {
    if (fieldElement.id) {
      return fieldElement.id;
    }
    
    if (fieldElement.name) {
      return fieldElement.name;
    }
    
    // Generate based on type and position
    const type = fieldElement.type || fieldElement.tagName.toLowerCase();
    const form = fieldElement.closest('form');
    const selector = form ? 'input, select, textarea' : 'input, select, textarea';
    const allFields = form ? form.querySelectorAll(selector) : document.querySelectorAll(selector);
    const position = Array.from(allFields).indexOf(fieldElement);
    
    return `${type}_field_${position}`;
  }

  /**
   * Generate a signature for form identification
   */
  generateFormSignature(formData) {
    const fieldTypes = Array.from(formData.fields.values())
      .map(field => `${field.semanticType}:${field.type}`)
      .sort()
      .join('|');
    
    const action = formData.action ? new URL(formData.action).pathname : '';
    return `${action}:${fieldTypes}`;
  }

  /**
   * Calculate confidence score for a form
   */
  calculateFormConfidence(formData) {
    let score = 0;
    const fieldCount = formData.fields.size;
    
    // Base score based on field count
    score += Math.min(fieldCount * 0.1, 0.5);
    
    // Boost for having action
    if (formData.action) {
      score += 0.2;
    }
    
    // Boost for semantic field types
    const semanticFields = Array.from(formData.fields.values())
      .filter(field => field.semanticType !== 'text');
    score += semanticFields.length * 0.05;
    
    return Math.min(score, 1.0);
  }

  /**
   * Calculate confidence score for a field
   */
  calculateFieldConfidence(fieldData) {
    let score = 0.5; // Base score
    
    // Boost for having a label
    if (fieldData.label) {
      score += 0.2;
    }
    
    // Boost for semantic type detection
    if (fieldData.semanticType !== 'text') {
      score += 0.2;
    }
    
    // Boost for required fields
    if (fieldData.required) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * Get all detected forms
   */
  getForms() {
    return Array.from(this.forms.values());
  }

  /**
   * Get a specific form by ID
   */
  getForm(formId) {
    return this.forms.get(formId);
  }

  /**
   * Get form by element
   */
  getFormByElement(element) {
    for (const form of this.forms.values()) {
      if (form.element === element) {
        return form;
      }
    }
    return null;
  }

  /**
   * Find the best matching form for given field types
   */
  findBestMatchingForm(desiredFields) {
    let bestMatch = null;
    let bestScore = 0;
    
    for (const form of this.forms.values()) {
      const score = this.scoreFormMatch(form, desiredFields);
      if (score > bestScore) {
        bestScore = score;
        bestMatch = form;
      }
    }
    
    return bestMatch;
  }

  /**
   * Score how well a form matches desired fields
   */
  scoreFormMatch(form, desiredFields) {
    const formFields = Array.from(form.fields.values());
    let matchCount = 0;
    
    for (const desiredField of desiredFields) {
      const hasMatch = formFields.some(field => 
        field.semanticType === desiredField ||
        field.name === desiredField ||
        field.id === desiredField
      );
      
      if (hasMatch) {
        matchCount++;
      }
    }
    
    return matchCount / Math.max(desiredFields.length, formFields.length);
  }

  /**
   * Clear all detected forms
   */
  clear() {
    this.forms.clear();
  }

  /**
   * Get detection statistics
   */
  getStats() {
    const forms = Array.from(this.forms.values());
    const totalFields = forms.reduce((sum, form) => sum + form.fields.size, 0);
    
    return {
      formsDetected: forms.length,
      fieldsDetected: totalFields,
      averageConfidence: forms.reduce((sum, form) => sum + form.confidence, 0) / forms.length || 0,
      formlessFields: forms.filter(form => form.isFormless).length
    };
  }
}

// Export for use in other modules
window.FormDetector = FormDetector; 