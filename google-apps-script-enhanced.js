/**
 * Google Apps Script for Attendance Report Synchronization
 * 
 * This script receives attendance data from the VenusHR14 Attendance Report System
 * and synchronizes it with a Google Spreadsheet.
 * 
 * Setup Instructions:
 * 1. Create a new Google Apps Script project
 * 2. Replace the default code with this script
 * 3. Create a Google Spreadsheet with a sheet named 'AttendanceData'
 * 4. Set up the spreadsheet with headers in row 1:
 *    A: Employee ID | B: Employee Name | C: Date | D: Day of Week | E: Shift 
 *    F: Check In | G: Check Out | H: Regular Hours | I: Overtime Hours | J: Total Hours
 * 5. Deploy as web app with execute permissions for "Anyone"
 * 6. Copy the deployment URL to use in the attendance report system
 * 
 * <AUTHOR> AI Assistant
 * @version 2.0
 */

/**
 * Handle GET requests from the attendance report system
 * @param {Object} e - Event object containing request parameters
 * @returns {ContentService.TextOutput} JSON response
 */
function doGet(e) {
  try {
    // Enable CORS headers for extension compatibility
    const response = handleRequest(e);
    return ContentService
      .createTextOutput(JSON.stringify(response))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
  } catch (error) {
    console.error('Error in doGet:', error);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
  }
}

/**
 * Main request handler with extension compatibility
 * @param {Object} e - Event object containing request parameters
 * @returns {Object} Response object
 */
function handleRequest(e) {
  const action = e.parameter.action;
  const sheetName = e.parameter.sheet || 'monthlyGridData_May_2025';
  
  console.log(`Handling request: action=${action}, sheet=${sheetName}`);
  
  switch (action) {
    case 'test':
      return testConnection(sheetName);
    
    case 'getData':
      return getAttendanceData(sheetName);
    
    case 'sync_attendance':
      return handleAttendanceSync(e.parameter);
    
    case 'sync_monthly_grid':
      return handleMonthlyGridSync(e.parameter);
    
    case 'sync_daily_grid':
      return handleDailyGridSync(e.parameter.data);
    
    case 'get_data':
      return handleGetData(e.parameter);
    
    case 'clear_data':
      return handleClearData(e.parameter);
    
    case 'getSheets':
      return getAvailableSheets();
    
    case 'validateData':
      return validateSheetData(sheetName);
    
    default:
      return {
        success: false,
        error: `Unknown action: ${action}. Available actions: test, getData, sync_attendance, sync_monthly_grid, sync_daily_grid, get_data, clear_data, getSheets, validateData`
      };
  }
}

/**
 * Test connection to the spreadsheet and sheet (Extension compatible)
 * @param {string} sheetName - Name of the sheet to test
 * @returns {Object} Test result
 */
function testConnection(sheetName) {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName(sheetName);
    
    if (!sheet) {
      return {
        success: false,
        error: `Sheet "${sheetName}" not found. Available sheets: ${getSheetNames().join(', ')}`
      };
    }
    
    const lastRow = sheet.getLastRow();
    const lastColumn = sheet.getLastColumn();
    
    return {
      success: true,
      message: 'Connection successful',
      sheetInfo: {
        name: sheetName,
        rows: lastRow,
        columns: lastColumn,
        spreadsheetId: spreadsheet.getId(),
        spreadsheetName: spreadsheet.getName()
      }
    };
  } catch (error) {
    console.error('Test connection error:', error);
    return {
      success: false,
      error: `Connection test failed: ${error.toString()}`
    };
  }
}

/**
 * Get attendance data from the specified sheet (Extension compatible)
 * @param {string} sheetName - Name of the sheet containing attendance data
 * @returns {Object} Attendance data or error
 */
function getAttendanceData(sheetName) {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName(sheetName);
    
    if (!sheet) {
      return {
        success: false,
        error: `Sheet "${sheetName}" not found`
      };
    }
    
    // Get all data from the sheet
    const range = sheet.getDataRange();
    const values = range.getValues();
    
    if (values.length === 0) {
      return {
        success: false,
        error: 'Sheet is empty'
      };
    }
    
    // Remove header row (first row)
    const headers = values[0];
    const dataRows = values.slice(1);
    
    // Filter out empty rows
    const filteredData = dataRows.filter(row => {
      return row.some(cell => cell !== null && cell !== undefined && cell !== '');
    });
    
    // Process data for extension compatibility
    const processedData = filteredData.map((row, index) => {
      const employeeId = row[0] || '';
      const employeeName = row[1] || '';
      const attendanceData = row.slice(2); // All remaining columns as attendance data
      
      return {
        employeeId: employeeId,
        employeeName: employeeName,
        attendanceData: attendanceData,
        rawAttendanceData: attendanceData
      };
    });
    
    console.log(`Retrieved ${processedData.length} data rows from sheet "${sheetName}"`);
    
    return {
      success: true,
      data: processedData,
      headers: headers,
      metadata: {
        sheetName: sheetName,
        totalRows: processedData.length,
        totalColumns: headers.length,
        lastUpdated: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Get attendance data error:', error);
    return {
      success: false,
      error: `Failed to retrieve data: ${error.toString()}`
    };
  }
}

/**
 * Handle attendance data synchronization
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleAttendanceSync(params) {
  try {
    const dataParam = params.data;
    if (!dataParam) {
      return { 
        status: 'error', 
        message: 'No data provided for sync' 
      };
    }

    let attendanceData;
    try {
      attendanceData = JSON.parse(dataParam);
    } catch (parseError) {
      return { 
        status: 'error', 
        message: 'Invalid JSON data format' 
      };
    }

    if (!Array.isArray(attendanceData) || attendanceData.length === 0) {
      return { 
        status: 'error', 
        message: 'Data must be a non-empty array' 
      };
    }

    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = spreadsheet.getSheetByName('AttendanceData');
    
    if (!sheet) {
      sheet = spreadsheet.insertSheet('AttendanceData');
      
      const headers = [
        'Employee ID', 'Employee Name', 'Date', 'Day of Week', 'Shift',
        'Check In', 'Check Out', 'Regular Hours', 'Overtime Hours', 'Total Hours',
        'Sync Date', 'Sync Time'
      ];
      
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('white');
      headerRange.setFontWeight('bold');
      headerRange.setWrap(true);
      
      // Set column widths
      sheet.setColumnWidth(1, 120); // Employee ID
      sheet.setColumnWidth(2, 200); // Employee Name
      sheet.setColumnWidth(3, 100); // Date
      sheet.setColumnWidth(4, 100); // Day of Week
      sheet.setColumnWidth(5, 80);  // Shift
      sheet.setColumnWidth(6, 100); // Check In
      sheet.setColumnWidth(7, 100); // Check Out
      sheet.setColumnWidth(8, 100); // Regular Hours
      sheet.setColumnWidth(9, 100); // Overtime Hours
      sheet.setColumnWidth(10, 100); // Total Hours
      sheet.setColumnWidth(11, 120); // Sync Date
      sheet.setColumnWidth(12, 120); // Sync Time
    }

    const currentDate = new Date();
    const syncDate = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'yyyy-MM-dd');
    const syncTime = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'HH:mm:ss');
    
    const dataToInsert = [];
    
    for (const record of attendanceData) {
      const row = [
        record.employeeId || '',
        record.employeeName || '',
        record.date || '',
        record.dayOfWeek || '',
        record.shift || '',
        record.checkIn || '',
        record.checkOut || '',
        parseFloat(record.regularHours) || 0,
        parseFloat(record.overtimeHours) || 0,
        parseFloat(record.totalHours) || 0,
        syncDate,
        syncTime
      ];
      dataToInsert.push(row);
    }

    const lastRow = sheet.getLastRow();
    const startRow = lastRow + 1;
    
    if (dataToInsert.length > 0) {
      const range = sheet.getRange(startRow, 1, dataToInsert.length, dataToInsert[0].length);
      range.setValues(dataToInsert);
      
      for (let i = 0; i < dataToInsert.length; i++) {
        const rowNumber = startRow + i;
        const rowRange = sheet.getRange(rowNumber, 1, 1, dataToInsert[0].length);
        
        if (rowNumber % 2 === 0) {
          rowRange.setBackground('#f8f9fa');
        } else {
          rowRange.setBackground('#ffffff');
        }
      }
    }

    return { 
      status: 'success', 
      message: `Successfully synced ${attendanceData.length} attendance records`,
      data: {
        records_synced: attendanceData.length,
        sync_date: syncDate,
        sync_time: syncTime,
        sheet_name: 'AttendanceData',
        total_rows: sheet.getLastRow()
      }
    };

  } catch (error) {
    console.error('Error in handleAttendanceSync:', error);
    return { 
      status: 'error', 
      message: 'Sync failed: ' + error.toString() 
    };
  }
}

/**
 * Handle monthly grid data synchronization
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleMonthlyGridSync(params) {
  try {
    const dataParam = params.data;
    if (!dataParam) {
      return { 
        status: 'error', 
        message: 'No data provided for monthly grid sync' 
      };
    }

    let monthlyGridData;
    try {
      monthlyGridData = JSON.parse(dataParam);
    } catch (parseError) {
      return { 
        status: 'error', 
        message: 'Invalid JSON data format' 
      };
    }

    if (!Array.isArray(monthlyGridData) || monthlyGridData.length === 0) {
      return { 
        status: 'error', 
        message: 'Data must be a non-empty array' 
      };
    }

    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = spreadsheet.getSheetByName('MonthlyGridData');
    
    if (!sheet) {
      sheet = spreadsheet.insertSheet('MonthlyGridData');
      
      const headers = [
        'No', 'Employee ID', 'Employee Name', 'Year', 'Month', 'Month Name',
        'Total Working Days', 'Total Regular Hours', 'Total Overtime Hours', 'Total Hours',
        'Sync Date', 'Sync Time'
      ];
      
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setBackground('#28a745');
      headerRange.setFontColor('white');
      headerRange.setFontWeight('bold');
      headerRange.setWrap(true);
      
      // Set column widths
      sheet.setColumnWidth(1, 60);  // No
      sheet.setColumnWidth(2, 120); // Employee ID
      sheet.setColumnWidth(3, 200); // Employee Name
      sheet.setColumnWidth(4, 80);  // Year
      sheet.setColumnWidth(5, 80);  // Month
      sheet.setColumnWidth(6, 120); // Month Name
      sheet.setColumnWidth(7, 140); // Total Working Days
      sheet.setColumnWidth(8, 140); // Total Regular Hours
      sheet.setColumnWidth(9, 140); // Total Overtime Hours
      sheet.setColumnWidth(10, 120); // Total Hours
      sheet.setColumnWidth(11, 120); // Sync Date
      sheet.setColumnWidth(12, 120); // Sync Time
    }

    const currentDate = new Date();
    const syncDate = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'yyyy-MM-dd');
    const syncTime = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), 'HH:mm:ss');
    
    const dataToInsert = [];
    
    for (const record of monthlyGridData) {
      const row = [
        record.no || '',
        record.employeeId || '',
        record.employeeName || '',
        parseInt(record.year) || 0,
        parseInt(record.month) || 0,
        record.monthName || '',
        parseInt(record.totalWorkingDays) || 0,
        parseFloat(record.totalRegularHours) || 0,
        parseFloat(record.totalOvertimeHours) || 0,
        parseFloat(record.totalHours) || 0,
        syncDate,
        syncTime
      ];
      dataToInsert.push(row);
    }

    const lastRow = sheet.getLastRow();
    const startRow = lastRow + 1;
    
    if (dataToInsert.length > 0) {
      const range = sheet.getRange(startRow, 1, dataToInsert.length, dataToInsert[0].length);
      range.setValues(dataToInsert);
      
      for (let i = 0; i < dataToInsert.length; i++) {
        const rowNumber = startRow + i;
        const rowRange = sheet.getRange(rowNumber, 1, 1, dataToInsert[0].length);
        
        if (rowNumber % 2 === 0) {
          rowRange.setBackground('#f8f9fa');
        } else {
          rowRange.setBackground('#ffffff');
        }
      }
      
      const workingDaysRange = sheet.getRange(startRow, 7, dataToInsert.length, 1);
      workingDaysRange.setNumberFormat('0');
      
      const hoursRange = sheet.getRange(startRow, 8, dataToInsert.length, 3);
      hoursRange.setNumberFormat('0.0');
    }

    return { 
      status: 'success', 
      message: `Successfully synced ${dataToInsert.length} monthly grid records`,
      records_synced: dataToInsert.length,
      sync_date: syncDate,
      sync_time: syncTime
    };

  } catch (error) {
    console.error('Error in handleMonthlyGridSync:', error);
    return { 
      status: 'error', 
      message: 'Monthly grid sync failed: ' + error.toString() 
    };
  }
}

/**
 * Handle enhanced daily grid data synchronization
 * @param {string} dataString - JSON string containing daily grid data
 * @returns {Object} Result object
 */
function handleDailyGridSync(dataString) {
  try {
    console.log('=== Handling Enhanced Daily Grid Sync ===');
    
    if (!dataString) {
      throw new Error('No data provided for daily grid sync');
    }
    
    const data = JSON.parse(dataString);
    console.log('Parsed data records:', data.length);
    console.log('Sample record:', JSON.stringify(data[0], null, 2));
    
    // Get month/year info from first record
    const firstRecord = data[0];
    const monthName = firstRecord.monthName || 'Unknown';
    const year = firstRecord.year || new Date().getFullYear();
    const month = firstRecord.month || new Date().getMonth() + 1;
    const daysInMonth = firstRecord.daysInMonth || 31;
    
    // Create sheet name with month/year
    const sheetName = `monthlyGridData_${monthName}_${year}`;
    
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = spreadsheet.getSheetByName(sheetName);
    
    if (!sheet) {
      console.log('Creating new sheet:', sheetName);
      sheet = spreadsheet.insertSheet(sheetName);
      
      // Create headers - Enhanced to include all visible columns
      const headers = ['No', 'Employee ID', 'Employee Name'];
      
      // Add day headers
      for (let day = 1; day <= daysInMonth; day++) {
        // Calculate day name
        const date = new Date(year, month - 1, day);
        const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];
        const dayName = dayNames[date.getDay()];
        headers.push(`${day}\n(${dayName})`);
      }
      
      // Add summary columns (matching UI grid totals)
      headers.push('Total\nDays');
      headers.push('Regular\nHours');
      headers.push('Overtime\nHours');
      
      // Add charge job columns (matching new integration)
      headers.push('Task\nCode');
      headers.push('Machine\nCode');
      headers.push('Expense\nCode');
      
      // Add sync metadata
      headers.push('Sync Date');
      headers.push('Sync Time');
      
      // Set headers
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      sheet.getRange(1, 1, 1, headers.length).setBackground('#4285f4');
      sheet.getRange(1, 1, 1, headers.length).setFontColor('white');
      sheet.getRange(1, 1, 1, headers.length).setHorizontalAlignment('center');
      sheet.getRange(1, 1, 1, headers.length).setWrapStrategy(SpreadsheetApp.WrapStrategy.WRAP);
      
      // Set column widths
      sheet.setColumnWidth(1, 60);   // No
      sheet.setColumnWidth(2, 150);  // Employee ID
      sheet.setColumnWidth(3, 200);  // Employee Name
      
      // Set day columns width
      for (let i = 4; i <= 3 + daysInMonth; i++) {
        sheet.setColumnWidth(i, 80);
      }
      
      // Set summary columns width
      const summaryStartCol = 4 + daysInMonth;
      sheet.setColumnWidth(summaryStartCol, 80);     // Total Days
      sheet.setColumnWidth(summaryStartCol + 1, 100); // Regular Hours
      sheet.setColumnWidth(summaryStartCol + 2, 100); // Overtime Hours
      
      // Set charge job columns width
      sheet.setColumnWidth(summaryStartCol + 3, 150); // Task Code
      sheet.setColumnWidth(summaryStartCol + 4, 150); // Machine Code
      sheet.setColumnWidth(summaryStartCol + 5, 150); // Expense Code
      
      // Set sync metadata columns width
      sheet.setColumnWidth(summaryStartCol + 6, 120); // Sync Date
      sheet.setColumnWidth(summaryStartCol + 7, 120); // Sync Time
      
      // Color Sunday headers (red) and Saturday headers (blue)
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month - 1, day);
        const col = 3 + day; // Column position
        if (date.getDay() === 0) { // Sunday
          sheet.getRange(1, col).setBackground('#dc3545');
        } else if (date.getDay() === 6) { // Saturday
          sheet.getRange(1, col).setBackground('#6f42c1');
        }
      }
      
      // Color summary headers (green)
      for (let i = 0; i < 3; i++) {
        sheet.getRange(1, summaryStartCol + i).setBackground('#198754');
      }
      
      // Color charge job headers (blue)
      for (let i = 3; i < 6; i++) {
        sheet.getRange(1, summaryStartCol + i).setBackground('#0d6efd');
      }
    }
    
    // Get current date/time for sync tracking
    const now = new Date();
    const syncDate = Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy-MM-dd');
    const syncTime = Utilities.formatDate(now, Session.getScriptTimeZone(), 'HH:mm:ss');
    
    // Prepare data for insertion with enhanced data processing
    const rows = data.map(record => {
      const row = [
        record.no || '',
        record.employeeId || '',
        record.employeeName || ''
      ];
      
      // Add daily hours data and calculate totals
      let totalWorkingDays = 0;
      let totalRegularHours = 0;
      let totalOvertimeHours = 0;
      
      for (let day = 1; day <= daysInMonth; day++) {
        const hours = record.dailyHours[day.toString()] || '-';
        row.push(hours);
        
        // Calculate totals for summary columns
        if (hours !== '-' && hours !== 'OFF') {
          if (hours.includes('|')) {
            const parts = hours.split('|');
            if (parts.length === 2) {
              const regularPart = parts[0].trim().replace(/[()]/g, '');
              const overtimePart = parts[1].trim().replace(/[()]/g, '');
              
              const regularHours = parseFloat(regularPart) || 0;
              const overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
              
              if (regularHours > 0 || overtimeHours > 0) {
                totalWorkingDays++;
                totalRegularHours += regularHours;
                totalOvertimeHours += overtimeHours;
              }
            }
          } else {
            const hoursNum = parseFloat(hours);
            if (hoursNum > 0) {
              totalWorkingDays++;
              totalRegularHours += hoursNum;
            }
          }
        }
      }
      
      // Add summary columns
      row.push(totalWorkingDays);
      row.push(totalRegularHours.toFixed(1));
      row.push(totalOvertimeHours.toFixed(1));
      
      // Add charge job data (if available)
      row.push(record.taskCode || '');
      row.push(record.machineCode || '');
      row.push(record.expenseCode || '');
      
      // Add sync metadata
      row.push(syncDate);
      row.push(syncTime);
      
      return row;
    });
    
    // Insert data
    const startRow = sheet.getLastRow() + 1;
    console.log('Inserting enhanced daily data starting at row:', startRow);
    
    sheet.getRange(startRow, 1, rows.length, rows[0].length).setValues(rows);
    
    // Apply enhanced formatting with conditional formatting
    for (let i = 0; i < rows.length; i++) {
      const currentRow = startRow + i;
      const record = data[i];
      
      // Format daily hours columns with enhanced conditional formatting
      for (let day = 1; day <= daysInMonth; day++) {
        const col = 3 + day;
        const hours = record.dailyHours[day.toString()] || '-';
        const cell = sheet.getRange(currentRow, col);
        
        // Apply enhanced color coding based on hours
        if (hours === 'OFF') {
          cell.setBackground('#d1ecf1');
          cell.setFontColor('#055160');
        } else if (hours === '-') {
          cell.setBackground('#f8f9fa');
          cell.setFontColor('#6c757d');
        } else if (hours.includes('|')) {
          // Parse regular and overtime hours
          const parts = hours.split('|');
          if (parts.length === 2) {
            const regularPart = parts[0].trim().replace(/[()]/g, '');
            const overtimePart = parts[1].trim().replace(/[()]/g, '');
            
            const regularHours = parseFloat(regularPart) || 0;
            const overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
            
            // Check if Saturday (different threshold)
            const date = new Date(year, month - 1, day);
            const isSaturday = date.getDay() === 6;
            
            // Enhanced formatting based on work hours
            if (regularHours === 7 && !isSaturday) {
              // BRIGHT GREEN for exactly 7 hours normal work (Monday-Friday)
              cell.setBackground('#22c55e');
              cell.setFontColor('#ffffff');
              cell.setFontWeight('bold');
              cell.setBorder(true, true, true, true, false, false, '#16a34a', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
            } else if (regularHours === 5 && isSaturday) {
              // BRIGHT GREEN for exactly 5 hours normal work (Saturday)
              cell.setBackground('#22c55e');
              cell.setFontColor('#ffffff');
              cell.setFontWeight('bold');
              cell.setBorder(true, true, true, true, false, false, '#16a34a', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
            } else if (regularHours > 7) {
              cell.setBackground('#2e7d32');
              cell.setFontColor('#ffffff');
              cell.setFontWeight('bold');
            } else if (regularHours > 0 && overtimeHours === 0) {
              cell.setBackground('#c8e6c9');
              cell.setFontColor('#2e7d32');
              cell.setFontWeight('bold');
            } else if (regularHours > 0 && overtimeHours > 0) {
              cell.setBackground('#fff3cd');
              cell.setFontColor('#664d03');
              cell.setFontWeight('bold');
            }
          }
        }
        
        cell.setHorizontalAlignment('center');
        cell.setFontFamily('Courier New');
        cell.setFontSize(9);
      }
      
      // Format summary columns
      const summaryStartCol = 4 + daysInMonth;
      
      // Total Days
      const totalDaysCell = sheet.getRange(currentRow, summaryStartCol);
      totalDaysCell.setBackground('#e8f5e8');
      totalDaysCell.setFontWeight('bold');
      totalDaysCell.setHorizontalAlignment('center');
      
      // Regular Hours
      const regularHoursCell = sheet.getRange(currentRow, summaryStartCol + 1);
      regularHoursCell.setBackground('#e8f5e8');
      regularHoursCell.setFontWeight('bold');
      regularHoursCell.setHorizontalAlignment('center');
      
      // Overtime Hours
      const overtimeHoursCell = sheet.getRange(currentRow, summaryStartCol + 2);
      overtimeHoursCell.setBackground('#e8f5e8');
      overtimeHoursCell.setFontWeight('bold');
      overtimeHoursCell.setHorizontalAlignment('center');
      
      // Format charge job columns
      for (let j = 3; j < 6; j++) {
        const chargeJobCell = sheet.getRange(currentRow, summaryStartCol + j);
        chargeJobCell.setBackground('#e8f5e8');
        chargeJobCell.setFontWeight('bold');
        chargeJobCell.setHorizontalAlignment('center');
        chargeJobCell.setFontSize(8);
      }
      
      // Add alternating row colors for employee info columns
      if (currentRow % 2 === 0) {
        sheet.getRange(currentRow, 1, 1, 3).setBackground('#f8f9fa');
      }
    }
    
    // Freeze header row and first 3 columns
    sheet.setFrozenRows(1);
    sheet.setFrozenColumns(3);
    
    console.log(`Successfully synced ${data.length} enhanced daily grid records to sheet ${sheetName}`);
    
    return {
      status: 'success',
      message: `Successfully synced ${data.length} enhanced daily grid records with all visible columns`,
      records_synced: data.length,
      sync_date: syncDate,
      sync_time: syncTime,
      sheet_name: sheetName,
      start_row: startRow,
      end_row: startRow + rows.length - 1,
      month_name: monthName,
      year: year,
      days_in_month: daysInMonth,
      enhanced_features: [
        '7-hour conditional formatting',
        'Total hours summary columns', 
        'Charge job data integration',
        'Perfect attendance highlighting'
      ]
    };
    
  } catch (error) {
    console.error('Error in enhanced handleDailyGridSync:', error);
    throw error;
  }
}

/**
 * Handle get data requests (Extension compatible)
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleGetData(params) {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName('AttendanceData');
    
    if (!sheet) {
      return { 
        status: 'error', 
        message: 'AttendanceData sheet not found' 
      };
    }

    const lastRow = sheet.getLastRow();
    if (lastRow <= 1) {
      return { 
        status: 'success', 
        data: [],
        message: 'No data found in the sheet'
      };
    }

    const dataRange = sheet.getRange(2, 1, lastRow - 1, 12);
    const values = dataRange.getValues();
    
    const formattedData = values.map(row => ({
      employeeId: row[0],
      employeeName: row[1],
      date: row[2],
      dayOfWeek: row[3],
      shift: row[4],
      checkIn: row[5],
      checkOut: row[6],
      regularHours: row[7],
      overtimeHours: row[8],
      totalHours: row[9],
      syncDate: row[10],
      syncTime: row[11]
    }));

    return { 
      status: 'success', 
      data: formattedData,
      total_records: formattedData.length
    };

  } catch (error) {
    console.error('Error in handleGetData:', error);
    return { 
      status: 'error', 
      message: 'Failed to get data: ' + error.toString() 
    };
  }
}

/**
 * Handle clear data requests
 * @param {Object} params - Request parameters
 * @returns {Object} Result object
 */
function handleClearData(params) {
  try {
    const confirmClear = params.confirm;
    if (confirmClear !== 'yes') {
      return { 
        status: 'error', 
        message: 'Clear operation requires confirm=yes parameter' 
      };
    }

    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName('AttendanceData');
    
    if (!sheet) {
      return { 
        status: 'error', 
        message: 'AttendanceData sheet not found' 
      };
    }

    const lastRow = sheet.getLastRow();
    if (lastRow > 1) {
      sheet.deleteRows(2, lastRow - 1);
    }

    return { 
      status: 'success', 
      message: 'All attendance data cleared successfully' 
    };

  } catch (error) {
    console.error('Error in handleClearData:', error);
    return { 
      status: 'error', 
      message: 'Failed to clear data: ' + error.toString() 
    };
  }
}

/**
 * Get list of available sheets (Extension compatible)
 * @returns {Object} List of sheets
 */
function getAvailableSheets() {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheets = spreadsheet.getSheets();
    const sheetNames = sheets.map(sheet => sheet.getName());
    
    return {
      success: true,
      sheets: sheetNames,
      total_sheets: sheetNames.length,
      spreadsheet_name: spreadsheet.getName(),
      spreadsheet_id: spreadsheet.getId()
    };
  } catch (error) {
    console.error('Error getting available sheets:', error);
    return {
      success: false,
      error: 'Failed to get available sheets: ' + error.toString()
    };
  }
}

/**
 * Validate sheet data structure (Extension compatible)
 * @param {string} sheetName - Name of the sheet to validate
 * @returns {Object} Validation result
 */
function validateSheetData(sheetName) {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName(sheetName);
    
    if (!sheet) {
      return {
        success: false,
        error: `Sheet "${sheetName}" not found`
      };
    }
    
    const lastRow = sheet.getLastRow();
    const lastColumn = sheet.getLastColumn();
    
    if (lastRow === 0) {
      return {
        success: false,
        error: 'Sheet is empty'
      };
    }
    
    // Get headers
    const headers = sheet.getRange(1, 1, 1, lastColumn).getValues()[0];
    
    // Basic validation
    const validationResult = {
      success: true,
      validation: {
        has_headers: headers.length > 0,
        total_rows: lastRow,
        total_columns: lastColumn,
        headers: headers,
        data_rows: lastRow - 1,
        estimated_records: lastRow > 1 ? lastRow - 1 : 0
      }
    };
    
    return validationResult;
    
  } catch (error) {
    console.error('Error validating sheet data:', error);
    return {
      success: false,
      error: 'Validation failed: ' + error.toString()
    };
  }
}

/**
 * Get sheet names helper function
 * @returns {Array} Array of sheet names
 */
function getSheetNames() {
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheets = spreadsheet.getSheets();
    return sheets.map(sheet => sheet.getName());
  } catch (error) {
    console.error('Error getting sheet names:', error);
    return [];
  }
}

/**
 * Handle POST requests (alternative method for large data)
 * @param {Object} e - Event object containing request data
 * @returns {ContentService.TextOutput} JSON response
 */
function doPost(e) {
  try {
    console.log('=== POST Request Received ===');

    let action, data;

    if (e.parameter && e.parameter.action) {
      action = e.parameter.action;
      data = e.parameter.data;
      console.log('Form data - Action:', action);
    } else if (e.postData && e.postData.contents) {
      const requestData = JSON.parse(e.postData.contents);
      action = requestData.action;
      data = requestData.data;
      console.log('JSON data - Action:', action);
    } else {
      return ContentService.createTextOutput(
        JSON.stringify({
          status: 'error',
          message: 'No POST data received'
        })
      ).setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });
    }

    if (!action) {
      return ContentService.createTextOutput(
        JSON.stringify({
          status: 'error',
          message: 'Action parameter is required'
        })
      ).setMimeType(ContentService.MimeType.JSON);
    }

    let result;

    switch (action) {
      case 'sync_attendance':
        result = handleAttendanceSync({ data: data });
        break;
      case 'sync_monthly_grid':
        result = handleMonthlyGridSync({ data: data });
        break;
      case 'sync_daily_grid':
        result = handleDailyGridSync(data);
        break;
      case 'get_data':
        result = handleGetData({ data: data });
        break;
      case 'clear_data':
        result = handleClearData({ data: data });
        break;
      default:
        result = {
          status: 'error',
          message: `Invalid POST action: ${action}`
        };
    }

    console.log('=== POST Request Completed Successfully ===');
    return ContentService
      .createTextOutput(JSON.stringify(result))
      .setMimeType(ContentService.MimeType.JSON)
      .setHeaders({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      });

  } catch (error) {
    console.error('=== Error in doPost ===');
    console.error('Error:', error.toString());

    const errorResult = {
      status: 'error',
      message: error.toString(),
      timestamp: new Date().toISOString()
    };

    return ContentService.createTextOutput(
      JSON.stringify(errorResult)
    ).setMimeType(ContentService.MimeType.JSON)
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    });
  }
}

/**
 * Test function to validate the script setup
 * Can be run from the Apps Script editor to test functionality
 */
function testScript() {
  console.log('Testing Enhanced Google Apps Script for Attendance Sync...');
  
  const testData = [
    {
      employeeId: 'TEST001',
      employeeName: 'Test Employee',
      date: '2025-01-15',
      dayOfWeek: 'Wednesday',
      shift: 'Morning',
      checkIn: '08:00:00',
      checkOut: '17:00:00',
      regularHours: 8,
      overtimeHours: 1,
      totalHours: 9
    }
  ];

  const mockParams = {
    action: 'sync_attendance',
    data: JSON.stringify(testData)
  };

  const result = handleAttendanceSync(mockParams);
  console.log('Test result:', JSON.stringify(result, null, 2));
  
  return 'Enhanced test completed. Check logs for results.';
} 