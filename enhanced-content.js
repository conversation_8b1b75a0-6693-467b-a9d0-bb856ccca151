// Venus-Millware AutoFill - Enhanced Content Script with Visual Feedback
// Developer: Atha Rizki Pangestu - <PERSON> Rebinmas (Delloyd Group)

class EnhancedAutomationContent {
    constructor() {
        this.isExecuting = false;
        this.isPaused = false;
        this.currentExecution = null;
        this.flowDefinition = null;
        this.currentStepIndex = 0;
        this.executionResults = {
            success: false,
            stepsExecuted: 0,
            errors: [],
            startTime: null,
            endTime: null
        };
        
        // Visual feedback system
        this.highlightedElements = new Set();
        this.overlayContainer = null;
        this.statusOverlay = null;
        
        this.init();
    }

    init() {
        console.log('🎯 Enhanced Venus-Millware AutoFill Content Script initialized');
        this.setupMessageListener();
        this.createVisualOverlay();
        this.injectStyles();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep the message channel open for async response
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'executeMillwareFlow':
                    await this.executeMillwareFlow(message, sendResponse);
                    break;

                case 'pauseAutomation':
                    this.pauseAutomation();
                    sendResponse({ success: true });
                    break;

                case 'stopAutomation':
                    this.stopAutomation();
                    sendResponse({ success: true });
                    break;

                case 'highlightElement':
                    await this.highlightElement(message.selector, message.style, sendResponse);
                    break;

                case 'updateFlowStatus':
                    this.updateFlowStatus(message.stepIndex, message.status);
                    sendResponse({ success: true });
                    break;

                default:
                    console.warn('Unknown automation action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling automation message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeMillwareFlow(message, sendResponse) {
        if (this.isExecuting) {
            sendResponse({ success: false, error: 'Automation already running' });
            return;
        }

        console.log('🚀 Starting Millware automation flow execution');
        
        this.isExecuting = true;
        this.isPaused = false;
        this.flowDefinition = message.flowDefinition;
        this.currentStepIndex = 0;
        
        this.executionResults = {
            success: false,
            stepsExecuted: 0,
            errors: [],
            startTime: new Date().toISOString(),
            endTime: null,
            executionId: message.metadata?.executionId || this.generateExecutionId()
        };

        try {
            // Show status overlay
            this.showStatusOverlay('Memulai otomatisasi Millware...');
            
            // Send initial response
            sendResponse({ success: true, message: 'Millware automation flow started' });

            // Execute the flow
            await this.runMillwareFlowSequence();

            // Mark as successful if we got here
            this.executionResults.success = true;
            this.executionResults.endTime = new Date().toISOString();

            this.showStatusOverlay('✅ Otomatisasi selesai!', 'success');
            this.notifyCompletion(true, null, this.executionResults);

        } catch (error) {
            console.error('❌ Millware automation flow execution failed:', error);
            this.executionResults.success = false;
            this.executionResults.endTime = new Date().toISOString();
            this.executionResults.errors.push({
                message: error.message,
                timestamp: new Date().toISOString(),
                stack: error.stack
            });
            
            this.showStatusOverlay(`❌ Otomatisasi gagal: ${error.message}`, 'error');
            this.notifyCompletion(false, error.message, this.executionResults);
        } finally {
            this.isExecuting = false;
            this.clearAllHighlights();
            setTimeout(() => this.hideStatusOverlay(), 3000);
        }
    }

    async runMillwareFlowSequence() {
        if (!this.flowDefinition || !this.flowDefinition.flow_steps) {
            throw new Error('Invalid flow definition');
        }

        const steps = this.flowDefinition.flow_steps;
        console.log(`📋 Executing ${steps.length} Millware automation steps`);
        
        for (let i = 0; i < steps.length; i++) {
            if (!this.isExecuting || this.isPaused) {
                console.log('⏸️ Automation paused or stopped');
                break;
            }

            const step = steps[i];
            this.currentStepIndex = i;
            
            console.log(`🎯 Executing step ${i + 1}/${steps.length}: ${step.name} (${step.type})`);

            try {
                // Update visual feedback
                this.notifyStepExecution(i, 'executing', step.name);
                this.showStatusOverlay(`${step.visual_node.title} (${i + 1}/${steps.length})`);

                // Execute the step
                await this.executeMillwareStep(step, i);
                this.executionResults.stepsExecuted++;

                // Update progress
                const progress = Math.round(((i + 1) / steps.length) * 100);
                this.notifyStepExecution(i, 'completed', step.name);
                this.notifyProgress(progress, `Completed: ${step.name}`);

                // Add delay between steps
                await this.delay(1000);

            } catch (error) {
                console.error(`❌ Step ${i + 1} failed:`, error);
                this.executionResults.errors.push({
                    stepIndex: i,
                    stepType: step.type,
                    stepName: step.name,
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
                
                this.notifyStepExecution(i, 'error', step.name);
                
                // Decide whether to continue or stop based on error type
                if (this.isCriticalError(error) || !step.error_handling?.continue_on_error) {
                    throw error;
                }
                
                // Continue with next step for non-critical errors
                console.log('⚠️ Non-critical error, continuing with next step');
            }
        }
    }

    async executeMillwareStep(step, index) {
        console.log(`🔧 Executing ${step.type} step:`, step);

        // Evaluate conditional logic if present
        if (step.condition) {
            const conditionResult = await this.evaluateStepCondition(step.condition);
            if (!conditionResult) {
                console.log(`⏭️ Skipping step ${index + 1} - condition not met`);
                this.notifyStepExecution(index, 'skipped', step.name);
                return;
            }
        }

        // Execute the step based on type
        switch (step.type) {
            case 'navigate':
                await this.executeNavigateStep(step);
                break;
                
            case 'wait':
                await this.executeWaitStep(step);
                break;
                
            case 'input':
                await this.executeInputStep(step);
                break;
                
            case 'click':
                await this.executeClickStep(step);
                break;
                
            case 'conditional_action':
                await this.executeConditionalActionStep(step);
                break;
                
            default:
                throw new Error(`Unknown step type: ${step.type}`);
        }
    }

    async executeNavigateStep(step) {
        const url = step.parameters.url;
        console.log(`🌐 Navigating to: ${url}`);
        
        // Highlight the entire page briefly
        this.highlightPage('navigate');
        
        window.location.href = url;
        
        if (step.parameters.wait_for_load) {
            await this.waitForNavigation(step.parameters.timeout || 30000);
        }
        
        console.log(`✅ Navigation completed`);
    }

    async executeWaitStep(step) {
        const duration = step.parameters.duration || 1000;
        const waitForElement = step.parameters.wait_for_element;
        
        if (waitForElement) {
            console.log(`⏳ Waiting for element: ${waitForElement}`);
            const element = await this.waitForElement(waitForElement, duration);
            
            if (element && step.parameters.element_visible) {
                // Highlight the element when found
                this.highlightElementWithFeedback(element, 'found');
            }
        } else {
            console.log(`⏳ Waiting ${duration}ms`);
            await this.delay(duration);
        }

        console.log(`✅ Wait completed`);
    }

    async executeInputStep(step) {
        const selector = step.parameters.selector;
        const alternatives = step.parameters.selector_alternatives || [];
        const value = step.parameters.value;
        
        let element = await this.findElementWithAlternatives(selector, alternatives);
        
        if (!element) {
            throw new Error(`Input target not found: ${selector}`);
        }

        // Highlight the element
        this.highlightElementWithFeedback(element, 'input');
        
        // Scroll to element
        this.scrollElementIntoView(element);
        await this.delay(300);

        // Focus the element
        element.focus();
        await this.delay(100);

        // Clear field if requested
        if (step.parameters.clear_first !== false) {
            element.value = '';
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(100);
        }

        // Simulate typing if requested
        if (step.parameters.simulate_typing) {
            await this.simulateTyping(element, value, step.parameters.typing_delay || 100);
        } else {
            element.value = value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
        }

        console.log(`✅ Input value "${value}" into: ${selector}`);
    }

    async executeClickStep(step) {
        const selector = step.parameters.selector;
        const alternatives = step.parameters.selector_alternatives || [];
        
        let element = await this.findElementWithAlternatives(selector, alternatives);
        
        if (!element) {
            throw new Error(`Click target not found: ${selector}`);
        }

        // Highlight the element
        this.highlightElementWithFeedback(element, 'click');
        
        // Scroll to element
        this.scrollElementIntoView(element);
        await this.delay(300);

        // Simulate click with animation
        await this.simulateClick(element, step.visual_feedback?.click_animation);

        // Wait after click if specified
        if (step.parameters.wait_after_click) {
            await this.delay(step.parameters.wait_after_click);
        }

        console.log(`✅ Clicked element: ${selector}`);
    }

    async executeConditionalActionStep(step) {
        const condition = step.condition;
        const timeout = condition.timeout || 5000;
        
        console.log(`🔍 Evaluating conditional action: ${condition.type}`);
        
        let conditionMet = false;
        const startTime = Date.now();
        
        // Wait for condition with timeout
        while (Date.now() - startTime < timeout) {
            conditionMet = await this.evaluateStepCondition(condition);
            if (conditionMet) break;
            await this.delay(500);
        }
        
        if (conditionMet && step.true_action) {
            console.log(`✅ Condition met, executing true action`);
            await this.executeAction(step.true_action);
        } else if (!conditionMet && step.false_action) {
            console.log(`❌ Condition not met, executing false action`);
            await this.executeAction(step.false_action);
        }
    }

    async executeAction(action) {
        switch (action.type) {
            case 'click':
                const element = await this.findElementWithAlternatives(
                    action.selector, 
                    action.selector_alternatives || []
                );
                if (element) {
                    this.highlightElementWithFeedback(element, 'click');
                    await this.simulateClick(element);
                    if (action.wait_after_click) {
                        await this.delay(action.wait_after_click);
                    }
                }
                break;
                
            case 'log':
                console.log(action.message);
                break;
                
            default:
                console.warn(`Unknown action type: ${action.type}`);
        }
    }

    async findElementWithAlternatives(selector, alternatives = []) {
        // Try main selector first
        let element = await this.findElement(selector);
        if (element) return element;
        
        // Try alternatives
        for (const altSelector of alternatives) {
            element = await this.findElement(altSelector);
            if (element) {
                console.log(`Found element with alternative selector: ${altSelector}`);
                return element;
            }
        }
        
        return null;
    }

    async findElement(selector, timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element && this.isElementVisible(element)) {
                return element;
            }
            await this.delay(100);
        }
        
        return null;
    }

    isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return rect.width > 0 && 
               rect.height > 0 && 
               style.visibility !== 'hidden' && 
               style.display !== 'none' &&
               style.opacity !== '0';
    }

    scrollElementIntoView(element) {
        if (element && typeof element.scrollIntoView === 'function') {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        }
    }

    async simulateTyping(element, text, delay = 100) {
        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(delay);
        }
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    async simulateClick(element, withAnimation = true) {
        if (withAnimation) {
            // Add click animation
            element.style.transform = 'scale(0.95)';
            element.style.transition = 'transform 0.1s ease';
            await this.delay(100);
            element.style.transform = 'scale(1)';
            await this.delay(100);
        }
        
        // Fire click events
        element.focus();
        element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    }

    highlightElementWithFeedback(element, type = 'default') {
        if (!element) return;
        
        // Remove any existing highlights on this element
        this.removeElementHighlight(element);
        
        // Create highlight overlay
        const rect = element.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        const highlight = document.createElement('div');
        highlight.className = `venus-highlight venus-highlight-${type}`;
        highlight.style.cssText = `
            position: absolute;
            top: ${rect.top + scrollTop - 3}px;
            left: ${rect.left + scrollLeft - 3}px;
            width: ${rect.width + 6}px;
            height: ${rect.height + 6}px;
            border: 3px solid #ff4444;
            border-radius: 4px;
            background: rgba(255, 68, 68, 0.1);
            box-shadow: 0 0 15px rgba(255, 68, 68, 0.5);
            pointer-events: none;
            z-index: 999999;
            animation: venus-pulse 1.5s infinite;
        `;
        
        // Add type-specific styles
        switch (type) {
            case 'input':
                highlight.style.borderColor = '#3b82f6';
                highlight.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
                highlight.style.boxShadow = '0 0 15px rgba(59, 130, 246, 0.5)';
                break;
            case 'click':
                highlight.style.borderColor = '#22c55e';
                highlight.style.backgroundColor = 'rgba(34, 197, 94, 0.1)';
                highlight.style.boxShadow = '0 0 15px rgba(34, 197, 94, 0.5)';
                break;
            case 'found':
                highlight.style.borderColor = '#ffa500';
                highlight.style.backgroundColor = 'rgba(255, 165, 0, 0.1)';
                highlight.style.boxShadow = '0 0 15px rgba(255, 165, 0, 0.5)';
                break;
        }
        
        this.overlayContainer.appendChild(highlight);
        this.highlightedElements.add(highlight);
        
        // Store reference to element for cleanup
        highlight._targetElement = element;
        
        // Auto-remove after duration
        setTimeout(() => {
            this.removeElementHighlight(element);
        }, 3000);
    }

    highlightPage(type = 'default') {
        const pageHighlight = document.createElement('div');
        pageHighlight.className = `venus-page-highlight venus-page-highlight-${type}`;
        pageHighlight.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            border: 5px solid #3b82f6;
            pointer-events: none;
            z-index: 999998;
            animation: venus-page-pulse 2s ease-out;
        `;
        
        this.overlayContainer.appendChild(pageHighlight);
        
        setTimeout(() => {
            if (pageHighlight.parentNode) {
                pageHighlight.parentNode.removeChild(pageHighlight);
            }
        }, 2000);
    }

    removeElementHighlight(element) {
        this.highlightedElements.forEach(highlight => {
            if (highlight._targetElement === element) {
                if (highlight.parentNode) {
                    highlight.parentNode.removeChild(highlight);
                }
                this.highlightedElements.delete(highlight);
            }
        });
    }

    clearAllHighlights() {
        this.highlightedElements.forEach(highlight => {
            if (highlight.parentNode) {
                highlight.parentNode.removeChild(highlight);
            }
        });
        this.highlightedElements.clear();
    }

    createVisualOverlay() {
        // Create main overlay container
        this.overlayContainer = document.createElement('div');
        this.overlayContainer.id = 'venus-overlay-container';
        this.overlayContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999997;
        `;
        
        // Create status overlay
        this.statusOverlay = document.createElement('div');
        this.statusOverlay.id = 'venus-status-overlay';
        this.statusOverlay.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        document.body.appendChild(this.overlayContainer);
        document.body.appendChild(this.statusOverlay);
    }

    showStatusOverlay(message, type = 'info') {
        this.statusOverlay.textContent = message;
        this.statusOverlay.style.transform = 'translateX(0)';
        
        // Update colors based on type
        switch (type) {
            case 'success':
                this.statusOverlay.style.background = 'rgba(34, 197, 94, 0.9)';
                this.statusOverlay.style.borderColor = 'rgba(34, 197, 94, 0.5)';
                break;
            case 'error':
                this.statusOverlay.style.background = 'rgba(239, 68, 68, 0.9)';
                this.statusOverlay.style.borderColor = 'rgba(239, 68, 68, 0.5)';
                break;
            case 'warning':
                this.statusOverlay.style.background = 'rgba(255, 165, 0, 0.9)';
                this.statusOverlay.style.borderColor = 'rgba(255, 165, 0, 0.5)';
                break;
            default:
                this.statusOverlay.style.background = 'rgba(59, 130, 246, 0.9)';
                this.statusOverlay.style.borderColor = 'rgba(59, 130, 246, 0.5)';
        }
    }

    hideStatusOverlay() {
        this.statusOverlay.style.transform = 'translateX(100%)';
    }

    injectStyles() {
        const styles = document.createElement('style');
        styles.textContent = `
            @keyframes venus-pulse {
                0% { opacity: 0.8; transform: scale(1); }
                50% { opacity: 1; transform: scale(1.02); }
                100% { opacity: 0.8; transform: scale(1); }
            }
            
            @keyframes venus-page-pulse {
                0% { opacity: 0; border-width: 0px; }
                50% { opacity: 0.8; border-width: 5px; }
                100% { opacity: 0; border-width: 0px; }
            }
            
            .venus-highlight {
                animation: venus-pulse 1.5s infinite;
            }
            
            .venus-page-highlight {
                animation: venus-page-pulse 2s ease-out;
            }
        `;
        document.head.appendChild(styles);
    }

    async evaluateStepCondition(condition) {
        try {
            switch (condition.type) {
                case 'element_exists':
                    const element = document.querySelector(condition.selector);
                    return Boolean(element && (condition.visible !== true || this.isElementVisible(element)));

                case 'url_contains':
                    return window.location.href.includes(condition.value);

                case 'page_title_contains':
                    return document.title.includes(condition.value);

                default:
                    console.warn(`Unknown condition type: ${condition.type}`);
                    return false;
            }
        } catch (error) {
            console.error('Error evaluating condition:', error);
            return false;
        }
    }

    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element && this.isElementVisible(element)) {
                return element;
            }
            await this.delay(100);
        }
        
        throw new Error(`Element not found within ${timeout}ms: ${selector}`);
    }

    async waitForNavigation(timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkReady = () => {
                if (document.readyState === 'complete' || Date.now() - startTime > timeout) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            
            checkReady();
        });
    }

    isCriticalError(error) {
        const criticalPatterns = [
            'Navigation failed',
            'Page not loaded',
            'Authentication required',
            'Login failed'
        ];
        
        return criticalPatterns.some(pattern => error.message.includes(pattern));
    }

    pauseAutomation() {
        this.isPaused = true;
        this.showStatusOverlay('⏸️ Otomatisasi dijeda', 'warning');
        console.log('⏸️ Automation paused');
    }

    stopAutomation() {
        this.isExecuting = false;
        this.isPaused = false;
        this.clearAllHighlights();
        this.showStatusOverlay('⏹️ Otomatisasi dihentikan', 'warning');
        console.log('⏹️ Automation stopped');
    }

    generateExecutionId() {
        return 'millware_exec_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    notifyProgress(progress, step) {
        chrome.runtime.sendMessage({
            action: 'executionProgress',
            progress: progress,
            step: step
        });
    }

    notifyCompletion(success, error = null, results = null) {
        chrome.runtime.sendMessage({
            action: 'executionComplete',
            success: success,
            error: error,
            results: results
        });
    }

    notifyStepExecution(stepIndex, status, stepName) {
        chrome.runtime.sendMessage({
            action: 'stepStatusUpdate',
            stepIndex: stepIndex,
            status: status,
            stepName: stepName,
            timestamp: new Date().toISOString()
        });
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the enhanced automation content script
const enhancedAutomationContent = new EnhancedAutomationContent(); 