// Venus-Millware AutoFill - Visual Flow Management
// Developer: <PERSON><PERSON> Riz<PERSON> - IT Rebinmas (Delloyd Group)

class MillwareVisualFlow {
    constructor() {
        this.flowDefinition = null;
        this.currentStepIndex = 0;
        this.isExecuting = false;
        this.isPaused = false;
        this.executionResults = {};
        
        this.init();
    }

    init() {
        console.log('🎨 Millware Visual Flow initialized');
        this.loadFlowDefinition();
        this.setupEventListeners();
        this.renderFlowSteps();
    }

    setupEventListeners() {
        // Tab navigation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-button')) {
                this.switchTab(e.target.dataset.tab);
            }
        });

        // Millware automation controls
        document.getElementById('runMillwareAutomation')?.addEventListener('click', () => {
            this.executeMillwareFlow();
        });

        document.getElementById('showVisualFlow')?.addEventListener('click', () => {
            this.switchTab('flow');
        });

        // Flow controls
        document.getElementById('startFlowBtn')?.addEventListener('click', () => {
            this.executeMillwareFlow();
        });

        document.getElementById('pauseFlowBtn')?.addEventListener('click', () => {
            this.pauseFlow();
        });

        document.getElementById('stopFlowBtn')?.addEventListener('click', () => {
            this.stopFlow();
        });

        // Data management
        document.getElementById('loadFlowDefinition')?.addEventListener('click', () => {
            this.loadFlowDefinition();
        });

        document.getElementById('exportFlowDefinition')?.addEventListener('click', () => {
            this.exportFlowDefinition();
        });

        document.getElementById('validateFlow')?.addEventListener('click', () => {
            this.validateFlowDefinition();
        });

        // Configuration
        document.getElementById('saveConfig')?.addEventListener('click', () => {
            this.saveConfiguration();
        });

        // Execution controls
        document.getElementById('runExecution')?.addEventListener('click', () => {
            this.executeMillwareFlow();
        });

        document.getElementById('pauseExecution')?.addEventListener('click', () => {
            this.pauseFlow();
        });

        document.getElementById('stopExecution')?.addEventListener('click', () => {
            this.stopFlow();
        });

        // Listen for messages from content script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleRuntimeMessage(message, sender, sendResponse);
        });
    }

    switchTab(tabName) {
        // Remove active class from all tabs and contents
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabName}"]`)?.classList.add('active');
        document.getElementById(tabName)?.classList.add('active');

        // If switching to flow tab, ensure flow is rendered
        if (tabName === 'flow') {
            this.renderFlowSteps();
        }

        // If switching to data tab, refresh JSON display
        if (tabName === 'data') {
            this.displayFlowDefinition();
        }
    }

    async loadFlowDefinition() {
        try {
            // First try to load from storage
            const stored = await chrome.storage.local.get(['millwareFlowDefinition']);
            if (stored.millwareFlowDefinition) {
                this.flowDefinition = stored.millwareFlowDefinition;
                console.log('✅ Loaded flow definition from storage');
            } else {
                // Load default flow definition
                this.flowDefinition = this.getDefaultFlowDefinition();
                console.log('📋 Loaded default flow definition');
            }

            this.renderFlowSteps();
            this.displayFlowDefinition();
            
        } catch (error) {
            console.error('❌ Error loading flow definition:', error);
            this.flowDefinition = this.getDefaultFlowDefinition();
        }
    }

    getDefaultFlowDefinition() {
        return {
            "flow_id": "millware_login_automation_v1",
            "name": "Millware Login & Task Register Automation",
            "description": "Automated login sequence untuk Millware system dengan navigasi ke Task Register",
            "version": "1.0.0",
            "author": "Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)",
            "created_date": "2024-01-20",
            "target_system": {
                "name": "Millware ERP System",
                "base_url": "http://millwarep3.rebinmas.com:8003/",
                "authentication_required": true
            },
            "flow_metadata": {
                "estimated_duration": "15-30 seconds",
                "complexity": "medium",
                "success_rate": "high",
                "dependencies": ["active_internet", "target_server_accessible"]
            },
            "flow_steps": [
                {
                    "step_id": 1,
                    "name": "navigate_to_login",
                    "type": "navigate",
                    "description": "Buka halaman login Millware system",
                    "parameters": {
                        "url": "http://millwarep3.rebinmas.com:8003/",
                        "wait_for_load": true,
                        "timeout": 30000
                    },
                    "visual_node": {
                        "title": "🌐 Buka Halaman Login",
                        "subtitle": "Navigasi ke Millware system",
                        "icon": "🌐",
                        "color": "#3b82f6"
                    },
                    "visual_feedback": {
                        "show_page_highlight": true,
                        "highlight_duration": 2000
                    },
                    "error_handling": {
                        "continue_on_error": false,
                        "retry_attempts": 2,
                        "retry_delay": 5000
                    }
                },
                {
                    "step_id": 2,
                    "name": "wait_for_page_load",
                    "type": "wait",
                    "description": "Tunggu halaman login dimuat lengkap",
                    "parameters": {
                        "duration": 3000,
                        "wait_for_element": "input[name='txtUsername']",
                        "element_visible": true
                    },
                    "visual_node": {
                        "title": "⏳ Tunggu Halaman Dimuat",
                        "subtitle": "Memastikan elemen login tersedia",
                        "icon": "⏳",
                        "color": "#f59e0b"
                    },
                    "error_handling": {
                        "continue_on_error": false,
                        "timeout": 10000
                    }
                },
                {
                    "step_id": 3,
                    "name": "input_username",
                    "type": "input",
                    "description": "Masukkan username ke field login",
                    "parameters": {
                        "selector": "input[name='txtUsername']",
                        "selector_alternatives": [
                            "#txtUsername",
                            "input[type='text'][name='txtUsername']"
                        ],
                        "value": "{{username}}",
                        "clear_first": true,
                        "simulate_typing": true,
                        "typing_delay": 100
                    },
                    "visual_node": {
                        "title": "👤 Input Username",
                        "subtitle": "Isi field username",
                        "icon": "👤",
                        "color": "#10b981"
                    },
                    "visual_feedback": {
                        "highlight_element": true,
                        "highlight_color": "#3b82f6",
                        "highlight_duration": 2000
                    },
                    "error_handling": {
                        "continue_on_error": false,
                        "retry_attempts": 3
                    }
                },
                {
                    "step_id": 4,
                    "name": "input_password",
                    "type": "input",
                    "description": "Masukkan password ke field login",
                    "parameters": {
                        "selector": "input[name='txtPassword']",
                        "selector_alternatives": [
                            "#txtPassword",
                            "input[type='password'][name='txtPassword']"
                        ],
                        "value": "{{password}}",
                        "clear_first": true,
                        "simulate_typing": true,
                        "typing_delay": 100
                    },
                    "visual_node": {
                        "title": "🔐 Input Password",
                        "subtitle": "Isi field password",
                        "icon": "🔐",
                        "color": "#10b981"
                    },
                    "visual_feedback": {
                        "highlight_element": true,
                        "highlight_color": "#3b82f6",
                        "highlight_duration": 2000
                    },
                    "error_handling": {
                        "continue_on_error": false,
                        "retry_attempts": 3
                    }
                },
                {
                    "step_id": 5,
                    "name": "click_login_button",
                    "type": "click",
                    "description": "Klik tombol login untuk masuk sistem",
                    "parameters": {
                        "selector": "input[name='btnLogin']",
                        "selector_alternatives": [
                            "#btnLogin",
                            "input[type='submit'][value='LOG IN']"
                        ],
                        "wait_after_click": 2000
                    },
                    "visual_node": {
                        "title": "🚀 Klik Login",
                        "subtitle": "Proses autentikasi",
                        "icon": "🚀",
                        "color": "#ef4444"
                    },
                    "visual_feedback": {
                        "highlight_element": true,
                        "highlight_color": "#22c55e",
                        "click_animation": true,
                        "highlight_duration": 2000
                    },
                    "error_handling": {
                        "continue_on_error": false,
                        "retry_attempts": 2
                    }
                },
                {
                    "step_id": 6,
                    "name": "handle_popup_if_exists",
                    "type": "conditional_action",
                    "description": "Handle popup login jika muncul",
                    "condition": {
                        "type": "element_exists",
                        "selector": ".PopupBoxLogin",
                        "timeout": 5000,
                        "visible": true
                    },
                    "true_action": {
                        "type": "click",
                        "selector": "input[name='ctl00$MainContent$btnOkay']",
                        "selector_alternatives": [
                            "#MainContent_btnOkay",
                            "input[value='ok']"
                        ],
                        "wait_after_click": 2000
                    },
                    "false_action": {
                        "type": "log",
                        "message": "No popup detected, proceeding"
                    },
                    "visual_node": {
                        "title": "🔔 Handle Popup",
                        "subtitle": "Tutup popup jika ada",
                        "icon": "🔔",
                        "color": "#f59e0b"
                    },
                    "error_handling": {
                        "continue_on_error": true
                    }
                },
                {
                    "step_id": 7,
                    "name": "wait_after_login",
                    "type": "wait",
                    "description": "Tunggu setelah login selesai",
                    "parameters": {
                        "duration": 3000
                    },
                    "visual_node": {
                        "title": "⏳ Tunggu Login Selesai",
                        "subtitle": "Memastikan halaman ter-load",
                        "icon": "⏳",
                        "color": "#6366f1"
                    },
                    "error_handling": {
                        "continue_on_error": true
                    }
                },
                {
                    "step_id": 8,
                    "name": "navigate_to_task_register",
                    "type": "navigate",
                    "description": "Navigasi ke halaman Task Register",
                    "parameters": {
                        "url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx",
                        "wait_for_load": true,
                        "timeout": 30000
                    },
                    "visual_node": {
                        "title": "📋 Buka Task Register",
                        "subtitle": "Navigasi ke halaman target",
                        "icon": "📋",
                        "color": "#8b5cf6"
                    },
                    "visual_feedback": {
                        "show_page_highlight": true,
                        "highlight_duration": 2000
                    },
                    "error_handling": {
                        "continue_on_error": false,
                        "retry_attempts": 2
                    }
                },
                {
                    "step_id": 9,
                    "name": "click_new_button",
                    "type": "click",
                    "description": "Klik tombol New di Task Register",
                    "parameters": {
                        "selector": "input[name='ctl00$MainContent$btnNew']",
                        "selector_alternatives": [
                            "#MainContent_btnNew",
                            "input[value='New']"
                        ],
                        "wait_after_click": 2000
                    },
                    "visual_node": {
                        "title": "➕ Klik New",
                        "subtitle": "Buat task register baru",
                        "icon": "➕",
                        "color": "#10b981"
                    },
                    "visual_feedback": {
                        "highlight_element": true,
                        "highlight_color": "#22c55e",
                        "click_animation": true,
                        "highlight_duration": 3000
                    },
                    "error_handling": {
                        "continue_on_error": false,
                        "retry_attempts": 3
                    }
                }
            ]
        };
    }

    renderFlowSteps() {
        const container = document.getElementById('flowStepsContainer');
        if (!container || !this.flowDefinition) return;

        container.innerHTML = '';

        this.flowDefinition.flow_steps.forEach((step, index) => {
            const stepElement = this.createFlowStepElement(step, index);
            container.appendChild(stepElement);

            // Add connector between steps (except for last step)
            if (index < this.flowDefinition.flow_steps.length - 1) {
                const connector = document.createElement('div');
                connector.className = 'mini-step-connector';
                connector.id = `connector-${index}`;
                container.appendChild(connector);
            }
        });

        // Update progress display
        this.updateProgressDisplay();
    }

    createFlowStepElement(step, index) {
        const stepElement = document.createElement('div');
        stepElement.className = 'mini-flow-step pending';
        stepElement.id = `flow-step-${index}`;
        stepElement.dataset.stepIndex = index;

        stepElement.innerHTML = `
            <div class="mini-step-header">
                <div class="mini-step-icon">${step.visual_node.icon}</div>
                <div class="mini-step-info">
                    <h4 class="mini-step-title">${step.visual_node.title}</h4>
                    <p class="mini-step-subtitle">${step.visual_node.subtitle}</p>
                </div>
                <div class="mini-step-status pending" id="status-${index}"></div>
            </div>
        `;

        // Add click handler for step details
        stepElement.addEventListener('click', () => {
            this.showStepDetails(step, index);
        });

        return stepElement;
    }

    showStepDetails(step, index) {
        const details = {
            stepNumber: index + 1,
            name: step.name,
            type: step.type,
            description: step.description,
            parameters: step.parameters,
            visualFeedback: step.visual_feedback
        };

        console.log('📋 Step Details:', details);
        
        // You could show a modal or update a side panel here
        // For now, we'll just log the details
    }

    async executeMillwareFlow() {
        if (this.isExecuting) {
            console.log('⚠️ Flow execution already in progress');
            return;
        }

        console.log('🚀 Starting Millware automation flow');

        try {
            // Get current configuration
            const config = await this.getCurrentConfiguration();
            
            // Prepare flow definition with user values
            const preparedFlow = this.prepareFlowWithConfig(config);

            // Update UI state
            this.isExecuting = true;
            this.updateFlowStatus('Memulai otomatisasi...');
            this.resetFlowSteps();

            // Get active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab) {
                throw new Error('No active tab found');
            }

            // Inject enhanced content script if needed
            await this.ensureEnhancedContentScript(tab.id);

            // Send execution message to content script
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'executeMillwareFlow',
                flowDefinition: preparedFlow,
                metadata: {
                    executionId: this.generateExecutionId(),
                    startTime: new Date().toISOString(),
                    config: config
                }
            });

            if (response && response.success) {
                console.log('✅ Flow execution started successfully');
                this.updateFlowStatus('Otomatisasi berjalan...');
            } else {
                throw new Error(response?.error || 'Failed to start execution');
            }

        } catch (error) {
            console.error('❌ Failed to execute Millware flow:', error);
            this.handleExecutionError(error);
        }
    }

    async getCurrentConfiguration() {
        const targetUrl = document.getElementById('targetUrl')?.value || 'http://millwarep3.rebinmas.com:8003/';
        const username = document.getElementById('username')?.value || 'adm075';
        const password = document.getElementById('password')?.value || 'adm075';
        const stepDelay = parseInt(document.getElementById('stepDelay')?.value) || 1000;
        const enableVisualFeedback = document.getElementById('enableVisualFeedback')?.checked || true;
        const enableScreenshots = document.getElementById('enableScreenshots')?.checked || false;

        return {
            targetUrl,
            username,
            password,
            stepDelay,
            enableVisualFeedback,
            enableScreenshots
        };
    }

    prepareFlowWithConfig(config) {
        const flowCopy = JSON.parse(JSON.stringify(this.flowDefinition));
        
        // Replace placeholders with actual values
        flowCopy.flow_steps.forEach(step => {
            if (step.parameters) {
                if (step.parameters.value) {
                    step.parameters.value = step.parameters.value
                        .replace('{{username}}', config.username)
                        .replace('{{password}}', config.password);
                }
                if (step.parameters.url) {
                    step.parameters.url = step.parameters.url
                        .replace('{{targetUrl}}', config.targetUrl);
                }
            }
        });

        return flowCopy;
    }

    async ensureEnhancedContentScript(tabId) {
        try {
            // Try to ping the enhanced content script
            const response = await chrome.tabs.sendMessage(tabId, { action: 'ping' });
            if (response && response.enhanced) {
                return; // Enhanced script already loaded
            }
        } catch (error) {
            // Content script not loaded, inject it
        }

        // Inject the enhanced content script
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['enhanced-content.js']
        });

        console.log('📥 Enhanced content script injected');
    }

    async pauseFlow() {
        if (!this.isExecuting) return;

        this.isPaused = true;
        this.updateFlowStatus('Otomatisasi dijeda');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                await chrome.tabs.sendMessage(tab.id, { action: 'pauseAutomation' });
            }
        } catch (error) {
            console.error('Error pausing flow:', error);
        }
    }

    async stopFlow() {
        this.isExecuting = false;
        this.isPaused = false;
        this.updateFlowStatus('Otomatisasi dihentikan');
        this.resetFlowSteps();

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                await chrome.tabs.sendMessage(tab.id, { action: 'stopAutomation' });
            }
        } catch (error) {
            console.error('Error stopping flow:', error);
        }
    }

    handleRuntimeMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'stepStatusUpdate':
                this.updateStepStatus(message.stepIndex, message.status, message.stepName);
                break;

            case 'executionProgress':
                this.updateProgress(message.progress, message.step);
                break;

            case 'executionComplete':
                this.handleExecutionComplete(message.success, message.error, message.results);
                break;

            default:
                console.log('Unknown runtime message:', message);
        }
    }

    updateStepStatus(stepIndex, status, stepName) {
        // Update mini flow step
        const stepElement = document.getElementById(`flow-step-${stepIndex}`);
        const statusElement = document.getElementById(`status-${stepIndex}`);
        const connectorElement = document.getElementById(`connector-${stepIndex}`);

        if (stepElement && statusElement) {
            // Remove all status classes
            stepElement.className = 'mini-flow-step';
            statusElement.className = 'mini-step-status';

            // Add new status class
            stepElement.classList.add(status);
            statusElement.classList.add(status);

            // Update connector
            if (connectorElement && status === 'completed') {
                connectorElement.classList.add('completed');
            }
        }

        // Update current step tracking
        if (status === 'executing') {
            this.currentStepIndex = stepIndex;
        }

        // Log to execution tab
        this.logExecution(`${stepName || `Step ${stepIndex + 1}`}: ${this.capitalizeFirst(status)}`, 
                         status === 'error' ? 'error' : 'info');
    }

    updateProgress(progress, step) {
        // Update progress bars
        const flowProgressFill = document.getElementById('flowProgressFill');
        const progressFill = document.getElementById('progressFill');
        const flowProgressText = document.getElementById('flowProgressText');
        const executionStatusText = document.getElementById('executionStatusText');

        if (flowProgressFill) {
            flowProgressFill.style.width = `${progress}%`;
        }

        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }

        if (flowProgressText) {
            const totalSteps = this.flowDefinition?.flow_steps?.length || 9;
            const completedSteps = Math.floor((progress / 100) * totalSteps);
            flowProgressText.textContent = `${completedSteps}/${totalSteps}`;
        }

        if (executionStatusText) {
            executionStatusText.textContent = step || `Progress: ${progress}%`;
        }
    }

    updateFlowStatus(status) {
        const flowStatus = document.getElementById('flowStatus');
        const statusText = document.querySelector('.status-text');

        if (flowStatus) {
            flowStatus.textContent = status;
        }

        if (statusText) {
            statusText.textContent = status;
        }
    }

    handleExecutionComplete(success, error, results) {
        this.isExecuting = false;
        this.isPaused = false;

        if (success) {
            this.updateFlowStatus('✅ Otomatisasi selesai!');
            this.updateProgress(100, 'Selesai dengan sukses');
            this.logExecution('✅ Otomatisasi Millware selesai dengan sukses!', 'success');
        } else {
            this.updateFlowStatus(`❌ Otomatisasi gagal: ${error}`);
            this.logExecution(`❌ Otomatisasi gagal: ${error}`, 'error');
        }

        // Store results
        this.executionResults = results || {};
        
        console.log('📊 Execution Results:', this.executionResults);
    }

    handleExecutionError(error) {
        this.isExecuting = false;
        this.isPaused = false;
        this.updateFlowStatus(`❌ Error: ${error.message}`);
        this.logExecution(`❌ Error: ${error.message}`, 'error');
    }

    resetFlowSteps() {
        // Reset all flow steps to pending state
        this.flowDefinition?.flow_steps?.forEach((step, index) => {
            this.updateStepStatus(index, 'pending', step.name);
        });

        // Reset progress
        this.updateProgress(0, 'Siap untuk memulai');
        this.currentStepIndex = 0;
    }

    displayFlowDefinition() {
        const display = document.getElementById('flowJsonDisplay');
        if (display && this.flowDefinition) {
            display.textContent = JSON.stringify(this.flowDefinition, null, 2);
        }
    }

    async saveConfiguration() {
        try {
            const config = await this.getCurrentConfiguration();
            await chrome.storage.local.set({ 
                millwareConfig: config,
                millwareFlowDefinition: this.flowDefinition 
            });
            
            this.logExecution('✅ Konfigurasi tersimpan', 'success');
            console.log('✅ Configuration saved');
            
        } catch (error) {
            this.logExecution(`❌ Gagal menyimpan konfigurasi: ${error.message}`, 'error');
            console.error('❌ Error saving configuration:', error);
        }
    }

    exportFlowDefinition() {
        if (!this.flowDefinition) return;

        const dataStr = JSON.stringify(this.flowDefinition, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `millware-flow-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.logExecution('📥 Flow definition exported', 'success');
    }

    validateFlowDefinition() {
        if (!this.flowDefinition) {
            this.logExecution('❌ No flow definition to validate', 'error');
            return;
        }

        const errors = [];
        const warnings = [];

        // Validate required fields
        if (!this.flowDefinition.flow_id) errors.push('Missing flow_id');
        if (!this.flowDefinition.flow_steps || !Array.isArray(this.flowDefinition.flow_steps)) {
            errors.push('Missing or invalid flow_steps array');
        }

        // Validate steps
        this.flowDefinition.flow_steps?.forEach((step, index) => {
            if (!step.step_id) errors.push(`Step ${index + 1}: Missing step_id`);
            if (!step.type) errors.push(`Step ${index + 1}: Missing type`);
            if (!step.parameters) warnings.push(`Step ${index + 1}: Missing parameters`);
        });

        // Update status
        const statusElement = document.getElementById('flowDataStatus');
        if (statusElement) {
            if (errors.length > 0) {
                statusElement.innerHTML = `<span style="color: #ef4444;">❌ ${errors.length} error(s) found</span>`;
                this.logExecution(`❌ Validation failed: ${errors.join(', ')}`, 'error');
            } else if (warnings.length > 0) {
                statusElement.innerHTML = `<span style="color: #f59e0b;">⚠️ ${warnings.length} warning(s)</span>`;
                this.logExecution(`⚠️ Validation completed with warnings: ${warnings.join(', ')}`, 'warning');
            } else {
                statusElement.innerHTML = `<span style="color: #10b981;">✅ Flow definition valid</span>`;
                this.logExecution('✅ Flow definition validation passed', 'success');
            }
        }
    }

    updateProgressDisplay() {
        const flowProgressText = document.getElementById('flowProgressText');
        if (flowProgressText && this.flowDefinition) {
            const totalSteps = this.flowDefinition.flow_steps.length;
            flowProgressText.textContent = `0/${totalSteps}`;
        }
    }

    logExecution(message, type = 'info') {
        const logContainer = document.getElementById('executionLog');
        if (!logContainer) return;

        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `
            <span class="log-time">${timestamp}</span>
            <span class="log-message">${message}</span>
        `;

        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    generateExecutionId() {
        return 'millware_exec_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const millwareVisualFlow = new MillwareVisualFlow();
});