// Venus-Millware AutoFill - Text Search Engine
// Advanced text search functionality similar to browser's Ctrl+F
// Developer: <PERSON><PERSON> - <PERSON> Rebinmas (Delloyd Group)

class VenusTextSearchEngine {
    constructor() {
        this.matches = [];
        this.currentIndex = -1;
        this.searchText = '';
        this.caseSensitive = false;
        this.highlightClass = 'venus-text-highlight';
        this.currentHighlightClass = 'venus-text-highlight-current';
        this.searchUI = null;
        this.isSearchActive = false;
        
        this.init();
    }

    init() {
        console.log('🔍 Venus Text Search Engine initialized');
        this.injectStyles();
        this.setupKeyboardShortcuts();
    }

    injectStyles() {
        if (document.getElementById('venus-text-search-styles')) {
            return; // Already injected
        }

        const style = document.createElement('style');
        style.id = 'venus-text-search-styles';
        style.textContent = `
            .${this.highlightClass} {
                background-color: #ffff00 !important;
                color: #000000 !important;
                padding: 1px 2px !important;
                border-radius: 2px !important;
                box-shadow: 0 0 2px rgba(0,0,0,0.3) !important;
                position: relative !important;
                z-index: 1000 !important;
            }
            
            .${this.currentHighlightClass} {
                background-color: #ff6600 !important;
                color: #ffffff !important;
                font-weight: bold !important;
                box-shadow: 0 0 4px rgba(255,102,0,0.6) !important;
                z-index: 1001 !important;
            }
            
            .venus-search-ui {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                background: #ffffff !important;
                border: 2px solid #007bff !important;
                border-radius: 8px !important;
                padding: 15px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                z-index: 10000 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                font-size: 14px !important;
                min-width: 300px !important;
                max-width: 400px !important;
            }
            
            .venus-search-header {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                margin-bottom: 10px !important;
                font-weight: bold !important;
                color: #333 !important;
            }
            
            .venus-search-close {
                background: none !important;
                border: none !important;
                font-size: 18px !important;
                cursor: pointer !important;
                color: #666 !important;
                padding: 0 !important;
                width: 24px !important;
                height: 24px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
            
            .venus-search-close:hover {
                color: #000 !important;
                background-color: #f0f0f0 !important;
                border-radius: 50% !important;
            }
            
            .venus-search-input {
                width: 100% !important;
                padding: 8px 12px !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                font-size: 14px !important;
                margin-bottom: 10px !important;
                box-sizing: border-box !important;
            }
            
            .venus-search-controls {
                display: flex !important;
                gap: 8px !important;
                align-items: center !important;
                margin-bottom: 10px !important;
            }
            
            .venus-search-btn {
                padding: 6px 12px !important;
                border: 1px solid #007bff !important;
                background: #007bff !important;
                color: white !important;
                border-radius: 4px !important;
                cursor: pointer !important;
                font-size: 12px !important;
                transition: all 0.2s !important;
            }
            
            .venus-search-btn:hover {
                background: #0056b3 !important;
                border-color: #0056b3 !important;
            }
            
            .venus-search-btn:disabled {
                background: #6c757d !important;
                border-color: #6c757d !important;
                cursor: not-allowed !important;
            }
            
            .venus-search-info {
                font-size: 12px !important;
                color: #666 !important;
                text-align: center !important;
                margin-top: 8px !important;
            }
            
            .venus-search-options {
                display: flex !important;
                gap: 10px !important;
                margin-bottom: 10px !important;
                font-size: 12px !important;
            }
            
            .venus-search-checkbox {
                display: flex !important;
                align-items: center !important;
                gap: 4px !important;
                cursor: pointer !important;
            }
            
            .venus-search-checkbox input {
                margin: 0 !important;
            }
        `;
        
        document.head.appendChild(style);
        console.log('✅ Text search styles injected');
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+F or Cmd+F to open search
            if ((event.ctrlKey || event.metaKey) && event.key === 'f' && !this.isSearchActive) {
                event.preventDefault();
                this.showSearchUI();
            }
            
            // Escape to close search
            if (event.key === 'Escape' && this.isSearchActive) {
                this.hideSearchUI();
            }
            
            // F3 or Ctrl+G for next match
            if ((event.key === 'F3' || (event.ctrlKey && event.key === 'g')) && this.isSearchActive) {
                event.preventDefault();
                this.navigateToNext();
            }
            
            // Shift+F3 or Ctrl+Shift+G for previous match
            if (((event.key === 'F3' && event.shiftKey) || (event.ctrlKey && event.shiftKey && event.key === 'G')) && this.isSearchActive) {
                event.preventDefault();
                this.navigateToPrevious();
            }
        });
    }

    async search(searchText, options = {}) {
        console.log(`🔍 Searching for: "${searchText}"`);
        
        this.searchText = searchText;
        this.caseSensitive = options.caseSensitive || false;
        
        // Clear previous highlights
        this.clearHighlights();
        
        if (!searchText.trim()) {
            return { matches: [], currentIndex: -1, searchText: '' };
        }
        
        // Find all matches
        this.matches = this.findAllMatches(searchText, options);
        this.currentIndex = this.matches.length > 0 ? 0 : -1;
        
        // Highlight matches if requested
        if (options.highlightMatches !== false) {
            this.highlightAllMatches();
        }
        
        // Navigate to first match
        if (this.matches.length > 0) {
            this.highlightCurrentMatch();
            this.scrollToCurrentMatch();
        }
        
        console.log(`✅ Search completed: ${this.matches.length} matches found`);
        
        return {
            matches: this.matches,
            currentIndex: this.currentIndex,
            searchText: this.searchText,
            totalMatches: this.matches.length
        };
    }

    findAllMatches(searchText, options = {}) {
        const matches = [];
        const searchRegex = new RegExp(
            this.escapeRegExp(searchText), 
            this.caseSensitive ? 'g' : 'gi'
        );
        
        // Get all text nodes
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    // Skip script, style, and other non-visible elements
                    const parent = node.parentElement;
                    if (!parent) return NodeFilter.FILTER_REJECT;
                    
                    const tagName = parent.tagName.toLowerCase();
                    if (['script', 'style', 'noscript', 'iframe'].includes(tagName)) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    // Skip hidden elements
                    const style = window.getComputedStyle(parent);
                    if (style.display === 'none' || style.visibility === 'hidden') {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );
        
        let node;
        while (node = walker.nextNode()) {
            const text = node.textContent;
            let match;
            
            while ((match = searchRegex.exec(text)) !== null) {
                matches.push({
                    node: node,
                    element: node.parentElement,
                    startOffset: match.index,
                    endOffset: match.index + match[0].length,
                    matchText: match[0],
                    contextBefore: text.substring(Math.max(0, match.index - 20), match.index),
                    contextAfter: text.substring(match.index + match[0].length, Math.min(text.length, match.index + match[0].length + 20))
                });
                
                // Prevent infinite loop
                if (!searchRegex.global) break;
            }
        }
        
        return matches;
    }

    highlightAllMatches() {
        this.matches.forEach((match, index) => {
            this.highlightMatch(index, false);
        });
    }

    highlightMatch(index, isCurrent = false) {
        if (index < 0 || index >= this.matches.length) return;
        
        const match = this.matches[index];
        const range = document.createRange();
        range.setStart(match.node, match.startOffset);
        range.setEnd(match.node, match.endOffset);
        
        // Create highlight span
        const highlight = document.createElement('span');
        highlight.className = isCurrent ? 
            `${this.highlightClass} ${this.currentHighlightClass}` : 
            this.highlightClass;
        highlight.setAttribute('data-venus-highlight', index);
        
        try {
            range.surroundContents(highlight);
            match.highlightElement = highlight;
        } catch (error) {
            console.warn('Could not highlight match:', error);
        }
    }

    clearHighlights() {
        const highlights = document.querySelectorAll(`.${this.highlightClass}`);
        highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            if (parent) {
                parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                parent.normalize();
            }
        });
        
        this.matches.forEach(match => {
            if (match.highlightElement) {
                match.highlightElement = null;
            }
        });
    }

    highlightCurrentMatch() {
        // Remove current highlight from all matches
        document.querySelectorAll(`.${this.currentHighlightClass}`).forEach(el => {
            el.classList.remove(this.currentHighlightClass);
        });
        
        // Add current highlight to current match
        if (this.currentIndex >= 0 && this.currentIndex < this.matches.length) {
            const match = this.matches[this.currentIndex];
            if (match.highlightElement) {
                match.highlightElement.classList.add(this.currentHighlightClass);
            }
        }
    }

    scrollToCurrentMatch() {
        if (this.currentIndex >= 0 && this.currentIndex < this.matches.length) {
            const match = this.matches[this.currentIndex];
            if (match.element) {
                match.element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            }
        }
    }

    navigateToNext() {
        if (this.matches.length === 0) return;
        
        this.currentIndex = (this.currentIndex + 1) % this.matches.length;
        this.highlightCurrentMatch();
        this.scrollToCurrentMatch();
        this.updateSearchInfo();
        
        console.log(`📍 Navigated to match ${this.currentIndex + 1}/${this.matches.length}`);
    }

    navigateToPrevious() {
        if (this.matches.length === 0) return;
        
        this.currentIndex = this.currentIndex === 0 ? this.matches.length - 1 : this.currentIndex - 1;
        this.highlightCurrentMatch();
        this.scrollToCurrentMatch();
        this.updateSearchInfo();
        
        console.log(`📍 Navigated to match ${this.currentIndex + 1}/${this.matches.length}`);
    }

    showSearchUI() {
        if (this.searchUI) {
            this.searchUI.remove();
        }
        
        this.isSearchActive = true;
        this.createSearchUI();
        
        // Focus on search input
        const searchInput = this.searchUI.querySelector('.venus-search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    hideSearchUI() {
        if (this.searchUI) {
            this.searchUI.remove();
            this.searchUI = null;
        }
        
        this.isSearchActive = false;
        this.clearHighlights();
        this.matches = [];
        this.currentIndex = -1;
    }

    createSearchUI() {
        this.searchUI = document.createElement('div');
        this.searchUI.className = 'venus-search-ui';
        this.searchUI.innerHTML = `
            <div class="venus-search-header">
                <span>🔍 Text Search</span>
                <button class="venus-search-close" title="Close (Esc)">×</button>
            </div>
            <input type="text" class="venus-search-input" placeholder="Search text..." value="${this.searchText}">
            <div class="venus-search-options">
                <label class="venus-search-checkbox">
                    <input type="checkbox" ${this.caseSensitive ? 'checked' : ''}> Case sensitive
                </label>
            </div>
            <div class="venus-search-controls">
                <button class="venus-search-btn" data-action="previous" title="Previous (Shift+F3)">↑ Prev</button>
                <button class="venus-search-btn" data-action="next" title="Next (F3)">↓ Next</button>
                <button class="venus-search-btn" data-action="clear" title="Clear search">Clear</button>
            </div>
            <div class="venus-search-info">No matches</div>
        `;
        
        // Add event listeners
        this.setupSearchUIEvents();
        
        document.body.appendChild(this.searchUI);
    }

    setupSearchUIEvents() {
        const searchInput = this.searchUI.querySelector('.venus-search-input');
        const caseSensitiveCheckbox = this.searchUI.querySelector('input[type="checkbox"]');
        const closeButton = this.searchUI.querySelector('.venus-search-close');
        const controls = this.searchUI.querySelectorAll('.venus-search-btn');
        
        // Search input
        searchInput.addEventListener('input', (e) => {
            this.performSearch(e.target.value);
        });
        
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (e.shiftKey) {
                    this.navigateToPrevious();
                } else {
                    this.navigateToNext();
                }
            }
        });
        
        // Case sensitive checkbox
        caseSensitiveCheckbox.addEventListener('change', (e) => {
            this.caseSensitive = e.target.checked;
            this.performSearch(searchInput.value);
        });
        
        // Close button
        closeButton.addEventListener('click', () => {
            this.hideSearchUI();
        });
        
        // Control buttons
        controls.forEach(button => {
            button.addEventListener('click', (e) => {
                const action = e.target.getAttribute('data-action');
                switch (action) {
                    case 'next':
                        this.navigateToNext();
                        break;
                    case 'previous':
                        this.navigateToPrevious();
                        break;
                    case 'clear':
                        searchInput.value = '';
                        this.performSearch('');
                        break;
                }
            });
        });
    }

    async performSearch(searchText) {
        const results = await this.search(searchText, {
            caseSensitive: this.caseSensitive,
            highlightMatches: true
        });
        
        this.updateSearchInfo();
        this.updateControlButtons();
    }

    updateSearchInfo() {
        if (!this.searchUI) return;
        
        const infoElement = this.searchUI.querySelector('.venus-search-info');
        if (this.matches.length === 0) {
            infoElement.textContent = this.searchText ? 'No matches' : 'Enter search text';
        } else {
            infoElement.textContent = `${this.currentIndex + 1} of ${this.matches.length} matches`;
        }
    }

    updateControlButtons() {
        if (!this.searchUI) return;
        
        const prevButton = this.searchUI.querySelector('[data-action="previous"]');
        const nextButton = this.searchUI.querySelector('[data-action="next"]');
        
        const hasMatches = this.matches.length > 0;
        prevButton.disabled = !hasMatches;
        nextButton.disabled = !hasMatches;
    }

    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
}

// Make it globally available
if (typeof window !== 'undefined') {
    window.VenusTextSearchEngine = VenusTextSearchEngine;
}
