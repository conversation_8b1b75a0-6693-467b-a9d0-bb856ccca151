/**
 * Auto Form Fill Pro - Field Highlighter
 * Provides visual feedback for form field interactions
 */

class FieldHighlighter {
  constructor() {
    this.highlightedElements = new Map();
    this.animationDuration = 300;
    this.highlightClass = 'autofill-highlight';
    
    this.initializeStyles();
  }

  /**
   * Initialize CSS styles for highlighting
   */
  initializeStyles() {
    if (document.getElementById('autofill-highlight-styles')) {
      return; // Styles already injected
    }

    const style = document.createElement('style');
    style.id = 'autofill-highlight-styles';
    style.textContent = `
      .autofill-highlight {
        position: relative !important;
        transition: all 0.3s ease !important;
      }
      
      .autofill-highlight::before {
        content: '' !important;
        position: absolute !important;
        top: -2px !important;
        left: -2px !important;
        right: -2px !important;
        bottom: -2px !important;
        border-radius: 6px !important;
        pointer-events: none !important;
        z-index: 1000 !important;
        transition: all 0.3s ease !important;
      }
      
      .autofill-highlight.filling::before {
        border: 2px solid #3b82f6 !important;
        background: rgba(59, 130, 246, 0.1) !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        animation: autofill-pulse 1.5s infinite !important;
      }
      
      .autofill-highlight.success::before {
        border: 2px solid #10b981 !important;
        background: rgba(16, 185, 129, 0.1) !important;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2) !important;
      }
      
      .autofill-highlight.error::before {
        border: 2px solid #ef4444 !important;
        background: rgba(239, 68, 68, 0.1) !important;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
        animation: autofill-shake 0.5s ease-in-out !important;
      }
      
      .autofill-highlight.preview::before {
        border: 2px dashed #6b7280 !important;
        background: rgba(107, 114, 128, 0.05) !important;
      }
      
      @keyframes autofill-pulse {
        0%, 100% {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        }
        50% {
          box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.3) !important;
        }
      }
      
      @keyframes autofill-shake {
        0%, 100% { transform: translateX(0) !important; }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-2px) !important; }
        20%, 40%, 60%, 80% { transform: translateX(2px) !important; }
      }
      
      .autofill-tooltip {
        position: absolute !important;
        top: -35px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        background: #1f2937 !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        z-index: 1001 !important;
        opacity: 0 !important;
        transition: opacity 0.2s ease !important;
        pointer-events: none !important;
        white-space: nowrap !important;
      }
      
      .autofill-tooltip::before {
        content: '' !important;
        position: absolute !important;
        top: 100% !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        border: 4px solid transparent !important;
        border-top-color: #1f2937 !important;
      }
      
      .autofill-highlight.filling .autofill-tooltip,
      .autofill-highlight.success .autofill-tooltip,
      .autofill-highlight.error .autofill-tooltip {
        opacity: 1 !important;
      }
      
      .autofill-form-outline {
        outline: 2px dashed #3b82f6 !important;
        outline-offset: 4px !important;
        border-radius: 8px !important;
        transition: outline 0.3s ease !important;
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * Highlight a field with specified state
   */
  highlightField(element, state = 'filling', options = {}) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
      console.warn('Invalid element provided to highlightField');
      return;
    }

    const {
      duration = null,
      message = null,
      showTooltip = true
    } = options;

    // Clear any existing highlight
    this.clearFieldHighlight(element);

    // Add highlight classes
    element.classList.add(this.highlightClass, state);
    
    // Add tooltip if message provided and tooltips enabled
    if (showTooltip && message) {
      this.addTooltip(element, message);
    }

    // Store highlight information
    const highlightInfo = {
      element,
      state,
      timestamp: Date.now(),
      timeout: null
    };

    // Set auto-clear timeout if duration specified
    if (duration && duration > 0) {
      highlightInfo.timeout = setTimeout(() => {
        this.clearFieldHighlight(element);
      }, duration);
    }

    this.highlightedElements.set(element, highlightInfo);

    // Scroll element into view if it's not visible
    this.scrollToElementIfNeeded(element);
  }

  /**
   * Clear highlight from a specific field
   */
  clearFieldHighlight(element) {
    if (!element) return;

    const highlightInfo = this.highlightedElements.get(element);
    
    if (highlightInfo) {
      // Clear timeout
      if (highlightInfo.timeout) {
        clearTimeout(highlightInfo.timeout);
      }
      
      // Remove from tracking
      this.highlightedElements.delete(element);
    }

    // Remove classes
    element.classList.remove(
      this.highlightClass,
      'filling',
      'success',
      'error',
      'preview'
    );

    // Remove tooltip
    this.removeTooltip(element);
  }

  /**
   * Clear all field highlights
   */
  clearAllHighlights() {
    this.highlightedElements.forEach((info, element) => {
      this.clearFieldHighlight(element);
    });
    this.highlightedElements.clear();
  }

  /**
   * Highlight all fields that will be filled (preview mode)
   */
  previewFillableFields(fields, options = {}) {
    const {
      duration = 3000,
      showCount = true
    } = options;

    fields.forEach((field, index) => {
      const element = field.element || field;
      const message = showCount ? `Field ${index + 1} of ${fields.length}` : 'Will be filled';
      
      this.highlightField(element, 'preview', {
        duration,
        message,
        showTooltip: true
      });
    });

    return fields.length;
  }

  /**
   * Highlight a form boundary
   */
  highlightForm(formElement, options = {}) {
    if (!formElement) return;

    const {
      duration = 2000
    } = options;

    formElement.classList.add('autofill-form-outline');

    if (duration > 0) {
      setTimeout(() => {
        formElement.classList.remove('autofill-form-outline');
      }, duration);
    }
  }

  /**
   * Add tooltip to element
   */
  addTooltip(element, message) {
    // Remove existing tooltip
    this.removeTooltip(element);

    const tooltip = document.createElement('div');
    tooltip.className = 'autofill-tooltip';
    tooltip.textContent = message;
    tooltip.setAttribute('data-autofill-tooltip', 'true');

    // Position tooltip relative to element
    element.style.position = element.style.position || 'relative';
    element.appendChild(tooltip);
  }

  /**
   * Remove tooltip from element
   */
  removeTooltip(element) {
    const existingTooltip = element.querySelector('[data-autofill-tooltip]');
    if (existingTooltip) {
      existingTooltip.remove();
    }
  }

  /**
   * Scroll element into view if needed
   */
  scrollToElementIfNeeded(element) {
    const rect = element.getBoundingClientRect();
    const viewport = {
      top: 0,
      left: 0,
      bottom: window.innerHeight,
      right: window.innerWidth
    };

    // Check if element is outside viewport
    if (rect.bottom < viewport.top || 
        rect.top > viewport.bottom || 
        rect.right < viewport.left || 
        rect.left > viewport.right) {
      
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
    }
  }

  /**
   * Animate field filling sequence
   */
  async animateFieldSequence(fields, options = {}) {
    const {
      delay = 200,
      highlightDuration = 1000,
      showProgress = true
    } = options;

    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      const element = field.element || field;
      
      const message = showProgress ? 
        `Filling ${i + 1}/${fields.length}: ${field.name || field.id || 'field'}` : 
        'Filling...';

      // Highlight current field
      this.highlightField(element, 'filling', {
        message,
        duration: highlightDuration
      });

      // Wait for delay
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Show success animation for filled fields
   */
  showSuccessAnimation(fields, options = {}) {
    const {
      staggerDelay = 100,
      duration = 1500
    } = options;

    fields.forEach((field, index) => {
      setTimeout(() => {
        const element = field.element || field;
        this.highlightField(element, 'success', {
          message: 'Filled successfully',
          duration
        });
      }, index * staggerDelay);
    });
  }

  /**
   * Show error animation for failed fields
   */
  showErrorAnimation(fields, errors = {}, options = {}) {
    const {
      duration = 2000
    } = options;

    fields.forEach(field => {
      const element = field.element || field;
      const fieldId = field.id || field.name || 'unknown';
      const errorMessage = errors[fieldId] || 'Fill failed';
      
      this.highlightField(element, 'error', {
        message: errorMessage,
        duration
      });
    });
  }

  /**
   * Create a visual progress indicator
   */
  createProgressIndicator(container, total) {
    const progressContainer = document.createElement('div');
    progressContainer.className = 'autofill-progress';
    progressContainer.style.cssText = `
      position: fixed !important;
      top: 20px !important;
      right: 20px !important;
      background: white !important;
      border: 1px solid #e5e7eb !important;
      border-radius: 8px !important;
      padding: 12px 16px !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
      z-index: 10000 !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      font-size: 14px !important;
      min-width: 200px !important;
    `;

    const progressText = document.createElement('div');
    progressText.style.cssText = `
      margin-bottom: 8px !important;
      font-weight: 500 !important;
      color: #374151 !important;
    `;
    progressText.textContent = 'Filling forms...';

    const progressBar = document.createElement('div');
    progressBar.style.cssText = `
      width: 100% !important;
      height: 4px !important;
      background: #e5e7eb !important;
      border-radius: 2px !important;
      overflow: hidden !important;
    `;

    const progressFill = document.createElement('div');
    progressFill.style.cssText = `
      width: 0% !important;
      height: 100% !important;
      background: #3b82f6 !important;
      transition: width 0.3s ease !important;
    `;

    const progressCount = document.createElement('div');
    progressCount.style.cssText = `
      margin-top: 8px !important;
      font-size: 12px !important;
      color: #6b7280 !important;
    `;
    progressCount.textContent = `0 / ${total} fields`;

    progressBar.appendChild(progressFill);
    progressContainer.appendChild(progressText);
    progressContainer.appendChild(progressBar);
    progressContainer.appendChild(progressCount);

    document.body.appendChild(progressContainer);

    return {
      container: progressContainer,
      text: progressText,
      fill: progressFill,
      count: progressCount,
      update: (current, status = 'Filling forms...') => {
        const percentage = (current / total) * 100;
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = status;
        progressCount.textContent = `${current} / ${total} fields`;
      },
      remove: () => {
        if (progressContainer.parentNode) {
          progressContainer.parentNode.removeChild(progressContainer);
        }
      }
    };
  }

  /**
   * Get currently highlighted elements
   */
  getHighlightedElements() {
    return Array.from(this.highlightedElements.keys());
  }

  /**
   * Get highlight state of element
   */
  getElementHighlightState(element) {
    const info = this.highlightedElements.get(element);
    return info ? info.state : null;
  }

  /**
   * Check if element is highlighted
   */
  isElementHighlighted(element) {
    return this.highlightedElements.has(element);
  }

  /**
   * Update highlight state
   */
  updateHighlightState(element, newState, options = {}) {
    if (!this.isElementHighlighted(element)) {
      return false;
    }

    // Clear current state
    element.classList.remove('filling', 'success', 'error', 'preview');
    
    // Add new state
    element.classList.add(newState);
    
    // Update stored info
    const info = this.highlightedElements.get(element);
    if (info) {
      info.state = newState;
      info.timestamp = Date.now();
    }

    // Update tooltip if message provided
    if (options.message) {
      this.removeTooltip(element);
      this.addTooltip(element, options.message);
    }

    return true;
  }

  /**
   * Cleanup all highlights and styles
   */
  cleanup() {
    this.clearAllHighlights();
    
    // Remove injected styles
    const styleElement = document.getElementById('autofill-highlight-styles');
    if (styleElement) {
      styleElement.remove();
    }

    // Remove any remaining progress indicators
    const progressElements = document.querySelectorAll('.autofill-progress');
    progressElements.forEach(el => el.remove());
  }
}

// Create singleton instance
const fieldHighlighter = new FieldHighlighter();

// Export for use in other modules
window.FieldHighlighter = fieldHighlighter; 