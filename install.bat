@echo off
echo ========================================
echo Venus Auto Fill Chrome Extension Setup
echo ========================================
echo.

echo Checking if Chrome is installed...
where chrome >nul 2>nul
if %errorlevel% neq 0 (
    echo Chrome not found in PATH. Please ensure Chrome is installed.
    echo You can download Chrome from: https://www.google.com/chrome/
    pause
    exit /b 1
)

echo Chrome found!
echo.

echo Current directory: %cd%
echo.

echo Checking extension files...
if not exist "manifest.json" (
    echo ERROR: manifest.json not found!
    echo Please ensure you're running this script from the extension directory.
    pause
    exit /b 1
)

if not exist "popup.html" (
    echo ERROR: popup.html not found!
    pause
    exit /b 1
)

if not exist "background.js" (
    echo ERROR: background.js not found!
    pause
    exit /b 1
)

if not exist "content.js" (
    echo ERROR: content.js not found!
    pause
    exit /b 1
)

if not exist "icons" (
    echo ERROR: icons directory not found!
    pause
    exit /b 1
)

echo All required files found!
echo.

echo ========================================
echo Installation Instructions
echo ========================================
echo.
echo 1. Open Google Chrome
echo 2. Navigate to: chrome://extensions/
echo 3. Enable "Developer mode" (toggle in top-right corner)
echo 4. Click "Load unpacked"
echo 5. Select this folder: %cd%
echo 6. The extension should now appear in your extensions list
echo 7. Pin the extension by clicking the puzzle piece icon in Chrome toolbar
echo.

echo ========================================
echo Next Steps
echo ========================================
echo.
echo 1. Set up your Google Apps Script:
echo    - Open script.google.com
echo    - Create a new project
echo    - Copy the code from google-apps-script-sample.js
echo    - Deploy as a web app
echo    - Copy the web app URL
echo.
echo 2. Configure the extension:
echo    - Click the Venus Auto Fill icon in Chrome
echo    - Go to Configuration tab
echo    - Enter your target website details
echo    - Paste the Google Apps Script URL
echo    - Test the connection
echo.
echo 3. Create your automation flows:
echo    - Go to Flow Definition tab
echo    - Add events for your specific website
echo    - Test elements using the Check button
echo    - Save your flow for reuse
echo.

echo ========================================
echo Troubleshooting
echo ========================================
echo.
echo If you encounter issues:
echo 1. Check the Chrome console (F12) for error messages
echo 2. Verify all files are present in the extension directory
echo 3. Ensure your Google Apps Script is properly deployed
echo 4. Check that your target website allows automation
echo.
echo For more help, see README.md
echo.

echo Press any key to open Chrome extensions page...
pause >nul

start chrome chrome://extensions/

echo.
echo Setup script completed!
echo The Chrome extensions page should now be open.
echo Follow the installation instructions above.
echo.
pause
