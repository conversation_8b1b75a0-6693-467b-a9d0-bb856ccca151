{"flow_id": "millware_login_automation_v1", "name": "Millware Login & Task Register Automation", "description": "Automated login sequence untuk Millware system dengan navigasi ke Task Register", "version": "1.0.0", "author": "Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)", "created_date": "2024-01-20", "target_system": {"name": "Millware ERP System", "base_url": "http://millwarep3.rebinmas.com:8003/", "authentication_required": true}, "flow_metadata": {"estimated_duration": "15-30 seconds", "complexity": "medium", "success_rate": "high", "dependencies": ["active_internet", "target_server_accessible"]}, "visual_flow_config": {"layout": "vertical", "theme": "modern", "animation_enabled": true, "highlight_current_step": true, "highlight_color": "#ff4444", "success_color": "#22c55e", "error_color": "#ef4444", "pending_color": "#3b82f6"}, "execution_settings": {"timeout_per_step": 10000, "retry_failed_steps": true, "continue_on_error": false, "take_screenshots": true, "log_detailed_errors": true}, "flow_steps": [{"id": "step_1", "name": "Navigate to Login Page", "type": "navigate", "description": "Membuka halaman login Millware system", "order": 1, "visual_node": {"icon": "🌐", "title": "<PERSON><PERSON>", "subtitle": "Navigasi ke sistem Millware", "position": {"x": 100, "y": 50}}, "parameters": {"url": "http://millwarep3.rebinmas.com:8003/", "wait_for_load": true, "timeout": 30000}, "success_criteria": {"url_contains": "millwarep3.rebinmas.com", "elements_present": ["#txtUsername", "#txtPassword", "#btnLogin"]}, "error_handling": {"retry_count": 2, "fallback_action": "alert_user"}}, {"id": "step_2", "name": "Wait for Page Load", "type": "wait", "description": "<PERSON><PERSON><PERSON> halaman login selesai dimuat", "order": 2, "visual_node": {"icon": "⏳", "title": "<PERSON><PERSON><PERSON>", "subtitle": "Memastikan semua elemen siap", "position": {"x": 100, "y": 120}}, "parameters": {"duration": 500, "wait_for_element": "#txtUsername", "element_visible": true}, "success_criteria": {"element_exists": "#txtUsername", "element_interactable": true}}, {"id": "step_3", "name": "Input Username", "type": "input", "description": "Mengisi field username dengan kredensial yang valid", "order": 3, "visual_node": {"icon": "👤", "title": "Input Username", "subtitle": "<PERSON><PERSON><PERSON><PERSON> nama pengguna", "position": {"x": 100, "y": 190}}, "parameters": {"selector": "#txtUsername", "selector_alternatives": ["input[name='txtUsername']", "input[placeholder*='User Name']"], "value": "adm075", "clear_first": true, "simulate_typing": true, "typing_delay": 100}, "visual_feedback": {"highlight_element": true, "highlight_style": {"border": "3px solid #ff4444", "box_shadow": "0 0 10px #ff4444", "background_color": "rgba(255, 68, 68, 0.1)"}, "highlight_duration": 2000}, "success_criteria": {"element_value_equals": "adm075", "no_error_messages": true}, "error_handling": {"retry_count": 2, "clear_and_retry": true}}, {"id": "step_4", "name": "Input Password", "type": "input", "description": "Mengisi field password dengan kredensial yang valid", "order": 4, "visual_node": {"icon": "🔒", "title": "Input Password", "subtitle": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "position": {"x": 100, "y": 260}}, "parameters": {"selector": "#txtPassword", "selector_alternatives": ["input[name='txtPassword']", "input[type='password']"], "value": "adm075", "clear_first": true, "simulate_typing": true, "typing_delay": 100, "mask_value_in_logs": true, "wait_after_input": 300}, "visual_feedback": {"highlight_element": true, "highlight_style": {"border": "3px solid #ff4444", "box_shadow": "0 0 10px #ff4444", "background_color": "rgba(255, 68, 68, 0.1)"}, "highlight_duration": 2000}, "success_criteria": {"element_has_value": true, "no_error_messages": true}, "error_handling": {"retry_count": 2, "clear_and_retry": true}}, {"id": "step_5", "name": "<PERSON><PERSON> Login <PERSON>", "type": "click", "description": "Klik tombol login untuk melakukan autentikasi", "order": 5, "visual_node": {"icon": "🚀", "title": "<PERSON><PERSON>", "subtitle": "Autentikasi ke sistem", "position": {"x": 100, "y": 330}}, "parameters": {"selector": "#btnLogin", "selector_alternatives": ["input[name='btnLogin']", "input[value='LOG IN']", "input[type='submit']", "button[type='submit']", "input[value*='LOG']", "input[value*='Login']", "button[value*='LOG']", "button[value*='Login']", ".button[value='LOG IN']", "form input[type='submit']", "input[class*='button']", "button:not([type='button']):not([type='reset'])"], "click_method": "mouse_event", "double_click": false, "wait_after_click": 500}, "visual_feedback": {"highlight_element": true, "highlight_style": {"border": "3px solid #ff4444", "box_shadow": "0 0 15px #ff4444", "background_color": "rgba(255, 68, 68, 0.2)"}, "highlight_duration": 1500, "click_animation": true}, "success_criteria": {"url_changed": true, "or": [{"element_appears": ".PopupBoxLogin"}, {"url_contains": "en/PR/trx/"}, {"element_appears": "#MainContent"}]}, "error_handling": {"retry_count": 1, "wait_before_retry": 2000}}, {"id": "step_6", "name": "<PERSON><PERSON>", "type": "conditional_action", "description": "<PERSON><PERSON>ni popup login jika muncul set<PERSON>h login", "order": 6, "visual_node": {"icon": "💬", "title": "<PERSON><PERSON>", "subtitle": "Tutup popup jika ada", "position": {"x": 100, "y": 400}}, "condition": {"type": "element_exists", "selector": ".PopupBoxLogin", "timeout": 200, "visible": true}, "true_action": {"type": "text_search_click", "searchTexts": ["ok", "OK", "Ok"], "timeout": 200, "quick": true, "wait_after_click": 200}, "false_action": {"type": "log", "message": "No popup detected, continuing to next step"}, "visual_feedback": {"highlight_element": true, "highlight_style": {"border": "3px solid #ffa500", "box_shadow": "0 0 10px #ffa500"}, "show_conditional_indicator": true}, "success_criteria": {"popup_closed": true, "or": [{"element_not_exists": ".PopupBoxLogin"}, {"url_contains": "en/PR/trx/"}]}}, {"id": "step_7", "name": "Navigate to Task Register", "type": "navigate", "description": "Na<PERSON><PERSON><PERSON> ke halaman Task Register List", "order": 7, "visual_node": {"icon": "📋", "title": "Buka Task Register", "subtitle": "Navi<PERSON>i ke halaman tugas", "position": {"x": 100, "y": 470}}, "parameters": {"url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx", "wait_for_load": true, "timeout": 15000}, "success_criteria": {"url_contains": "frmPrTrxTaskRegisterList.aspx", "element_exists": "#MainContent_btnNew"}, "error_handling": {"retry_count": 2, "fallback_url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/"}}, {"id": "step_8", "name": "Wait for Task Register Page", "type": "wait", "description": "Menunggu halaman Task Register selesai dimuat", "order": 8, "visual_node": {"icon": "⏳", "title": "<PERSON><PERSON><PERSON>", "subtitle": "Memastikan halaman siap", "position": {"x": 100, "y": 540}}, "parameters": {"duration": 300, "wait_for_element": "#MainContent_btnNew", "element_visible": true}, "success_criteria": {"element_exists": "#MainContent_btnNew", "element_interactable": true}}, {"id": "step_9", "name": "Click New Button", "type": "text_search_click", "description": "Klik tombol New untuk membuat task register baru menggunakan text search", "order": 9, "visual_node": {"icon": "➕", "title": "Klik Tombol New", "subtitle": "Buat task register baru", "position": {"x": 100, "y": 610}}, "parameters": {"searchTexts": ["New", "NEW", "new", "<PERSON><PERSON>"], "timeout": 1000, "quick": true, "elementTypes": ["button", "input", "a"], "wait_after_click": 500}, "visual_feedback": {"highlight_element": true, "highlight_style": {"border": "3px solid #22c55e", "box_shadow": "0 0 15px #22c55e", "background_color": "rgba(34, 197, 94, 0.1)"}, "highlight_duration": 2000}, "success_criteria": {"page_redirected": true, "or": [{"url_contains": "frmPrTrxTaskRegister.aspx"}, {"element_appears": "#MainContent_txtTaskCode"}]}, "completion_action": {"type": "success_notification", "message": "✅ Millware automation completed successfully!"}}], "flow_connections": [{"from": "step_1", "to": "step_2"}, {"from": "step_2", "to": "step_3"}, {"from": "step_3", "to": "step_4"}, {"from": "step_4", "to": "step_5"}, {"from": "step_5", "to": "step_6"}, {"from": "step_6", "to": "step_7"}, {"from": "step_7", "to": "step_8"}, {"from": "step_8", "to": "step_9"}], "error_recovery": {"global_timeout": 120000, "screenshot_on_error": true, "retry_entire_flow": false, "recovery_actions": [{"error_type": "element_not_found", "action": "wait_and_retry", "max_retries": 2}, {"error_type": "navigation_timeout", "action": "refresh_and_retry", "max_retries": 1}, {"error_type": "authentication_failed", "action": "alert_user", "stop_execution": true}]}, "success_metrics": {"completion_time": "target_under_30s", "success_rate": "target_above_95%", "user_satisfaction": "high"}}