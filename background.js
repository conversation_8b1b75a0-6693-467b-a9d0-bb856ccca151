// Venus-Millware AutoFill - Background Service Worker
// Developer: <PERSON><PERSON> - IT Rebinmas (Delloyd Group)

class VenusAutoFillBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        console.log('Venus-Millware AutoFill background service worker initialized');
    }

    setupEventListeners() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Handle messages from popup and content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Handle storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });
    }

    handleInstallation(details) {
        if (details.reason === 'install') {
            console.log('Venus Auto Fill installed');
            this.setDefaultConfiguration();
        } else if (details.reason === 'update') {
            console.log('Venus Auto Fill updated');
            this.handleUpdate(details);
        }
    }

    async setDefaultConfiguration() {
        const defaultConfig = {
            scriptUrl: 'https://script.google.com/macros/s/AKfycbzfhUsf1nHUy3ivBdjK-GkzTjcnM6oSUHKDRUDf9r0zKvvEeQcLl7y85nmXctZqRM7rDg/exec',
            sheetName: 'monthlyGridData_May_2025',
            targetUrl: '',
            username: '',
            password: ''
        };

        try {
            await chrome.storage.local.set({ venusConfig: defaultConfig });
            console.log('Default configuration set');
        } catch (error) {
            console.error('Failed to set default configuration:', error);
        }
    }

    handleUpdate(details) {
        console.log(`Venus Auto Fill updated from ${details.previousVersion} to ${chrome.runtime.getManifest().version}`);
        // Handle any migration logic here if needed
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'fetchData':
                    await this.fetchDataFromScript(message, sendResponse);
                    break;

                case 'testConnection':
                    await this.testScriptConnection(message, sendResponse);
                    break;

                case 'executeFlow':
                    await this.executeFlowOnTab(message, sender, sendResponse);
                    break;

                case 'getTabInfo':
                    await this.getTabInfo(sender, sendResponse);
                    break;

                case 'logExecution':
                    this.logExecutionEvent(message);
                    sendResponse({ success: true });
                    break;

                // New automation actions
                case 'TEST_API_CONNECTION':
                    await this.testApiConnection(sendResponse);
                    break;

                case 'FETCH_STAGING_DATA':
                    await this.fetchStagingData(sendResponse);
                    break;

                case 'RUN_COMPLETE_AUTOMATION':
                    await this.runCompleteAutomation(message, sendResponse);
                    break;

                case 'GET_EXTENSION_STATUS':
                    await this.getExtensionStatus(sendResponse);
                    break;

                case 'TOGGLE_EXTENSION':
                    await this.toggleExtension(sendResponse);
                    break;

                case 'GET_CONFIGURATION':
                    await this.getConfiguration(sendResponse);
                    break;

                case 'UPDATE_CONFIGURATION':
                    await this.updateConfiguration(message, sendResponse);
                    break;

                default:
                    console.warn('Unknown message action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async fetchDataFromScript(message, sendResponse) {
        try {
            const { scriptUrl, sheetName } = message;
            
            if (!scriptUrl || !sheetName) {
                throw new Error('Script URL and Sheet Name are required');
            }
            
            const url = `${scriptUrl}?action=getData&sheet=${encodeURIComponent(sheetName)}`;
            
            console.log('Fetching data from:', url);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                // Add mode and credentials for better CORS handling
                mode: 'cors',
                credentials: 'omit'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
            }

            const text = await response.text();
            console.log('Raw response:', text);
            
            let data;
            try {
                data = JSON.parse(text);
            } catch (parseError) {
                throw new Error(`Invalid JSON response: ${parseError.message}`);
            }
            
            if (data.success) {
                // Process and validate the data
                const processedData = this.processAttendanceData(data.data);
                console.log('Processed data:', processedData.length, 'records');
                sendResponse({ success: true, data: processedData });
            } else {
                sendResponse({ success: false, error: data.error || 'Unknown error from Google Apps Script' });
            }
        } catch (error) {
            console.error('Fetch data error:', error);
            sendResponse({ success: false, error: `Data fetch failed: ${error.message}` });
        }
    }

    processAttendanceData(rawData) {
        if (!Array.isArray(rawData)) {
            throw new Error('Invalid data format: expected array');
        }

        return rawData.map((row, index) => {
            // Expected format: [Employee ID, Employee Name, Day1, Day2, ..., Day31]
            if (!Array.isArray(row) || row.length < 3) {
                console.warn(`Invalid row ${index}:`, row);
                return null;
            }

            const employeeId = row[0];
            const employeeName = row[1];
            const attendanceData = row.slice(2); // All days data

            // Parse attendance data - format: "(7) | (7.5)" where first is regular hours, second is overtime
            const parsedAttendance = attendanceData.map(dayData => {
                if (!dayData || typeof dayData !== 'string') {
                    return { regular: 0, overtime: 0, raw: dayData };
                }

                const match = dayData.match(/\(([^)]+)\)\s*\|\s*\(([^)]+)\)/);
                if (match) {
                    return {
                        regular: parseFloat(match[1]) || 0,
                        overtime: parseFloat(match[2]) || 0,
                        raw: dayData
                    };
                } else {
                    // Try to parse single number in parentheses
                    const singleMatch = dayData.match(/\(([^)]+)\)/);
                    if (singleMatch) {
                        return {
                            regular: parseFloat(singleMatch[1]) || 0,
                            overtime: 0,
                            raw: dayData
                        };
                    }
                }

                return { regular: 0, overtime: 0, raw: dayData };
            });

            return {
                employeeId,
                employeeName,
                attendanceData: parsedAttendance,
                rawAttendanceData: attendanceData
            };
        }).filter(row => row !== null);
    }

    async testScriptConnection(message, sendResponse) {
        try {
            const { scriptUrl, sheetName } = message;
            const url = `${scriptUrl}?action=test&sheet=${encodeURIComponent(sheetName)}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            sendResponse(data);
        } catch (error) {
            console.error('Test connection error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeFlowOnTab(message, sender, sendResponse) {
        try {
            const { flowEvents, data } = message;
            const tabId = sender.tab.id;

            // Inject the execution script into the tab
            await chrome.scripting.executeScript({
                target: { tabId },
                files: ['injected.js']
            });

            // Send the flow execution command
            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'executeFlow',
                flowEvents,
                data
            });

            sendResponse(response);
        } catch (error) {
            console.error('Execute flow error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async getTabInfo(sender, sendResponse) {
        try {
            const tab = sender.tab;
            sendResponse({
                success: true,
                tabInfo: {
                    id: tab.id,
                    url: tab.url,
                    title: tab.title
                }
            });
        } catch (error) {
            console.error('Get tab info error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    logExecutionEvent(message) {
        const { event, level, timestamp } = message;
        console.log(`[${timestamp || new Date().toISOString()}] [${level || 'INFO'}] ${event}`);
        
        // Store execution logs for debugging
        chrome.storage.local.get(['venusExecutionLogs'], (result) => {
            const logs = result.venusExecutionLogs || [];
            logs.push({
                event,
                level: level || 'info',
                timestamp: timestamp || new Date().toISOString()
            });
            
            // Keep only last 100 logs
            if (logs.length > 100) {
                logs.splice(0, logs.length - 100);
            }
            
            chrome.storage.local.set({ venusExecutionLogs: logs });
        });
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Handle tab updates if needed
        if (changeInfo.status === 'complete' && tab.url) {
            // Tab finished loading
            console.log(`Tab ${tabId} finished loading: ${tab.url}`);
        }
    }

    handleStorageChange(changes, namespace) {
        // Handle storage changes if needed
        if (namespace === 'local') {
            for (const key in changes) {
                const change = changes[key];
                console.log(`Storage key "${key}" changed:`, change);
            }
        }
    }

    // New automation methods
    async testApiConnection(sendResponse) {
        try {
            console.log('🔍 Testing API connection to staging server...');

            const response = await fetch('http://localhost:5173/api/staging/data', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                mode: 'cors',
                credentials: 'omit'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseData = await response.json();
            console.log('🔍 API test response:', responseData);

            let dataPreview = 'Unknown format';
            let recordCount = 0;

            if (responseData.success && responseData.data && Array.isArray(responseData.data)) {
                recordCount = responseData.data.length;
                dataPreview = `${recordCount} employee timesheet records`;
            } else if (Array.isArray(responseData)) {
                recordCount = responseData.length;
                dataPreview = `${recordCount} records (direct array)`;
            }

            sendResponse({
                success: true,
                data: {
                    status: 'connected',
                    endpoint: 'http://localhost:5173/api/staging/data',
                    timestamp: new Date().toISOString(),
                    responseStatus: response.status,
                    dataPreview: dataPreview,
                    recordCount: recordCount,
                    apiSuccess: responseData.success || false,
                    totalRecords: responseData.total_records || recordCount
                }
            });
        } catch (error) {
            console.error('❌ API connection test failed:', error);

            // Provide more detailed error information
            let errorDetails = error.message;
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                errorDetails = 'Network error - unable to reach staging API server. Please check if the server is running and accessible.';
            }

            sendResponse({
                success: false,
                error: `API connection failed: ${errorDetails}`,
                endpoint: 'http://localhost:5173/api/staging/data',
                errorType: error.name,
                timestamp: new Date().toISOString()
            });
        }
    }

    async fetchStagingData(sendResponse) {
        try {
            console.log('📥 Fetching staging data...');

            const response = await fetch('http://localhost:5173/api/staging/data', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                mode: 'cors',
                credentials: 'omit'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseData = await response.json();
            console.log('📋 Raw API response:', responseData);

            // Handle the correct API response format
            if (responseData.success && responseData.data && Array.isArray(responseData.data)) {
                const records = responseData.data;
                console.log(`✅ Successfully fetched ${records.length} records`);

                sendResponse({
                    success: true,
                    data: records,
                    metadata: {
                        recordCount: records.length,
                        totalRecords: responseData.total_records || records.length,
                        returnedRecords: responseData.returned_records || records.length,
                        timestamp: new Date().toISOString(),
                        source: 'staging_api',
                        pagination: responseData.pagination || null
                    }
                });
            } else if (Array.isArray(responseData)) {
                // Fallback for direct array response
                console.log(`✅ Successfully fetched ${responseData.length} records (direct array)`);
                sendResponse({
                    success: true,
                    data: responseData,
                    metadata: {
                        recordCount: responseData.length,
                        timestamp: new Date().toISOString(),
                        source: 'staging_api'
                    }
                });
            } else {
                throw new Error(`Invalid data format received from API. Expected array or object with data property, got: ${typeof responseData}`);
            }
        } catch (error) {
            console.error('❌ Failed to fetch staging data:', error);

            // Try Google Apps Script fallback
            console.log('🔄 Attempting Google Apps Script fallback...');
            try {
                const fallbackResponse = await this.fetchDataFromGoogleAppsScript();
                if (fallbackResponse.success) {
                    console.log('✅ Google Apps Script fallback successful');
                    sendResponse(fallbackResponse);
                    return;
                }
            } catch (fallbackError) {
                console.error('❌ Google Apps Script fallback also failed:', fallbackError);
            }

            sendResponse({
                success: false,
                error: `Failed to fetch staging data: ${error.message}`,
                details: {
                    endpoint: 'http://localhost:5173/api/staging/data',
                    timestamp: new Date().toISOString(),
                    fallbackAttempted: true
                }
            });
        }
    }

    async runCompleteAutomation(message, sendResponse) {
        try {
            const { data } = message;
            const { tabId, stagingData, targetUrl, credentials } = data;

            console.log('🚀 Starting complete automation flow...');

            // Step 1: Navigate to target website
            console.log('🌐 Navigating to target website...');
            await chrome.tabs.update(tabId, { url: targetUrl });

            // Wait for navigation to complete
            await this.waitForTabLoad(tabId);

            // Step 2: Inject content script and run automation
            console.log('💉 Injecting automation scripts...');
            await chrome.scripting.executeScript({
                target: { tabId },
                files: ['content.js']
            });

            // Step 3: Execute login automation flow
            console.log('🔐 Starting login automation...');
            const automationFlow = this.createLoginAutomationFlow(credentials);

            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'executeAutomationFlow',
                flowEvents: automationFlow,
                automationData: stagingData,
                metadata: {
                    executionId: this.generateExecutionId(),
                    targetUrl: targetUrl,
                    timestamp: new Date().toISOString()
                }
            });

            if (response && response.success) {
                console.log('✅ Automation completed successfully');
                sendResponse({
                    success: true,
                    message: 'Complete automation flow executed successfully',
                    results: response.results || {}
                });
            } else {
                throw new Error(response?.error || 'Automation execution failed');
            }

        } catch (error) {
            console.error('❌ Complete automation failed:', error);
            sendResponse({
                success: false,
                error: `Complete automation failed: ${error.message}`
            });
        }
    }

    createLoginAutomationFlow(credentials) {
        return [
            {
                type: 'wait',
                duration: 2000,
                description: 'Wait for page to load'
            },
            {
                type: 'input',
                selector: 'input[type="text"], input[name*="user"], input[id*="user"], input[placeholder*="user"]',
                selectorType: 'css',
                value: credentials.username,
                clearFirst: true,
                description: 'Fill username field'
            },
            {
                type: 'wait',
                duration: 1000,
                description: 'Wait after username input'
            },
            {
                type: 'input',
                selector: 'input[type="password"], input[name*="pass"], input[id*="pass"]',
                selectorType: 'css',
                value: credentials.password,
                clearFirst: true,
                description: 'Fill password field'
            },
            {
                type: 'wait',
                duration: 1000,
                description: 'Wait after password input'
            },
            {
                type: 'click',
                selector: 'button[type="submit"], input[type="submit"], button:contains("LOG IN"), button:contains("Login"), button:contains("Sign In")',
                selectorType: 'css',
                description: 'Click login button'
            },
            {
                type: 'wait',
                duration: 3000,
                description: 'Wait for login completion'
            }
        ];
    }

    async waitForTabLoad(tabId, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkTab = async () => {
                try {
                    const tab = await chrome.tabs.get(tabId);

                    if (tab.status === 'complete') {
                        resolve();
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error('Tab load timeout'));
                    } else {
                        setTimeout(checkTab, 500);
                    }
                } catch (error) {
                    reject(error);
                }
            };

            checkTab();
        });
    }

    generateExecutionId() {
        return 'exec_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    async fetchDataFromGoogleAppsScript() {
        try {
            // Get Google Apps Script URL from storage or use default
            const result = await chrome.storage.local.get(['venusConfig']);
            const config = result.venusConfig || {};
            const scriptUrl = config.scriptUrl || 'https://script.google.com/macros/s/AKfycbzfhUsf1nHUy3ivBdjK-GkzTjcnM6oSUHKDRUDf9r0zKvvEeQcLl7y85nmXctZqRM7rDg/exec';
            const sheetName = config.sheetName || 'monthlyGridData_May_2025';

            console.log('🔄 Fetching data from Google Apps Script:', scriptUrl);

            const url = `${scriptUrl}?action=getData&sheet=${encodeURIComponent(sheetName)}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                mode: 'cors',
                credentials: 'omit'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
            }

            const text = await response.text();
            console.log('Raw Google Apps Script response:', text);

            let data;
            try {
                data = JSON.parse(text);
            } catch (parseError) {
                throw new Error(`Invalid JSON response: ${parseError.message}`);
            }

            if (data.success && data.data) {
                // Process and validate the data
                const processedData = this.processGoogleAppsScriptData(data.data);
                console.log('Processed Google Apps Script data:', processedData.length, 'records');

                return {
                    success: true,
                    data: processedData,
                    metadata: {
                        recordCount: processedData.length,
                        timestamp: new Date().toISOString(),
                        source: 'google_apps_script',
                        sheetName: sheetName
                    }
                };
            } else {
                throw new Error(data.error || 'Unknown error from Google Apps Script');
            }
        } catch (error) {
            console.error('Google Apps Script fetch error:', error);
            throw error;
        }
    }

    processGoogleAppsScriptData(rawData) {
        if (!Array.isArray(rawData)) {
            throw new Error('Invalid data format: expected array');
        }

        return rawData.map((record, index) => {
            try {
                // Convert Google Apps Script format to staging API format
                return {
                    employee_id: record.employeeId || `EMP_${index + 1}`,
                    employee_name: record.employeeName || 'Unknown Employee',
                    date: new Date().toISOString().split('T')[0],
                    check_in: '08:00',
                    check_out: '17:00',
                    regular_hours: 7.0,
                    overtime_hours: 0.0,
                    total_hours: 7.0,
                    task_code: '',
                    machine_code: '',
                    expense_code: '',
                    status: 'staged',
                    source: 'google_apps_script',
                    _original: record
                };
            } catch (error) {
                console.error('Error processing Google Apps Script record:', record, error);
                return {
                    employee_id: `ERROR_${index}`,
                    employee_name: 'Processing Error',
                    date: new Date().toISOString().split('T')[0],
                    check_in: '',
                    check_out: '',
                    regular_hours: 0,
                    overtime_hours: 0,
                    total_hours: 0,
                    task_code: '',
                    machine_code: '',
                    expense_code: '',
                    status: 'error',
                    _error: error.message,
                    _original: record
                };
            }
        });
    }

    async getExtensionStatus(sendResponse) {
        try {
            const result = await chrome.storage.local.get(['venusExtensionEnabled']);
            const isEnabled = result.venusExtensionEnabled !== false; // Default to true

            sendResponse({
                success: true,
                data: {
                    isEnabled: isEnabled,
                    version: chrome.runtime.getManifest().version,
                    timestamp: new Date().toISOString()
                }
            });
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    async toggleExtension(sendResponse) {
        try {
            const result = await chrome.storage.local.get(['venusExtensionEnabled']);
            const currentState = result.venusExtensionEnabled !== false;
            const newState = !currentState;

            await chrome.storage.local.set({ venusExtensionEnabled: newState });

            sendResponse({
                success: true,
                data: {
                    isEnabled: newState,
                    message: `Extension ${newState ? 'enabled' : 'disabled'}`
                }
            });
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    async getConfiguration(sendResponse) {
        try {
            const config = await chrome.storage.local.get(['venusConfig']);
            const currentConfig = config.venusConfig || {};
            
            sendResponse({
                success: true,
                config: {
                    scriptUrl: currentConfig.scriptUrl || 'https://script.google.com/macros/s/AKfycbzfhUsf1nHUy3ivBdjK-GkzTjcnM6oSUHKDRUDf9r0zKvvEeQcLl7y85nmXctZqRM7rDg/exec',
                    sheetName: currentConfig.sheetName || 'monthlyGridData_May_2025',
                    targetUrl: currentConfig.targetUrl || '',
                    username: currentConfig.username || '',
                    password: currentConfig.password || '',
                    // API configuration
                    apiEndpoint: 'http://localhost:5173/api/staging/data',
                    apiEnabled: true,
                    fallbackToGoogleAppsScript: true
                }
            });
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    async updateConfiguration(message, sendResponse) {
        try {
            const { data: updates } = message;
            const result = await chrome.storage.local.get(['venusConfig']);
            const currentConfig = result.venusConfig || {};

            // Merge updates with current configuration
            const newConfig = { ...currentConfig, ...updates };
            await chrome.storage.local.set({ venusConfig: newConfig });

            sendResponse({
                success: true,
                data: newConfig,
                message: 'Configuration updated successfully'
            });
        } catch (error) {
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }
}

// Initialize background service worker
new VenusAutoFillBackground();
