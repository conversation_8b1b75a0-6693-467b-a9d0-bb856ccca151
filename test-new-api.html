<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venus Auto Fill - New API Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        
        .api-info {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .api-info h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
        }
        
        .api-info code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            display: block;
            margin: 10px 0;
            padding: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }
        
        .status {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
            font-size: 16px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .results {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .results pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            font-size: 12px;
        }
        
        .data-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .data-table tr:hover {
            background: #e3f2fd;
        }
        
        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metadata-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .metadata-item h4 {
            margin: 0 0 8px 0;
            color: #667eea;
            font-size: 14px;
        }
        
        .metadata-item p {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .employee-summary {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .employee-summary h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .stat-item {
            text-align: center;
            background: white;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #bbdefb;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Venus Auto Fill</h1>
            <p>New Google Apps Script API Testing</p>
        </div>

        <div class="api-info">
            <h3>📡 New API Endpoint</h3>
            <p><strong>Deployed URL:</strong></p>
            <code id="apiUrl">https://script.google.com/macros/s/AKfycbyZoY78RbqqX_YyxSdQrVZzGHU80Go5syIjDKZpMYAXwMkdfTino4mrQqNKK8kOyBttIA/exec</code>
            <p><strong>Expected Response Format:</strong></p>
            <ul>
                <li><code>success</code>: Boolean indicating API call success</li>
                <li><code>data</code>: Array of employee records with daily attendance data</li>
                <li><code>headers</code>: Column headers for the timesheet data</li>
                <li><code>metadata</code>: Spreadsheet information and timestamps</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔗 Basic Connection Tests</h3>
            <button class="btn btn-success" onclick="testBasicConnection()">🚀 Test Basic Connection</button>
            <button class="btn" onclick="testWithParameters()">📋 Test with Parameters</button>
            <button class="btn btn-warning" onclick="testErrorHandling()">❌ Test Error Handling</button>
            <button class="btn btn-danger" onclick="clearResults()">🗑️ Clear Results</button>
            
            <div id="connectionStatus"></div>
            <div id="connectionResults" class="results hidden"></div>
        </div>

        <div class="test-section">
            <h3>📊 Data Fetching & Analysis</h3>
            <button class="btn btn-success" onclick="fetchEmployeeData()">📥 Fetch Employee Data</button>
            <button class="btn" onclick="analyzeDataStructure()">🔍 Analyze Data Structure</button>
            <button class="btn" onclick="processAttendanceData()">⚙️ Process Attendance Data</button>
            <button class="btn" onclick="exportDataAsJSON()">💾 Export as JSON</button>
            
            <div id="dataStatus"></div>
            <div id="dataResults" class="results hidden"></div>
            <div id="metadataDisplay"></div>
            <div id="dataAnalysis"></div>
        </div>

        <div class="test-section">
            <h3>📈 Performance & Reliability Tests</h3>
            <button class="btn" onclick="performanceTest()">⚡ Performance Test</button>
            <button class="btn" onclick="reliabilityTest()">🔄 Reliability Test (5x)</button>
            <button class="btn" onclick="timeoutTest()">⏱️ Timeout Test</button>
            <button class="btn" onclick="concurrencyTest()">🚦 Concurrency Test</button>
            
            <div id="performanceStatus"></div>
            <div id="performanceResults" class="results hidden"></div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_CONFIG = {
            baseUrl: 'https://script.google.com/macros/s/AKfycbyZoY78RbqqX_YyxSdQrVZzGHU80Go5syIjDKZpMYAXwMkdfTino4mrQqNKK8kOyBttIA/exec',
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 1000
        };

        // Global variables for storing test results
        let lastApiResponse = null;
        let performanceMetrics = [];

        // Utility Functions
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showResults(elementId, data) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            element.classList.remove('hidden');
        }

        function hideResults(elementId) {
            const element = document.getElementById(elementId);
            element.classList.add('hidden');
        }

        function logMetric(operation, duration, success, details = {}) {
            performanceMetrics.push({
                operation,
                duration,
                success,
                timestamp: new Date().toISOString(),
                ...details
            });
        }

        // API Request Functions
        async function makeApiRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                mode: 'cors',
                ...options
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);

            try {
                defaultOptions.signal = controller.signal;
                
                console.log('📡 Making API request to:', url);
                const startTime = Date.now();
                
                const response = await fetch(url, defaultOptions);
                const duration = Date.now() - startTime;
                
                clearTimeout(timeoutId);
                
                console.log('📡 Response received:', {
                    status: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type'),
                    duration: `${duration}ms`
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseText = await response.text();
                console.log('📄 Response length:', responseText.length);

                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('✅ JSON parsed successfully');
                } catch (parseError) {
                    console.error('❌ JSON parse error:', parseError);
                    console.log('📄 Response preview:', responseText.substring(0, 500));
                    throw new Error(`Invalid JSON response: ${parseError.message}`);
                }

                return { result, duration, responseSize: responseText.length };

            } catch (error) {
                clearTimeout(timeoutId);
                
                if (error.name === 'AbortError') {
                    throw new Error(`Request timeout after ${API_CONFIG.timeout / 1000} seconds`);
                }
                
                throw error;
            }
        }

        // Test Functions
        async function testBasicConnection() {
            showStatus('connectionStatus', '<div class="loading"></div>Testing basic connection...', 'info');
            hideResults('connectionResults');

            try {
                const startTime = Date.now();
                const { result, duration, responseSize } = await makeApiRequest(API_CONFIG.baseUrl);
                
                console.log('🎉 Basic connection test successful:', result);
                
                if (result.success === true) {
                    showStatus('connectionStatus', 
                        `✅ <strong>Connection Successful!</strong><br>` +
                        `⚡ Response Time: ${duration}ms<br>` +
                        `📦 Response Size: ${responseSize} bytes<br>` +
                        `📊 Data Records: ${result.data ? result.data.length : 'N/A'}<br>` +
                        `📋 Headers: ${result.headers ? result.headers.length : 'N/A'} columns`, 'success');
                } else {
                    showStatus('connectionStatus', 
                        `⚠️ <strong>API Responded but with Error</strong><br>` +
                        `Error: ${result.error || 'Unknown error'}`, 'warning');
                }

                showResults('connectionResults', result);
                lastApiResponse = result;
                logMetric('Basic Connection', duration, result.success === true, { responseSize });

            } catch (error) {
                console.error('❌ Basic connection test failed:', error);
                showStatus('connectionStatus', 
                    `❌ <strong>Connection Failed</strong><br>` +
                    `Error: ${error.message}`, 'error');
                
                showResults('connectionResults', { error: error.message, timestamp: new Date().toISOString() });
                logMetric('Basic Connection', 0, false, { error: error.message });
            }
        }

        async function testWithParameters() {
            showStatus('connectionStatus', '<div class="loading"></div>Testing with parameters...', 'info');
            
            const testParams = [
                { action: 'test', sheet: 'monthlyGridData_May_2025' },
                { action: 'getData', sheet: 'monthlyGridData_May_2025' },
                { action: 'getSheets' }
            ];

            try {
                const results = [];
                
                for (const params of testParams) {
                    const url = new URL(API_CONFIG.baseUrl);
                    Object.entries(params).forEach(([key, value]) => {
                        url.searchParams.set(key, value);
                    });
                    
                    console.log(`🔄 Testing with parameters:`, params);
                    const { result, duration } = await makeApiRequest(url.toString());
                    
                    results.push({
                        parameters: params,
                        response: result,
                        duration,
                        success: result.success === true
                    });
                }

                const successCount = results.filter(r => r.success).length;
                showStatus('connectionStatus', 
                    `✅ <strong>Parameter Tests Completed</strong><br>` +
                    `Successful: ${successCount}/${results.length} tests<br>` +
                    `Average Response Time: ${Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length)}ms`, 
                    successCount === results.length ? 'success' : 'warning');

                showResults('connectionResults', results);
                logMetric('Parameter Tests', 0, successCount === results.length, { successRate: `${successCount}/${results.length}` });

            } catch (error) {
                console.error('❌ Parameter test failed:', error);
                showStatus('connectionStatus', 
                    `❌ <strong>Parameter Test Failed</strong><br>` +
                    `Error: ${error.message}`, 'error');
            }
        }

        async function fetchEmployeeData() {
            showStatus('dataStatus', '<div class="loading"></div>Fetching employee timesheet data...', 'info');
            hideResults('dataResults');

            try {
                const url = new URL(API_CONFIG.baseUrl);
                url.searchParams.set('action', 'getData');
                url.searchParams.set('sheet', 'monthlyGridData_May_2025');

                const { result, duration } = await makeApiRequest(url.toString());
                
                if (result.success === true) {
                    const { data, headers, metadata } = result;
                    
                    showStatus('dataStatus', 
                        `✅ <strong>Employee Data Fetched Successfully!</strong><br>` +
                        `📊 Employees: ${data.length}<br>` +
                        `📋 Columns: ${headers ? headers.length : 'N/A'}<br>` +
                        `⚡ Response Time: ${duration}ms`, 'success');

                    // Display metadata
                    displayMetadata(metadata);
                    
                    // Display data table
                    displayEmployeeTable(data, headers);
                    
                    // Show raw response
                    showResults('dataResults', result);
                    
                    lastApiResponse = result;
                    logMetric('Employee Data Fetch', duration, true, { employeeCount: data.length });

                } else {
                    throw new Error(result.error || 'Failed to fetch employee data');
                }

            } catch (error) {
                console.error('❌ Employee data fetch failed:', error);
                showStatus('dataStatus', 
                    `❌ <strong>Data Fetch Failed</strong><br>` +
                    `Error: ${error.message}`, 'error');
                logMetric('Employee Data Fetch', 0, false, { error: error.message });
            }
        }

        function displayMetadata(metadata) {
            if (!metadata) return;

            const metadataHtml = `
                <div class="metadata-grid">
                    <div class="metadata-item">
                        <h4>📋 Sheet Information</h4>
                        <p>${metadata.sheetName || 'N/A'}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>📊 Data Dimensions</h4>
                        <p>${metadata.rows || 0} rows × ${metadata.columns || 0} cols</p>
                    </div>
                    <div class="metadata-item">
                        <h4>📅 Last Updated</h4>
                        <p>${metadata.lastUpdated ? new Date(metadata.lastUpdated).toLocaleString() : 'N/A'}</p>
                    </div>
                    <div class="metadata-item">
                        <h4>🆔 Spreadsheet ID</h4>
                        <p>${metadata.spreadsheetId ? metadata.spreadsheetId.substring(0, 20) + '...' : 'N/A'}</p>
                    </div>
                </div>
            `;
            
            document.getElementById('metadataDisplay').innerHTML = metadataHtml;
        }

        function displayEmployeeTable(data, headers) {
            if (!data || data.length === 0) {
                document.getElementById('dataAnalysis').innerHTML = '<p>No employee data to display</p>';
                return;
            }

            // Create summary statistics
            const totalEmployees = data.length;
            const maxDays = Math.max(...data.map(emp => emp.length - 2)); // Subtract ID and Name columns
            
            let tableHtml = `
                <div class="employee-summary">
                    <h4>📊 Employee Timesheet Summary</h4>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-value">${totalEmployees}</div>
                            <div class="stat-label">Employees</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${maxDays}</div>
                            <div class="stat-label">Max Days</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${headers ? headers.length : 'N/A'}</div>
                            <div class="stat-label">Columns</div>
                        </div>
                    </div>
                </div>
            `;

            // Create table
            tableHtml += '<table class="data-table">';
            
            // Table headers
            if (headers && headers.length > 0) {
                tableHtml += '<thead><tr>';
                headers.forEach(header => {
                    tableHtml += `<th>${header}</th>`;
                });
                tableHtml += '</tr></thead>';
            }
            
            // Table body (show first 10 employees)
            tableHtml += '<tbody>';
            const displayData = data.slice(0, 10);
            
            displayData.forEach(employee => {
                tableHtml += '<tr>';
                if (Array.isArray(employee)) {
                    employee.forEach((cell, index) => {
                        // Highlight attendance data (regular | overtime format)
                        const cellValue = cell || '';
                        const isAttendanceData = typeof cellValue === 'string' && cellValue.includes('|');
                        const displayValue = isAttendanceData ? 
                            `<span class="highlight">${cellValue}</span>` : cellValue;
                        tableHtml += `<td>${displayValue}</td>`;
                    });
                } else {
                    tableHtml += `<td colspan="100%">Invalid data format</td>`;
                }
                tableHtml += '</tr>';
            });
            
            tableHtml += '</tbody></table>';
            
            if (data.length > 10) {
                tableHtml += `<p style="text-align: center; color: #666; font-style: italic;">
                    ... and ${data.length - 10} more employees
                </p>`;
            }
            
            document.getElementById('dataAnalysis').innerHTML = tableHtml;
        }

        async function analyzeDataStructure() {
            if (!lastApiResponse || !lastApiResponse.data) {
                showStatus('dataStatus', '⚠️ No data available. Please fetch employee data first.', 'warning');
                return;
            }

            showStatus('dataStatus', '<div class="loading"></div>Analyzing data structure...', 'info');

            const { data, headers } = lastApiResponse;
            
            const analysis = {
                overview: {
                    totalRecords: data.length,
                    columnsCount: headers ? headers.length : 0,
                    dataTypes: {}
                },
                structure: {
                    headers: headers || [],
                    sampleRecord: data[0] || null,
                    attendanceFormat: {}
                },
                validation: {
                    hasEmployeeId: false,
                    hasEmployeeName: false,
                    hasAttendanceData: false,
                    attendanceFormatConsistent: true
                }
            };

            // Analyze data types and structure
            if (data.length > 0) {
                const firstRecord = data[0];
                if (Array.isArray(firstRecord)) {
                    analysis.validation.hasEmployeeId = firstRecord.length > 0;
                    analysis.validation.hasEmployeeName = firstRecord.length > 1;
                    analysis.validation.hasAttendanceData = firstRecord.length > 2;
                    
                    // Analyze attendance data format
                    const attendanceColumns = firstRecord.slice(2);
                    let pipeFormatCount = 0;
                    let singleFormatCount = 0;
                    let emptyCount = 0;
                    
                    attendanceColumns.forEach(cell => {
                        if (!cell || cell === '') {
                            emptyCount++;
                        } else if (typeof cell === 'string') {
                            if (cell.includes('|')) {
                                pipeFormatCount++;
                            } else {
                                singleFormatCount++;
                            }
                        }
                    });
                    
                    analysis.structure.attendanceFormat = {
                        pipeFormat: pipeFormatCount,
                        singleFormat: singleFormatCount,
                        empty: emptyCount,
                        total: attendanceColumns.length
                    };
                }
            }

            showStatus('dataStatus', 
                `✅ <strong>Data Structure Analysis Complete</strong><br>` +
                `📊 Records: ${analysis.overview.totalRecords}<br>` +
                `📋 Columns: ${analysis.overview.columnsCount}<br>` +
                `✓ Employee ID: ${analysis.validation.hasEmployeeId}<br>` +
                `✓ Attendance Data: ${analysis.validation.hasAttendanceData}`, 'success');

            showResults('dataResults', analysis);
        }

        async function processAttendanceData() {
            if (!lastApiResponse || !lastApiResponse.data) {
                showStatus('dataStatus', '⚠️ No data available. Please fetch employee data first.', 'warning');
                return;
            }

            showStatus('dataStatus', '<div class="loading"></div>Processing attendance data...', 'info');

            const { data } = lastApiResponse;
            const processedEmployees = [];

            data.forEach((employee, index) => {
                if (!Array.isArray(employee) || employee.length < 3) {
                    console.warn(`Skipping invalid employee record ${index}:`, employee);
                    return;
                }

                const employeeId = employee[0];
                const employeeName = employee[1];
                const attendanceData = employee.slice(2);

                const processedAttendance = attendanceData.map((dayData, dayIndex) => {
                    return parseAttendanceEntry(dayData, dayIndex + 1);
                });

                const totalRegular = processedAttendance.reduce((sum, day) => sum + (day.regular || 0), 0);
                const totalOvertime = processedAttendance.reduce((sum, day) => sum + (day.overtime || 0), 0);
                const workingDays = processedAttendance.filter(day => day.regular > 0 || day.overtime > 0).length;

                processedEmployees.push({
                    employeeId,
                    employeeName,
                    attendanceData: processedAttendance,
                    summary: {
                        totalRegular: totalRegular,
                        totalOvertime: totalOvertime,
                        totalHours: totalRegular + totalOvertime,
                        workingDays: workingDays
                    },
                    rawData: attendanceData
                });
            });

            const processingResults = {
                processedCount: processedEmployees.length,
                totalEmployees: data.length,
                skippedCount: data.length - processedEmployees.length,
                overallStats: {
                    totalWorkingHours: processedEmployees.reduce((sum, emp) => sum + emp.summary.totalHours, 0),
                    averageHoursPerEmployee: processedEmployees.length > 0 ? 
                        (processedEmployees.reduce((sum, emp) => sum + emp.summary.totalHours, 0) / processedEmployees.length).toFixed(2) : 0
                },
                sampleProcessedEmployee: processedEmployees[0] || null
            };

            showStatus('dataStatus', 
                `✅ <strong>Attendance Data Processed</strong><br>` +
                `👥 Processed: ${processingResults.processedCount}/${processingResults.totalEmployees}<br>` +
                `⏰ Total Hours: ${processingResults.overallStats.totalWorkingHours}<br>` +
                `📊 Avg Hours/Employee: ${processingResults.overallStats.averageHoursPerEmployee}`, 'success');

            showResults('dataResults', processingResults);
        }

        function parseAttendanceEntry(dayData, dayNumber) {
            if (!dayData || typeof dayData !== 'string') {
                return { 
                    day: dayNumber,
                    regular: 0, 
                    overtime: 0, 
                    raw: dayData,
                    status: 'empty'
                };
            }

            // Format: "(7) | (7.5)" where first is regular hours, second is overtime
            const pipeMatch = dayData.match(/\(([^)]+)\)\s*\|\s*\(([^)]+)\)/);
            if (pipeMatch) {
                return {
                    day: dayNumber,
                    regular: parseFloat(pipeMatch[1]) || 0,
                    overtime: parseFloat(pipeMatch[2]) || 0,
                    raw: dayData,
                    status: 'complete'
                };
            }

            // Try to parse single number in parentheses
            const singleMatch = dayData.match(/\(([^)]+)\)/);
            if (singleMatch) {
                return {
                    day: dayNumber,
                    regular: parseFloat(singleMatch[1]) || 0,
                    overtime: 0,
                    raw: dayData,
                    status: 'regular_only'
                };
            }

            return { 
                day: dayNumber,
                regular: 0, 
                overtime: 0, 
                raw: dayData,
                status: 'invalid'
            };
        }

        async function testErrorHandling() {
            showStatus('connectionStatus', '<div class="loading"></div>Testing error handling...', 'info');

            const errorTests = [
                { name: 'Invalid URL', url: 'https://invalid-domain.com/test' },
                { name: 'Invalid Action', url: API_CONFIG.baseUrl + '?action=invalidAction' },
                { name: 'Invalid Sheet', url: API_CONFIG.baseUrl + '?action=getData&sheet=nonexistentSheet' }
            ];

            const results = [];

            for (const test of errorTests) {
                try {
                    console.log(`🧪 Running error test: ${test.name}`);
                    const { result, duration } = await makeApiRequest(test.url);
                    results.push({
                        testName: test.name,
                        success: false,
                        expectedError: true,
                        actualResult: result,
                        duration
                    });
                } catch (error) {
                    results.push({
                        testName: test.name,
                        success: true,
                        expectedError: true,
                        error: error.message,
                        handledCorrectly: true
                    });
                }
            }

            const handledCorrectly = results.filter(r => r.success).length;
            showStatus('connectionStatus', 
                `✅ <strong>Error Handling Tests Complete</strong><br>` +
                `Handled Correctly: ${handledCorrectly}/${results.length}<br>` +
                `Error handling is ${handledCorrectly === results.length ? 'working properly' : 'needs improvement'}`, 
                handledCorrectly === results.length ? 'success' : 'warning');

            showResults('connectionResults', results);
        }

        async function performanceTest() {
            showStatus('performanceStatus', '<div class="loading"></div>Running performance test...', 'info');
            
            const iterations = 5;
            const results = [];

            for (let i = 0; i < iterations; i++) {
                try {
                    console.log(`🏃 Performance test iteration ${i + 1}/${iterations}`);
                    const { result, duration } = await makeApiRequest(API_CONFIG.baseUrl);
                    results.push({
                        iteration: i + 1,
                        duration,
                        success: result.success === true,
                        dataSize: result.data ? result.data.length : 0
                    });
                } catch (error) {
                    results.push({
                        iteration: i + 1,
                        duration: 0,
                        success: false,
                        error: error.message
                    });
                }

                // Add small delay between requests
                if (i < iterations - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            const successfulResults = results.filter(r => r.success);
            const avgDuration = successfulResults.length > 0 ? 
                Math.round(successfulResults.reduce((sum, r) => sum + r.duration, 0) / successfulResults.length) : 0;
            const minDuration = successfulResults.length > 0 ? Math.min(...successfulResults.map(r => r.duration)) : 0;
            const maxDuration = successfulResults.length > 0 ? Math.max(...successfulResults.map(r => r.duration)) : 0;

            const performanceResults = {
                summary: {
                    totalTests: iterations,
                    successful: successfulResults.length,
                    failed: iterations - successfulResults.length,
                    successRate: `${successfulResults.length}/${iterations}`,
                    averageResponseTime: avgDuration,
                    minResponseTime: minDuration,
                    maxResponseTime: maxDuration
                },
                details: results
            };

            showStatus('performanceStatus', 
                `✅ <strong>Performance Test Complete</strong><br>` +
                `Success Rate: ${performanceResults.summary.successRate}<br>` +
                `Average Response: ${avgDuration}ms<br>` +
                `Range: ${minDuration}ms - ${maxDuration}ms`, 'success');

            showResults('performanceResults', performanceResults);
            logMetric('Performance Test', avgDuration, successfulResults.length === iterations, performanceResults.summary);
        }

        async function reliabilityTest() {
            showStatus('performanceStatus', '<div class="loading"></div>Running reliability test (5 requests)...', 'info');
            
            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(makeApiRequest(API_CONFIG.baseUrl));
            }

            try {
                const results = await Promise.all(promises.map(p => p.catch(e => ({ error: e.message }))));
                const successful = results.filter(r => r.result && r.result.success === true).length;
                
                showStatus('performanceStatus', 
                    `✅ <strong>Reliability Test Complete</strong><br>` +
                    `Concurrent Success: ${successful}/5<br>` +
                    `API handled concurrent requests ${successful === 5 ? 'perfectly' : 'with some issues'}`, 
                    successful >= 4 ? 'success' : 'warning');

                showResults('performanceResults', { concurrentResults: results, successRate: `${successful}/5` });

            } catch (error) {
                showStatus('performanceStatus', `❌ Reliability test failed: ${error.message}`, 'error');
            }
        }

        function exportDataAsJSON() {
            if (!lastApiResponse) {
                showStatus('dataStatus', '⚠️ No data available to export. Please fetch data first.', 'warning');
                return;
            }

            const exportData = {
                timestamp: new Date().toISOString(),
                source: 'Venus Auto Fill API Test',
                apiUrl: API_CONFIG.baseUrl,
                response: lastApiResponse,
                performanceMetrics: performanceMetrics
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `venus-auto-fill-test-data-${Date.now()}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            showStatus('dataStatus', '💾 Test data exported successfully!', 'success');
        }

        function clearResults() {
            hideResults('connectionResults');
            hideResults('dataResults');
            hideResults('performanceResults');
            document.getElementById('connectionStatus').innerHTML = '';
            document.getElementById('dataStatus').innerHTML = '';
            document.getElementById('performanceStatus').innerHTML = '';
            document.getElementById('metadataDisplay').innerHTML = '';
            document.getElementById('dataAnalysis').innerHTML = '';
            lastApiResponse = null;
            console.log('🗑️ Results cleared');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Venus Auto Fill API Test Page Loaded');
            console.log('📡 API URL:', API_CONFIG.baseUrl);
        });
    </script>
</body>
</html> 