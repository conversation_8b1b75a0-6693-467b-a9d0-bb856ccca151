/**
 * Auto Form Fill Pro - API Service
 * Handles all communication with the local API
 */

import { Logger } from '../utils/logger.js';
import { CryptoUtils } from '../utils/crypto.js';

export class ApiService {
  constructor() {
    this.logger = new Logger('ApiService');
    this.config = {
      baseUrl: 'http://localhost:3000',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000
    };
    this.authToken = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the API service
   */
  async initialize() {
    try {
      // Load stored credentials and configuration
      await this.loadConfiguration();
      
      // Attempt to authenticate if credentials are available
      if (this.config.credentials) {
        await this.authenticate();
      }
      
      this.isInitialized = true;
      this.logger.info('API service initialized');
    } catch (error) {
      this.logger.error('Failed to initialize API service:', error);
      throw error;
    }
  }

  /**
   * Load configuration from storage
   */
  async loadConfiguration() {
    try {
      const stored = await chrome.storage.local.get(['apiConfiguration']);
      
      if (stored.apiConfiguration) {
        this.config = { ...this.config, ...stored.apiConfiguration };
        
        // Decrypt credentials if they exist
        if (this.config.encryptedCredentials) {
          this.config.credentials = await CryptoUtils.decrypt(this.config.encryptedCredentials);
        }
      }
      
      this.logger.info('Configuration loaded');
    } catch (error) {
      this.logger.error('Failed to load configuration:', error);
    }
  }

  /**
   * Update API configuration
   */
  async updateConfiguration(newConfig) {
    try {
      this.config = { ...this.config, ...newConfig };
      
      // Encrypt credentials before storing
      const configToStore = { ...this.config };
      
      if (configToStore.credentials) {
        configToStore.encryptedCredentials = await CryptoUtils.encrypt(configToStore.credentials);
        delete configToStore.credentials;
      }
      
      await chrome.storage.local.set({ apiConfiguration: configToStore });
      
      // Re-authenticate if credentials changed
      if (newConfig.credentials) {
        await this.authenticate();
      }
      
      this.logger.info('Configuration updated');
    } catch (error) {
      this.logger.error('Failed to update configuration:', error);
      throw error;
    }
  }

  /**
   * Authenticate with the API
   */
  async authenticate() {
    try {
      if (!this.config.credentials) {
        throw new Error('No credentials configured');
      }

      const response = await this.makeRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify(this.config.credentials),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.token) {
        this.authToken = response.token;
        this.logger.info('Authentication successful');
        return true;
      } else {
        throw new Error('Authentication failed: No token received');
      }
    } catch (error) {
      this.logger.error('Authentication error:', error);
      this.authToken = null;
      throw error;
    }
  }

  /**
   * Test API connection
   */
  async testConnection() {
    try {
      const response = await this.makeRequest('/health', {
        method: 'GET'
      });
      
      return {
        success: true,
        status: response.status || 'ok',
        timestamp: new Date().toISOString(),
        latency: response.latency
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get form data from API
   */
  async getFormData(options = {}) {
    try {
      if (!this.isAuthenticated()) {
        await this.authenticate();
      }

      const queryParams = new URLSearchParams();
      
      // Add query parameters based on options
      if (options.formType) {
        queryParams.append('type', options.formType);
      }
      if (options.userId) {
        queryParams.append('userId', options.userId);
      }
      if (options.context) {
        queryParams.append('context', JSON.stringify(options.context));
      }

      const url = `/form-data${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      
      const response = await this.makeRequest(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      // Process and validate the response
      return this.processFormData(response);
    } catch (error) {
      this.logger.error('Failed to get form data:', error);
      throw error;
    }
  }

  /**
   * Submit form data to API
   */
  async submitFormData(formData, options = {}) {
    try {
      if (!this.isAuthenticated()) {
        await this.authenticate();
      }

      const response = await this.makeRequest('/form-data', {
        method: 'POST',
        body: JSON.stringify({
          formData,
          context: options.context,
          metadata: {
            url: options.url,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          }
        }),
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json'
        }
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to submit form data:', error);
      throw error;
    }
  }

  /**
   * Get field mappings from API
   */
  async getFieldMappings(options = {}) {
    try {
      if (!this.isAuthenticated()) {
        await this.authenticate();
      }

      const queryParams = new URLSearchParams();
      
      if (options.domain) {
        queryParams.append('domain', options.domain);
      }
      if (options.formSignature) {
        queryParams.append('signature', options.formSignature);
      }

      const url = `/field-mappings${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      
      const response = await this.makeRequest(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      return response.mappings || {};
    } catch (error) {
      this.logger.error('Failed to get field mappings:', error);
      return {};
    }
  }

  /**
   * Update field mappings
   */
  async updateFieldMappings(mappings, options = {}) {
    try {
      if (!this.isAuthenticated()) {
        await this.authenticate();
      }

      const response = await this.makeRequest('/field-mappings', {
        method: 'PUT',
        body: JSON.stringify({
          mappings,
          domain: options.domain,
          formSignature: options.formSignature
        }),
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json'
        }
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to update field mappings:', error);
      throw error;
    }
  }

  /**
   * Make HTTP request with retry logic
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.config.baseUrl}${endpoint}`;
    let lastError;

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const startTime = Date.now();
        
        const response = await this.fetchWithTimeout(url, {
          ...options,
          timeout: this.config.timeout
        });

        const endTime = Date.now();
        const latency = endTime - startTime;

        if (!response.ok) {
          const errorText = await response.text();
          let errorData;
          
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = { message: errorText };
          }

          // Handle specific HTTP errors
          if (response.status === 401) {
            this.authToken = null;
            throw new Error('Authentication required');
          } else if (response.status === 403) {
            throw new Error('Access forbidden');
          } else if (response.status >= 500) {
            throw new Error(`Server error: ${errorData.message || response.statusText}`);
          } else {
            throw new Error(`API error: ${errorData.message || response.statusText}`);
          }
        }

        const data = await response.json();
        data.latency = latency;
        
        if (attempt > 1) {
          this.logger.info(`Request succeeded on attempt ${attempt}`);
        }
        
        return data;
      } catch (error) {
        lastError = error;
        
        this.logger.warn(`Request attempt ${attempt} failed:`, error.message);
        
        // Don't retry for certain types of errors
        if (this.isNonRetryableError(error) || attempt === this.config.retryAttempts) {
          throw error;
        }
        
        // Wait before retrying with exponential backoff
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
        await this.delay(delay);
      }
    }

    throw lastError;
  }

  /**
   * Fetch with timeout support
   */
  async fetchWithTimeout(url, options = {}) {
    const { timeout = 30000, ...fetchOptions } = options;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${timeout}ms`);
      }
      
      throw error;
    }
  }

  /**
   * Process form data response
   */
  processFormData(data) {
    try {
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid form data response');
      }

      // Validate required fields
      const processedData = {
        fields: data.fields || {},
        mappings: data.mappings || {},
        metadata: data.metadata || {},
        timestamp: data.timestamp || new Date().toISOString()
      };

      // Validate field data
      Object.keys(processedData.fields).forEach(fieldName => {
        const fieldData = processedData.fields[fieldName];
        
        if (typeof fieldData !== 'object' || fieldData === null) {
          this.logger.warn(`Invalid field data for ${fieldName}:`, fieldData);
          delete processedData.fields[fieldName];
        }
      });

      return processedData;
    } catch (error) {
      this.logger.error('Error processing form data:', error);
      throw error;
    }
  }

  /**
   * Check if error should not be retried
   */
  isNonRetryableError(error) {
    const nonRetryableErrors = [
      'Authentication required',
      'Access forbidden',
      'Invalid form data response',
      'No credentials configured'
    ];
    
    return nonRetryableErrors.some(msg => error.message.includes(msg));
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    if (!this.authToken) {
      return {};
    }
    
    return {
      'Authorization': `Bearer ${this.authToken}`
    };
  }

  /**
   * Check if authenticated
   */
  isAuthenticated() {
    return !!this.authToken;
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isAuthenticated: this.isAuthenticated(),
      baseUrl: this.config.baseUrl,
      hasCredentials: !!this.config.credentials
    };
  }

  /**
   * Delay utility
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Refresh authentication token
   */
  async refreshToken() {
    try {
      if (!this.config.credentials) {
        throw new Error('No credentials available for token refresh');
      }

      const response = await this.makeRequest('/auth/refresh', {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (response.token) {
        this.authToken = response.token;
        this.logger.info('Token refreshed successfully');
        return true;
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      this.logger.error('Token refresh error:', error);
      this.authToken = null;
      throw error;
    }
  }

  /**
   * Logout and clear authentication
   */
  async logout() {
    try {
      if (this.authToken) {
        await this.makeRequest('/auth/logout', {
          method: 'POST',
          headers: this.getAuthHeaders()
        });
      }
    } catch (error) {
      this.logger.warn('Logout request failed:', error);
    } finally {
      this.authToken = null;
      this.logger.info('Logged out successfully');
    }
  }
} 