<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Flow Interface</title>
    <style>
        .visual-flow-container {
            width: 100%;
            height: 600px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 12px;
            padding: 20px;
            position: relative;
            overflow-y: auto;
            border: 2px solid #e1e5e9;
        }

        .flow-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .flow-title {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            margin: 0;
        }

        .flow-subtitle {
            font-size: 14px;
            color: #718096;
            margin: 5px 0 0 0;
        }

        .flow-step {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 10px auto;
            width: 280px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .flow-step:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .flow-step.pending {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
        }

        .flow-step.executing {
            border-color: #ff4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fff5f5 100%);
            animation: pulse-executing 1.5s infinite;
        }

        .flow-step.completed {
            border-color: #22c55e;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        }

        .flow-step.error {
            border-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
        }

        .flow-step.skipped {
            border-color: #9ca3af;
            background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
            opacity: 0.7;
        }

        @keyframes pulse-executing {
            0% {
                box-shadow: 0 4px 12px rgba(255, 68, 68, 0.2);
                border-color: #ff4444;
            }
            50% {
                box-shadow: 0 8px 24px rgba(255, 68, 68, 0.4);
                border-color: #ff6666;
                transform: translateY(-3px);
            }
            100% {
                box-shadow: 0 4px 12px rgba(255, 68, 68, 0.2);
                border-color: #ff4444;
            }
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .step-icon {
            font-size: 24px;
            margin-right: 12px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(59, 130, 246, 0.1);
        }

        .step-info {
            flex: 1;
        }

        .step-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .step-subtitle {
            font-size: 12px;
            color: #6b7280;
            margin: 2px 0 0 0;
        }

        .step-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #d1d5db;
            background: white;
            position: relative;
        }

        .step-status.pending {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .step-status.executing {
            background: #ff4444;
            border-color: #ff4444;
            animation: spin 1s linear infinite;
        }

        .step-status.completed {
            background: #22c55e;
            border-color: #22c55e;
        }

        .step-status.error {
            background: #ef4444;
            border-color: #ef4444;
        }

        .step-status.completed::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .step-status.error::after {
            content: '✕';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .step-description {
            font-size: 13px;
            color: #4b5563;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #e5e7eb;
        }

        .step-connector {
            width: 3px;
            height: 20px;
            background: #d1d5db;
            margin: 0 auto;
            position: relative;
        }

        .step-connector.active {
            background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
        }

        .step-connector.completed {
            background: linear-gradient(to bottom, #22c55e, #16a34a);
        }

        .step-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }

        .step-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            width: 0%;
            transition: width 0.3s ease;
        }

        .flow-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.9);
            color: #374151;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: white;
            transform: translateY(-1px);
        }

        .control-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .control-btn.primary:hover {
            background: #2563eb;
        }

        .execution-summary {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .summary-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }

        .summary-progress {
            margin-top: 8px;
        }

        .summary-progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .summary-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            width: 0%;
            transition: width 0.3s ease;
        }

        .step-timing {
            font-size: 11px;
            color: #9ca3af;
            margin-top: 4px;
        }

        .conditional-indicator {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background: #ffa500;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }

        .conditional-indicator::after {
            content: '?';
        }

        .step-details {
            font-size: 11px;
            color: #6b7280;
            margin-top: 6px;
            padding: 6px;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 4px;
            border-left: 3px solid #e5e7eb;
        }

        .step-details.show {
            display: block;
        }

        .step-details.hide {
            display: none;
        }
    </style>
</head>
<body>
    <div class="visual-flow-container" id="visualFlowContainer">
        <div class="flow-header">
            <h3 class="flow-title" id="flowTitle">Millware Login & Task Register Automation</h3>
            <p class="flow-subtitle" id="flowSubtitle">Automated login sequence untuk Millware system</p>
        </div>

        <div class="flow-controls">
            <button class="control-btn" id="pauseBtn" onclick="pauseExecution()">⏸️ Jeda</button>
            <button class="control-btn" id="stopBtn" onclick="stopExecution()">⏹️ Berhenti</button>
            <button class="control-btn primary" id="startBtn" onclick="startExecution()">▶️ Mulai</button>
        </div>

        <div id="flowStepsContainer">
            <!-- Flow steps will be dynamically generated here -->
        </div>

        <div class="execution-summary" id="executionSummary">
            <div class="summary-stats">
                <span>Progress: <span id="progressText">0/9 steps</span></span>
                <span>Waktu: <span id="executionTime">00:00</span></span>
                <span>Status: <span id="executionStatus">Siap</span></span>
            </div>
            <div class="summary-progress">
                <div class="summary-progress-bar">
                    <div class="summary-progress-fill" id="summaryProgressFill"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Visual Flow Controller
        class VisualFlowController {
            constructor() {
                this.currentStep = 0;
                this.totalSteps = 0;
                this.isExecuting = false;
                this.isPaused = false;
                this.startTime = null;
                this.steps = [];
                this.timers = [];
                
                this.init();
            }

            init() {
                this.loadFlowDefinition();
                this.setupEventListeners();
                this.updateDisplay();
            }

            async loadFlowDefinition() {
                try {
                    // Load the flow definition (normally from JSON file or API)
                    this.flowDefinition = {
                        name: "Millware Login & Task Register Automation",
                        description: "Automated login sequence untuk Millware system",
                        steps: [
                            { id: "step_1", icon: "🌐", title: "Buka Halaman Login", subtitle: "Navigasi ke sistem Millware", description: "Membuka halaman login Millware system" },
                            { id: "step_2", icon: "⏳", title: "Tunggu Halaman Dimuat", subtitle: "Memastikan semua elemen siap", description: "Menunggu halaman login selesai dimuat" },
                            { id: "step_3", icon: "👤", title: "Input Username", subtitle: "Masukkan nama pengguna", description: "Mengisi field username dengan kredensial yang valid" },
                            { id: "step_4", icon: "🔒", title: "Input Password", subtitle: "Masukkan kata sandi", description: "Mengisi field password dengan kredensial yang valid" },
                            { id: "step_5", icon: "🚀", title: "Klik Login", subtitle: "Autentikasi ke sistem", description: "Klik tombol login untuk melakukan autentikasi" },
                            { id: "step_6", icon: "💬", title: "Handle Popup", subtitle: "Tutup popup jika ada", description: "Menangani popup login jika muncul setelah login", conditional: true },
                            { id: "step_7", icon: "📋", title: "Buka Task Register", subtitle: "Navigasi ke halaman tugas", description: "Navigasi ke halaman Task Register List" },
                            { id: "step_8", icon: "⏳", title: "Tunggu Halaman Task", subtitle: "Memastikan halaman siap", description: "Menunggu halaman Task Register selesai dimuat" },
                            { id: "step_9", icon: "➕", title: "Klik Tombol New", subtitle: "Buat task register baru", description: "Klik tombol New untuk membuat task register baru" }
                        ]
                    };
                    
                    this.steps = this.flowDefinition.steps;
                    this.totalSteps = this.steps.length;
                    this.renderFlowSteps();
                } catch (error) {
                    console.error('Error loading flow definition:', error);
                }
            }

            renderFlowSteps() {
                const container = document.getElementById('flowStepsContainer');
                container.innerHTML = '';

                this.steps.forEach((step, index) => {
                    // Create step element
                    const stepElement = document.createElement('div');
                    stepElement.className = 'flow-step';
                    stepElement.id = `flow-step-${index}`;
                    
                    stepElement.innerHTML = `
                        <div class="step-header">
                            <div class="step-icon">${step.icon}</div>
                            <div class="step-info">
                                <h4 class="step-title">${step.title}</h4>
                                <p class="step-subtitle">${step.subtitle}</p>
                            </div>
                            <div class="step-status" id="status-${index}"></div>
                        </div>
                        <div class="step-description">${step.description}</div>
                        <div class="step-timing" id="timing-${index}"></div>
                        <div class="step-details hide" id="details-${index}">
                            <strong>Selector:</strong> <code>${step.selector || 'N/A'}</code><br>
                            <strong>Action:</strong> ${step.action || 'N/A'}
                        </div>
                        <div class="step-progress">
                            <div class="step-progress-bar" id="progress-${index}"></div>
                        </div>
                        ${step.conditional ? '<div class="conditional-indicator"></div>' : ''}
                    `;

                    // Add click handler for step details
                    stepElement.addEventListener('click', () => {
                        this.toggleStepDetails(index);
                    });

                    container.appendChild(stepElement);

                    // Add connector (except for last step)
                    if (index < this.steps.length - 1) {
                        const connector = document.createElement('div');
                        connector.className = 'step-connector';
                        connector.id = `connector-${index}`;
                        container.appendChild(connector);
                    }
                });

                this.updateDisplay();
            }

            toggleStepDetails(index) {
                const details = document.getElementById(`details-${index}`);
                if (details.classList.contains('hide')) {
                    details.classList.remove('hide');
                    details.classList.add('show');
                } else {
                    details.classList.remove('show');
                    details.classList.add('hide');
                }
            }

            setStepStatus(stepIndex, status, timing = '') {
                const stepElement = document.getElementById(`flow-step-${stepIndex}`);
                const statusElement = document.getElementById(`status-${stepIndex}`);
                const timingElement = document.getElementById(`timing-${stepIndex}`);
                const progressElement = document.getElementById(`progress-${stepIndex}`);

                // Remove all status classes
                stepElement.classList.remove('pending', 'executing', 'completed', 'error', 'skipped');
                statusElement.classList.remove('pending', 'executing', 'completed', 'error', 'skipped');

                // Add new status
                stepElement.classList.add(status);
                statusElement.classList.add(status);

                // Update timing
                if (timing) {
                    timingElement.textContent = timing;
                }

                // Update progress bar
                if (status === 'executing') {
                    progressElement.style.width = '50%';
                } else if (status === 'completed') {
                    progressElement.style.width = '100%';
                } else if (status === 'error') {
                    progressElement.style.width = '100%';
                    progressElement.style.background = '#ef4444';
                }

                // Update connectors
                if (stepIndex > 0) {
                    const prevConnector = document.getElementById(`connector-${stepIndex - 1}`);
                    if (prevConnector && status === 'completed') {
                        prevConnector.classList.add('completed');
                    }
                }
            }

            updateExecutionProgress() {
                const completed = this.currentStep;
                const total = this.totalSteps;
                const percentage = Math.round((completed / total) * 100);

                document.getElementById('progressText').textContent = `${completed}/${total} steps`;
                document.getElementById('summaryProgressFill').style.width = `${percentage}%`;

                if (this.startTime) {
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    const minutes = Math.floor(elapsed / 60);
                    const seconds = elapsed % 60;
                    document.getElementById('executionTime').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }

            updateDisplay() {
                document.getElementById('flowTitle').textContent = this.flowDefinition?.name || 'Automation Flow';
                document.getElementById('flowSubtitle').textContent = this.flowDefinition?.description || '';
                this.updateExecutionProgress();
            }

            // Simulation methods for demonstration
            async simulateExecution() {
                if (this.isExecuting) return;
                
                this.isExecuting = true;
                this.startTime = Date.now();
                this.currentStep = 0;
                
                document.getElementById('executionStatus').textContent = 'Menjalankan...';
                document.getElementById('startBtn').textContent = '⏸️ Jeda';

                for (let i = 0; i < this.steps.length; i++) {
                    if (!this.isExecuting) break;
                    
                    // Set step as executing
                    this.setStepStatus(i, 'executing');
                    this.updateExecutionProgress();
                    
                    // Simulate step execution time
                    await this.delay(2000 + Math.random() * 1000);
                    
                    // Set step as completed
                    this.setStepStatus(i, 'completed', `${(2 + Math.random()).toFixed(1)}s`);
                    this.currentStep = i + 1;
                    this.updateExecutionProgress();
                    
                    await this.delay(500);
                }

                this.isExecuting = false;
                document.getElementById('executionStatus').textContent = 'Selesai';
                document.getElementById('startBtn').textContent = '▶️ Mulai';
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            pause() {
                this.isPaused = !this.isPaused;
                if (this.isPaused) {
                    document.getElementById('executionStatus').textContent = 'Dijeda';
                    document.getElementById('pauseBtn').textContent = '▶️ Lanjut';
                } else {
                    document.getElementById('executionStatus').textContent = 'Menjalankan...';
                    document.getElementById('pauseBtn').textContent = '⏸️ Jeda';
                }
            }

            stop() {
                this.isExecuting = false;
                this.isPaused = false;
                this.currentStep = 0;
                
                // Reset all steps
                for (let i = 0; i < this.steps.length; i++) {
                    this.setStepStatus(i, '');
                    document.getElementById(`progress-${i}`).style.width = '0%';
                }
                
                document.getElementById('executionStatus').textContent = 'Dihentikan';
                document.getElementById('startBtn').textContent = '▶️ Mulai';
                document.getElementById('pauseBtn').textContent = '⏸️ Jeda';
                this.updateExecutionProgress();
            }

            setupEventListeners() {
                // Setup any additional event listeners
            }
        }

        // Global functions for button handlers
        let flowController;

        function startExecution() {
            if (!flowController.isExecuting) {
                flowController.simulateExecution();
            } else {
                flowController.pause();
            }
        }

        function pauseExecution() {
            flowController.pause();
        }

        function stopExecution() {
            flowController.stop();
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            flowController = new VisualFlowController();
        });
    </script>
</body>
</html> 