// Venus-Millware AutoFill - Popup Script
// Alat otomatisasi untuk pengisian form dan input data otomatis
// Developer: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)

class AutomationBotPopup {
    constructor() {
        this.currentTab = 'config';
        this.automationData = null;
        this.flowEvents = [];
        this.isExecuting = false;
        this.apiService = new ApiService();
        
        // Default configuration for timesheet automation
        this.defaultConfig = {
            apiBaseUrl: 'http://localhost:5173/api',
            targetUrl: 'http://millwarep3.rebinmas.com:8003/',
            username: 'adm075',
            password: 'adm075',
            apiKey: '',
            refreshInterval: 30000,
            retryAttempts: 3,
            delayInterval: 1000 // 1 second between steps
        };

        // Predefined timesheet login flow
        this.timesheetLoginFlow = [
            {
                type: 'wait',
                duration: 2000,
                description: 'Wait for page to load'
            },
            {
                type: 'input',
                selector: 'input[type="text"]',
                selectorType: 'css',
                value: 'adm075',
                description: 'Enter username',
                clearFirst: true
            },
            {
                type: 'wait',
                duration: 1000,
                description: 'Wait after username input'
            },
            {
                type: 'input',
                selector: 'input[type="password"]',
                selectorType: 'css',
                value: 'adm075',
                description: 'Enter password',
                clearFirst: true
            },
            {
                type: 'wait',
                duration: 1000,
                description: 'Wait after password input'
            },
            {
                type: 'click',
                selector: 'button',
                selectorType: 'text',
                value: 'LOG IN',
                description: 'Click login button'
            },
            {
                type: 'wait',
                duration: 3000,
                description: 'Wait for login to complete'
            }
        ];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSavedData();
        this.updateStatus('Ready');
        this.setupMessageListener();
        this.initializeWithDefaultConfig();
        this.checkAuthStatus();
    }

    async initializeWithDefaultConfig() {
        try {
            const result = await chrome.storage.local.get(['automationConfig']);
            if (!result.automationConfig || !result.automationConfig.apiBaseUrl) {
                await chrome.storage.local.set({ automationConfig: this.defaultConfig });
                this.loadConfigurationToUI();
                this.showNotification('🔧 Configuration initialized for timesheet automation', 'success');
            }
        } catch (error) {
            console.error('Failed to initialize configuration:', error);
        }
    }

    async checkAuthStatus() {
        // For timesheet automation, we don't need API auth initially
        // The credentials are used for the target website login
        this.updateStatus('Ready for timesheet automation');
        this.showNotification('✅ Ready to automate timesheet login', 'success');
    }

    loadConfigurationToUI() {
        document.getElementById('targetUrl').value = this.defaultConfig.targetUrl;
        document.getElementById('username').value = this.defaultConfig.username;
        document.getElementById('password').value = this.defaultConfig.password;
        document.getElementById('scriptUrl').value = this.defaultConfig.apiBaseUrl;
        document.getElementById('sheetName').value = this.defaultConfig.apiKey;
        document.getElementById('delayInterval').value = this.defaultConfig.delayInterval;
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Quick Run Automation
        document.getElementById('runTimesheetAutomation').addEventListener('click', () => {
            this.runTimesheetAutomation();
        });

        // Configuration tab
        document.getElementById('saveConfig').addEventListener('click', () => {
            this.saveConfiguration();
        });

        document.getElementById('testConnection').addEventListener('click', () => {
            this.testApiConnection();
        });

        document.getElementById('debugConnection').addEventListener('click', () => {
            this.debugConnection();
        });

        document.getElementById('copyDebugInfo').addEventListener('click', () => {
            this.copyDebugInfo();
        });

        // Data preview tab
        document.getElementById('fetchData').addEventListener('click', () => {
            this.fetchStagingData();
        });

        document.getElementById('refreshData').addEventListener('click', () => {
            this.refreshStagingData();
        });

        // Flow definition tab
        document.getElementById('addEvent').addEventListener('click', () => {
            this.addFlowEvent();
        });

        document.getElementById('saveFlow').addEventListener('click', () => {
            this.saveFlow();
        });

        document.getElementById('loadFlow').addEventListener('click', () => {
            this.loadFlow();
        });

        document.getElementById('loadPredefinedFlow').addEventListener('click', () => {
            this.loadPredefinedTimesheetFlow();
        });

        document.getElementById('startFlow').addEventListener('click', () => {
            this.startAutomationFlow();
        });

        // Execution tab
        document.getElementById('runExecution').addEventListener('click', () => {
            this.runAutomationExecution();
        });

        document.getElementById('pauseExecution').addEventListener('click', () => {
            this.pauseExecution();
        });

        document.getElementById('stopExecution').addEventListener('click', () => {
            this.stopExecution();
        });
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'executionProgress':
                this.updateExecutionStatus('Running...', message.progress);
                this.logExecution(`Progress: ${message.progress}% - ${message.step || 'Processing'}`, 'info');
                break;
            
            case 'executionComplete':
                this.isExecuting = false;
                if (message.success) {
                    this.updateExecutionStatus('Completed', 100);
                    this.updateStatus('Automation completed');
                    this.logExecution('✅ Timesheet automation completed successfully', 'success');
                    this.showNotification('🎉 Timesheet automation completed!', 'success');
                    
                    if (message.results) {
                        this.submitAutomationResults(message.results);
                    }
                } else {
                    this.updateExecutionStatus('Failed', 0);
                    this.updateStatus('Automation failed');
                    this.logExecution('❌ Timesheet automation failed: ' + (message.error || 'Unknown error'), 'error');
                    this.showNotification('❌ Automation failed: ' + (message.error || 'Unknown error'), 'error');
                }
                break;

            case 'elementInteraction':
                this.logExecution(`🎯 ${message.type}: ${message.selector} - ${message.status}`, 
                    message.success ? 'success' : 'warning');
                break;

            case 'dataExtracted':
                this.logExecution(`📊 Data extracted: ${message.dataPoints} points`, 'info');
                break;
                
            default:
                break;
        }
        
        sendResponse({ success: true });
    }

    switchTab(tabName) {
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
    }

    async saveConfiguration() {
        const config = {
            apiBaseUrl: document.getElementById('scriptUrl').value,
            targetUrl: document.getElementById('targetUrl').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value,
            apiKey: document.getElementById('sheetName').value,
            refreshInterval: 30000,
            retryAttempts: 3,
            delayInterval: parseInt(document.getElementById('delayInterval').value) || 1000
        };

        try {
            await chrome.storage.local.set({ automationConfig: config });
            
            await this.apiService.saveConfiguration({
                baseUrl: config.apiBaseUrl
            });
            
            this.showNotification('✅ Configuration saved successfully', 'success');
            this.logExecution('Configuration updated', 'info');
        } catch (error) {
            this.showNotification('❌ Failed to save configuration: ' + error.message, 'error');
            console.error('Save config error:', error);
        }
    }

    async loadSavedData() {
        try {
            const result = await chrome.storage.local.get(['automationConfig', 'automationFlowEvents']);
            
            if (result.automationConfig) {
                const config = result.automationConfig;
                document.getElementById('scriptUrl').value = config.apiBaseUrl || this.defaultConfig.apiBaseUrl;
                document.getElementById('targetUrl').value = config.targetUrl || this.defaultConfig.targetUrl;
                document.getElementById('username').value = config.username || this.defaultConfig.username;
                document.getElementById('password').value = config.password || this.defaultConfig.password;
                document.getElementById('sheetName').value = config.apiKey || '';
                document.getElementById('delayInterval').value = config.delayInterval || this.defaultConfig.delayInterval;
            }

            if (result.automationFlowEvents) {
                this.flowEvents = result.automationFlowEvents;
                this.renderFlowEvents();
            }
        } catch (error) {
            console.error('Load saved data error:', error);
        }
    }

    async testApiConnection() {
        this.updateStatus('Testing API connection...');
        this.showNotification('🔄 Testing connection to staging API...', 'info');
        
        try {
            const config = await this.getConfiguration();
            
            await this.apiService.saveConfiguration({
                baseUrl: config.apiBaseUrl
            });
            
            const result = await this.apiService.testConnection();
            
            if (result.success) {
                this.showNotification('✅ Staging API connection successful!', 'success');
                this.updateStatus('API connected');
                this.logExecution(`Staging API connection test successful`, 'success');
                return result;
            } else {
                throw new Error(result.error || 'Connection test failed');
            }
            
        } catch (error) {
            console.error('❌ API connection test error:', error);
            this.handleConnectionError(error);
            throw error;
        }
    }

    async runTimesheetAutomation() {
        if (this.isExecuting) {
            this.showNotification('⚠️ Automation already in progress.', 'warning');
            return;
        }

        this.isExecuting = true;
        this.updateStatus('Starting timesheet automation...');
        this.logExecution('🚀 Starting complete timesheet automation flow', 'info');

        try {
            // Step 1: Fetch staging data first
            this.logExecution('📊 Step 1: Fetching timesheet data from staging API', 'info');
            await this.fetchStagingData();

            if (!this.automationData || this.automationData.length === 0) {
                this.showNotification('⚠️ No staging data available. Continuing with login only.', 'warning');
            }

            // Step 2: Get current tab or open new one
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const config = await this.getConfiguration();
            
            if (!currentTab) {
                throw new Error('No active tab found');
            }

            // Step 3: Navigate to target website
            this.logExecution('🌐 Step 2: Navigating to timesheet website', 'info');
            await chrome.tabs.update(currentTab.id, { url: config.targetUrl });
            
            await this.waitForPageLoad(currentTab.id);
            this.logExecution('✅ Page loaded successfully', 'success');

            // Step 4: Execute login flow
            this.logExecution('🔐 Step 3: Executing login automation', 'info');
            
            const loginFlow = this.timesheetLoginFlow.map(event => ({
                ...event,
                // Update values from configuration
                value: event.type === 'input' && event.selector.includes('text') ? config.username :
                       event.type === 'input' && event.selector.includes('password') ? config.password :
                       event.value
            }));

            const message = {
                action: 'executeAutomationFlow',
                flowEvents: loginFlow,
                automationData: this.automationData,
                config: config,
                metadata: {
                    startTime: new Date().toISOString(),
                    totalEvents: loginFlow.length,
                    executionId: this.generateExecutionId(),
                    automationType: 'timesheet_login'
                }
            };

            const response = await chrome.tabs.sendMessage(currentTab.id, message);
            
            if (response && response.success === false) {
                throw new Error(response.error || 'Login automation failed');
            }

            this.showNotification('🚀 Timesheet automation started successfully!', 'success');
            
        } catch (error) {
            this.isExecuting = false;
            this.updateStatus('Automation failed');
            this.showNotification(`❌ Timesheet automation failed: ${error.message}`, 'error');
            this.logExecution(`❌ Timesheet automation failed: ${error.message}`, 'error');
        }
    }

    async fetchStagingData() {
        this.updateStatus('Fetching timesheet data...');
        document.getElementById('dataStatus').innerHTML = '<span>🔄 Loading timesheet data from staging API...</span>';
        
        try {
            this.logExecution('Fetching data from staging API endpoint', 'info');
            
            const response = await this.apiService.fetchStagingData();
            
            if (response.success) {
                this.automationData = response.data;
                this.renderTimesheetDataPreview(response);
                this.updateStatus('Timesheet data loaded');
                
                const statusMessage = `✅ Loaded ${this.automationData.length} timesheet records`;
                document.getElementById('dataStatus').innerHTML = `<span>${statusMessage}</span>`;
                
                this.showNotification(`Successfully loaded ${this.automationData.length} timesheet records`, 'success');
                this.logExecution(`Staging data fetch successful: ${this.automationData.length} records`, 'success');
                
                return response;
            } else {
                throw new Error(response.error || 'Failed to fetch timesheet data');
            }
            
        } catch (error) {
            console.error('❌ Staging data fetch error:', error);
            this.handleDataFetchError(error);
            throw error;
        }
    }

    async refreshStagingData() {
        await this.fetchStagingData();
    }

    renderTimesheetDataPreview(response) {
        const previewContainer = document.getElementById('dataPreview');
        
        if (!this.automationData || this.automationData.length === 0) {
            previewContainer.innerHTML = '<p>No timesheet data available</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'data-table';
        
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // Define timesheet-specific headers
        const headers = [
            { key: 'employeeId', label: 'Employee ID' },
            { key: 'employeeName', label: 'Employee Name' },
            { key: 'date', label: 'Date' },
            { key: 'checkIn', label: 'Check In' },
            { key: 'checkOut', label: 'Check Out' },
            { key: 'regularHours', label: 'Regular Hours' },
            { key: 'overtimeHours', label: 'Overtime Hours' },
            { key: 'totalHours', label: 'Total Hours' }
        ];
        
        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header.label;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        const tbody = document.createElement('tbody');
        
        this.automationData.slice(0, 10).forEach(record => {
            const tr = document.createElement('tr');
            
            headers.forEach(header => {
                const td = document.createElement('td');
                const value = record[header.key];
                
                if (typeof value === 'number') {
                    td.textContent = value.toFixed(2);
                } else {
                    td.textContent = String(value || '');
                }
                
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        previewContainer.innerHTML = '';
        previewContainer.appendChild(table);
        
        if (this.automationData.length > 10) {
            const moreInfo = document.createElement('p');
            moreInfo.textContent = `... and ${this.automationData.length - 10} more timesheet records`;
            moreInfo.style.textAlign = 'center';
            moreInfo.style.color = '#6c757d';
            moreInfo.style.fontSize = '12px';
            moreInfo.style.marginTop = '10px';
            previewContainer.appendChild(moreInfo);
        }

        this.addTimesheetDataSummary(previewContainer, response);
    }

    addTimesheetDataSummary(container, response) {
        const summary = document.createElement('div');
        summary.style.marginTop = '20px';
        summary.style.padding = '15px';
        summary.style.backgroundColor = '#e8f5e8';
        summary.style.borderRadius = '8px';
        summary.style.border = '1px solid #4caf50';
        
        const totalRecords = this.automationData.length;
        const totalRegularHours = this.automationData.reduce((sum, record) => sum + (record.regularHours || 0), 0);
        const totalOvertimeHours = this.automationData.reduce((sum, record) => sum + (record.overtimeHours || 0), 0);
        const lastUpdated = new Date().toLocaleString();
        
        summary.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #2e7d32;">📊 Timesheet Data Summary</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                <div>
                    <strong>Total Records:</strong><br>
                    <span style="font-size: 18px; color: #2e7d32;">${totalRecords}</span>
                </div>
                <div>
                    <strong>Regular Hours:</strong><br>
                    <span style="font-size: 16px; color: #1976d2;">${totalRegularHours.toFixed(1)}h</span>
                </div>
                <div>
                    <strong>Overtime Hours:</strong><br>
                    <span style="font-size: 16px; color: #f57c00;">${totalOvertimeHours.toFixed(1)}h</span>
                </div>
                <div>
                    <strong>Last Updated:</strong><br>
                    <span style="font-size: 12px; color: #666;">${lastUpdated}</span>
                </div>
            </div>
        `;
        
        container.appendChild(summary);
    }

    loadPredefinedTimesheetFlow() {
        this.flowEvents = [...this.timesheetLoginFlow];
        this.renderFlowEvents();
        this.saveFlowEvents();
        this.showNotification('📂 Predefined timesheet login flow loaded', 'success');
        this.logExecution('Loaded predefined timesheet automation flow', 'info');
    }

    renderFlowEvents() {
        const flowList = document.getElementById('flowList');
        
        if (this.flowEvents.length === 0) {
            flowList.innerHTML = '<p style="padding: 20px; text-align: center; color: #6c757d;">No automation events defined</p>';
            return;
        }
        
        flowList.innerHTML = '';
        
        this.flowEvents.forEach((event, index) => {
            const eventDiv = document.createElement('div');
            eventDiv.className = 'flow-event';
            
            const summary = this.getEventSummary(event);
            const description = event.description ? ` - ${event.description}` : '';
            
            eventDiv.innerHTML = `
                <div class="event-info">
                    <div class="event-type">${event.type.toUpperCase()} #${index + 1}</div>
                    <div class="event-details">${summary}${description}</div>
                </div>
                <div class="event-actions">
                    <button class="btn btn-secondary btn-small" onclick="automationBot.deleteFlowEvent(${index})">Delete</button>
                </div>
            `;
            
            flowList.appendChild(eventDiv);
        });
    }

    getEventSummary(event) {
        switch (event.type) {
            case 'click':
                return `Click: ${event.selector}${event.value ? ` (${event.value})` : ''}`;
            case 'input':
                return `Input: ${event.selector} = "${event.value || event.dataMapping || 'value'}"`;
            case 'wait':
                return `Wait: ${event.duration}ms`;
            case 'extract':
                return `Extract ${event.attribute} from ${event.selector} → ${event.variableName}`;
            case 'navigate':
                return `Navigate to: ${event.url}`;
            default:
                return JSON.stringify(event);
        }
    }

    async getConfiguration() {
        try {
            const result = await chrome.storage.local.get(['automationConfig']);
            const config = result.automationConfig || this.defaultConfig;
            
            if (!config.apiBaseUrl) {
                throw new Error('API Base URL is not configured');
            }
            
            return config;
        } catch (error) {
            console.error('Failed to get configuration:', error);
            throw new Error('Configuration error: ' + error.message);
        }
    }

    handleConnectionError(error) {
        let errorMessage = 'API connection error: ' + error.message;
        let debugInfo = '';
        
        if (error.message.includes('timeout')) {
            errorMessage = '⏱️ Connection timeout - Staging API server not responding';
            debugInfo = 'Check if your staging API server is running on localhost:5173';
        } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
            errorMessage = '🌐 Network error - Unable to reach staging API server';
            debugInfo = 'Verify the API URL and ensure the server is running';
        } else if (error.message.includes('HTTP 401')) {
            errorMessage = '🔒 Authentication required';
            debugInfo = 'Please configure valid API credentials';
        } else if (error.message.includes('HTTP 404')) {
            errorMessage = '🔍 API endpoint not found (404)';
            debugInfo = 'Verify the staging API base URL is correct';
        }
        
        this.showNotification(errorMessage, 'error');
        this.updateStatus('API connection failed');
        this.logExecution(`API connection failed: ${errorMessage}`, 'error');
        
        if (debugInfo) {
            this.logExecution(`💡 Tip: ${debugInfo}`, 'warning');
        }
    }

    handleDataFetchError(error) {
        let errorMessage = 'Staging data fetch error: ' + error.message;
        
        this.showNotification(errorMessage, 'error');
        this.updateStatus('Data fetch failed');
        document.getElementById('dataStatus').innerHTML = `<span style="color: #dc3545;">❌ Failed to load timesheet data</span>`;
        this.logExecution(`Staging data fetch failed: ${errorMessage}`, 'error');
    }

    async debugConnection() {
        const debugDiv = document.getElementById('debugInfo');
        const debugOutput = document.getElementById('debugOutput');
        
        debugDiv.style.display = 'block';
        
        const scriptUrl = document.getElementById('scriptUrl').value;
        const sheetName = document.getElementById('sheetName').value;
        
        const debugInfo = {
            timestamp: new Date().toISOString(),
            configuration: {
                apiBaseUrl: scriptUrl,
                apiKey: sheetName,
                urlValid: scriptUrl.includes('http'),
                urlLength: scriptUrl.length,
                targetUrl: document.getElementById('targetUrl').value,
                username: document.getElementById('username').value ? 'configured' : 'missing'
            },
            browser: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                online: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled
            },
            extension: {
                manifestVersion: chrome.runtime.getManifest().version,
                permissions: chrome.runtime.getManifest().permissions,
                hostPermissions: chrome.runtime.getManifest().host_permissions
            },
            networkTest: null
        };
        
        debugOutput.value = JSON.stringify(debugInfo, null, 2);
        
        try {
            this.updateStatus('Running debug tests...');
            
            const result = await this.apiService.testConnection();
            
            debugInfo.networkTest = {
                success: result.success,
                serverInfo: result.serverInfo || {},
                timestamp: result.timestamp,
                error: result.error || null
            };
            
            this.updateStatus('Debug test completed');
            this.showNotification('Debug information collected successfully', 'success');
            
        } catch (error) {
            debugInfo.networkTest = {
                success: false,
                error: error.message,
                errorName: error.name
            };
            
            this.updateStatus('Debug test failed');
            this.showNotification('Debug test failed: ' + error.message, 'error');
        }
        
        debugOutput.value = JSON.stringify(debugInfo, null, 2);
        debugDiv.scrollIntoView({ behavior: 'smooth' });
    }

    copyDebugInfo() {
        const debugOutput = document.getElementById('debugOutput');
        
        try {
            debugOutput.select();
            document.execCommand('copy');
            this.showNotification('Debug information copied to clipboard', 'success');
        } catch (error) {
            navigator.clipboard.writeText(debugOutput.value).then(() => {
                this.showNotification('Debug information copied to clipboard', 'success');
            }).catch(() => {
                this.showNotification('Failed to copy debug information', 'error');
            });
        }
    }

    addFlowEvent() {
        const eventTypes = [
            // Basic events
            'click', 'input', 'wait', 'extract', 'navigate', 'scroll',
            // New navigation events
            'open_to', 'wait_for_element', 
            // UI interaction events
            'hover', 'scroll_to', 'select_option', 'alert_handle', 'screenshot',
            // Form handling
            'form_fill',
            // Advanced flow control
            'loop', 'if_then_else', 'variable_set', 
            // Data extraction
            'data_extract_multiple',
            // Tab management
            'tab_switch'
        ];

        const eventType = prompt(`Event type (${eventTypes.join(', ')}):`);
        if (!eventType) return;

        let eventData = { type: eventType.toLowerCase(), id: Date.now() };
        
        // Add conditional logic option for any event
        const addCondition = confirm('Add conditional logic to this event?');
        if (addCondition) {
            eventData.condition = this.promptForCondition();
        }
        
        switch (eventType.toLowerCase()) {
            case 'click':
                eventData.selector = prompt('Element selector:');
                eventData.selectorType = prompt('Selector type (css, xpath, text):') || 'css';
                if (eventData.selectorType === 'text') {
                    eventData.value = prompt('Button text to find:');
                }
                break;
                
            case 'input':
                eventData.selector = prompt('Input element selector:');
                eventData.value = prompt('Value to input:');
                eventData.dataMapping = prompt('Map from API data (optional):');
                eventData.clearFirst = confirm('Clear field before input?');
                break;
                
            case 'wait':
                eventData.duration = parseInt(prompt('Wait duration (ms):')) || 1000;
                eventData.waitFor = prompt('Wait for (time, element, navigation):') || 'time';
                if (eventData.waitFor === 'element') {
                    eventData.condition = prompt('Element selector to wait for:');
                }
                break;
                
            case 'extract':
                eventData.selector = prompt('Element selector:');
                eventData.attribute = prompt('Attribute to extract (text, value, href):') || 'text';
                eventData.variableName = prompt('Store in variable:');
                break;
                
            case 'navigate':
                eventData.url = prompt('Target URL:');
                eventData.waitForLoad = confirm('Wait for page to load?');
                break;

            // New event types
            case 'open_to':
                eventData.url = prompt('URL to open:');
                eventData.newTab = confirm('Open in new tab?');
                eventData.waitForLoad = confirm('Wait for page to load?');
                break;

            case 'wait_for_element':
                eventData.selector = prompt('Element selector to wait for:');
                eventData.timeout = parseInt(prompt('Timeout (ms):')) || 10000;
                eventData.expectVisible = confirm('Wait for element to be visible? (false = wait for disappear)');
                break;

            case 'hover':
                eventData.selector = prompt('Element selector to hover:');
                eventData.selectorType = prompt('Selector type (css, xpath, text):') || 'css';
                eventData.duration = parseInt(prompt('Hover duration (ms) - optional:')) || null;
                break;

            case 'scroll_to':
                const scrollTarget = prompt('Scroll target (element/position):');
                if (scrollTarget === 'element') {
                    eventData.selector = prompt('Element selector:');
                    eventData.block = prompt('Block position (start, center, end, nearest):') || 'center';
                } else {
                    const x = parseInt(prompt('X position:')) || 0;
                    const y = parseInt(prompt('Y position:')) || 0;
                    eventData.position = { x, y };
                }
                eventData.smooth = confirm('Smooth scrolling?');
                break;

            case 'select_option':
                eventData.selector = prompt('Select element selector:');
                const selectionMethod = prompt('Select by (value, text, index):');
                if (selectionMethod === 'value') {
                    eventData.value = prompt('Option value:');
                } else if (selectionMethod === 'text') {
                    eventData.text = prompt('Option text:');
                } else {
                    eventData.index = parseInt(prompt('Option index:'));
                }
                break;

            case 'alert_handle':
                eventData.action = prompt('Action (accept, dismiss, text):') || 'accept';
                if (eventData.action === 'text') {
                    eventData.text = prompt('Text to enter:');
                }
                eventData.timeout = parseInt(prompt('Timeout (ms):')) || 5000;
                break;

            case 'screenshot':
                eventData.name = prompt('Screenshot name:') || `screenshot_${Date.now()}`;
                break;

            case 'form_fill':
                eventData.fields = this.promptForFormFields();
                eventData.fieldDelay = parseInt(prompt('Delay between fields (ms):')) || 100;
                break;

            case 'tab_switch':
                const switchMethod = prompt('Switch by (index, url):');
                if (switchMethod === 'index') {
                    eventData.tabIndex = parseInt(prompt('Tab index:'));
                } else {
                    eventData.url = prompt('URL to open in new tab:');
                }
                break;

            case 'loop':
                eventData.iterations = parseInt(prompt('Number of iterations:'));
                eventData.events = [];
                eventData.iterationDelay = parseInt(prompt('Delay between iterations (ms):')) || 500;
                eventData.continueOnError = confirm('Continue on error?');
                alert('Loop created. Add sub-events using the visual editor.');
                break;

            case 'if_then_else':
                eventData.condition = this.promptForCondition();
                eventData.thenEvents = [];
                eventData.elseEvents = [];
                alert('Conditional event created. Add then/else events using the visual editor.');
                break;

            case 'variable_set':
                eventData.variableName = prompt('Variable name:');
                const valueSource = prompt('Value source (static, element, expression, data):');
                if (valueSource === 'static') {
                    eventData.value = prompt('Static value:');
                } else if (valueSource === 'element') {
                    eventData.selector = prompt('Element selector:');
                    eventData.attribute = prompt('Attribute (text, value, etc.):') || 'text';
                } else if (valueSource === 'expression') {
                    eventData.expression = prompt('JavaScript expression:');
                } else if (valueSource === 'data') {
                    eventData.dataMapping = prompt('Data path (e.g., employee.name):');
                }
                break;

            case 'data_extract_multiple':
                eventData.extractions = this.promptForMultipleExtractions();
                break;

            default:
                this.showNotification('Invalid event type', 'error');
                return;
        }
        
        eventData.description = prompt('Event description (optional):') || '';
        
        this.flowEvents.push(eventData);
        this.renderFlowEvents();
        this.saveFlowEvents();
        this.showNotification(`✅ ${eventType} event added successfully`, 'success');
    }

    promptForCondition() {
        const conditionTypes = [
            'element_exists', 'element_text_contains', 'url_contains', 
            'variable_equals', 'page_title_contains', 'element_count', 'custom_script'
        ];
        
        const conditionType = prompt(`Condition type (${conditionTypes.join(', ')}):`);
        if (!conditionType) return null;

        const condition = { type: conditionType };

        switch (conditionType) {
            case 'element_exists':
                condition.selector = prompt('Element selector:');
                condition.visible = confirm('Must be visible?');
                break;
            case 'element_text_contains':
                condition.selector = prompt('Element selector:');
                condition.value = prompt('Text to search for:');
                break;
            case 'url_contains':
                condition.value = prompt('URL part to match:');
                break;
            case 'variable_equals':
                condition.variableName = prompt('Variable name:');
                condition.value = prompt('Expected value:');
                break;
            case 'page_title_contains':
                condition.value = prompt('Title text to match:');
                break;
            case 'element_count':
                condition.selector = prompt('Element selector:');
                condition.operator = prompt('Operator (equals, greater_than, less_than):') || 'greater_than';
                condition.value = parseInt(prompt('Count value:'));
                break;
            case 'custom_script':
                condition.script = prompt('JavaScript expression (return boolean):');
                break;
        }

        return condition;
    }

    promptForFormFields() {
        const fields = [];
        let addMore = true;

        while (addMore) {
            const field = {
                selector: prompt('Field selector:'),
                value: prompt('Field value:'),
                required: confirm('Required field?')
            };

            const hasDataMapping = confirm('Map from API data?');
            if (hasDataMapping) {
                field.dataMapping = prompt('Data mapping path:');
            }

            fields.push(field);
            addMore = confirm('Add another field?');
        }

        return fields;
    }

    promptForMultipleExtractions() {
        const extractions = [];
        let addMore = true;

        while (addMore) {
            const extraction = {
                name: prompt('Extraction name:'),
                selector: prompt('Element selector:'),
                attribute: prompt('Attribute (text, value, href):') || 'text',
                required: confirm('Required extraction?'),
                storeGlobally: confirm('Store globally?')
            };

            const hasTransform = confirm('Apply transformation?');
            if (hasTransform) {
                const transformType = prompt('Transform type (trim, uppercase, lowercase, number, regex_extract):');
                extraction.transform = { type: transformType };
                
                if (transformType === 'regex_extract') {
                    extraction.transform.pattern = prompt('Regex pattern:');
                    extraction.transform.group = parseInt(prompt('Capture group (default 0):')) || 0;
                }
            }

            extractions.push(extraction);
            addMore = confirm('Add another extraction?');
        }

        return extractions;
    }

    async saveFlowEvents() {
        try {
            await chrome.storage.local.set({ automationFlowEvents: this.flowEvents });
        } catch (error) {
            console.error('Save flow events error:', error);
        }
    }

    async startAutomationFlow() {
        if (this.isExecuting) {
            this.showNotification('⚠️ Automation already in progress.', 'warning');
            return;
        }
        
        if (this.flowEvents.length === 0) {
            this.showNotification('❌ No automation events defined.', 'error');
            return;
        }
        
        const config = await this.getConfiguration();
        const targetUrl = config.targetUrl;
        
        if (!targetUrl) {
            this.showNotification('❌ Target URL not configured.', 'error');
            return;
        }
        
        this.isExecuting = true;
        this.updateStatus('Starting automation...');
        this.logExecution(`🚀 Starting automation flow with ${this.flowEvents.length} events`, 'info');
        
        try {
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!currentTab) {
                throw new Error('No active tab found');
            }
            
            await chrome.tabs.update(currentTab.id, { url: targetUrl });
            this.logExecution(`📍 Navigating to: ${targetUrl}`, 'info');
            
            await this.waitForPageLoad(currentTab.id);
            this.logExecution('✅ Page loaded successfully', 'success');
            
            const message = {
                action: 'executeAutomationFlow',
                flowEvents: this.flowEvents,
                automationData: this.automationData,
                config: config,
                metadata: {
                    startTime: new Date().toISOString(),
                    totalEvents: this.flowEvents.length,
                    executionId: this.generateExecutionId()
                }
            };
            
            const response = await chrome.tabs.sendMessage(currentTab.id, message);
            
            if (response && response.success === false) {
                throw new Error(response.error || 'Flow execution failed');
            }
            
            this.showNotification('🚀 Automation flow started successfully!', 'success');
            
        } catch (error) {
            this.isExecuting = false;
            this.updateStatus('Automation failed');
            this.showNotification(`❌ Automation failed: ${error.message}`, 'error');
            this.logExecution(`❌ Automation failed: ${error.message}`, 'error');
        }
    }

    async runAutomationExecution() {
        return this.startAutomationFlow();
    }

    generateExecutionId() {
        return 'exec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async submitAutomationResults(results) {
        try {
            const submissionData = {
                executionId: results.executionId,
                timestamp: new Date().toISOString(),
                success: results.success,
                eventsExecuted: results.eventsExecuted,
                duration: results.duration,
                processedRecords: results.processedRecords || 0,
                extractedData: results.extractedData || {},
                errors: results.errors || []
            };

            const response = await this.apiService.submitAutomationResults(submissionData);
            
            if (response.success) {
                this.logExecution('✅ Results submitted to staging API successfully', 'success');
            } else {
                this.logExecution(`⚠️ Failed to submit results: ${response.error}`, 'warning');
            }
        } catch (error) {
            this.logExecution(`❌ Error submitting results: ${error.message}`, 'error');
        }
    }

    async waitForPageLoad(tabId, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkComplete = () => {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }
                    
                    if (tab.status === 'complete') {
                        setTimeout(resolve, 2000);
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error('Page load timeout'));
                    } else {
                        setTimeout(checkComplete, 100);
                    }
                });
            };
            
            checkComplete();
        });
    }

    pauseExecution() {
        if (!this.isExecuting) return;
        
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'pauseAutomation' });
        });
        
        this.updateExecutionStatus('Paused', null);
        this.logExecution('⏸️ Automation paused', 'warning');
    }

    stopExecution() {
        if (!this.isExecuting) return;
        
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'stopAutomation' });
        });
        
        this.isExecuting = false;
        this.updateExecutionStatus('Stopped', 0);
        this.logExecution('⏹️ Automation stopped', 'error');
    }

    updateStatus(status) {
        document.querySelector('.status-text').textContent = status;
    }

    updateExecutionStatus(status, progress) {
        document.getElementById('executionStatusText').textContent = status;
        if (progress !== null) {
            document.getElementById('progressFill').style.width = progress + '%';
        }
    }

    logExecution(message, type = 'info') {
        const logContainer = document.getElementById('executionLog');
        if (!logContainer) return;
        
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        const statusIndicator = document.getElementById('statusIndicator');
        if (statusIndicator) {
            const statusText = statusIndicator.querySelector('.status-text');
            if (statusText) {
                const originalText = statusText.textContent;
                statusText.textContent = message;
                
                setTimeout(() => {
                    statusText.textContent = originalText;
                }, 3000);
            }
        }
    }

    saveFlow() {
        const flowName = prompt('Masukkan nama flow:');
        if (!flowName) return;
        
        chrome.storage.local.get(['automationFlows'], (result) => {
            const flows = result.automationFlows || {};
            flows[flowName] = {
                events: this.flowEvents,
                created: new Date().toISOString(),
                description: prompt('Masukkan deskripsi flow (opsional):') || ''
            };
            chrome.storage.local.set({ automationFlows: flows });
            this.showNotification(`💾 Flow "${flowName}" disimpan`, 'success');
        });
    }

    loadFlow() {
        chrome.storage.local.get(['automationFlows'], (result) => {
            const flows = result.automationFlows || {};
            const flowNames = Object.keys(flows);
            
            if (flowNames.length === 0) {
                this.showNotification('📋 Tidak ada flow tersimpan', 'warning');
                return;
            }
            
            const flowName = prompt(`Flow tersedia: ${flowNames.join(', ')}\n\nMasukkan nama flow untuk dimuat:`);
            if (!flowName || !flows[flowName]) {
                this.showNotification('❌ Flow tidak ditemukan', 'error');
                return;
            }
            
            this.flowEvents = flows[flowName].events || [];
            this.renderFlowEvents();
            this.saveFlowEvents();
            this.showNotification(`📂 Flow "${flowName}" dimuat`, 'success');
        });
    }

    deleteFlowEvent(index) {
        if (confirm(`Apakah Anda yakin ingin menghapus event #${index + 1}?`)) {
            this.flowEvents.splice(index, 1);
            this.renderFlowEvents();
            this.saveFlowEvents();
            this.showNotification(`🗑️ Event dihapus`, 'success');
        }
    }
}

// Global reference for event handlers
let automationBot;

// Initialize Venus-Millware AutoFill when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    automationBot = new AutomationBotPopup();
    window.automationBot = automationBot;
});
