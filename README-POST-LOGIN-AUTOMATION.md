# Venus-Millware AutoFill - Post-Login Automation & Text Search

## Overview

This Chrome extension provides advanced automation capabilities for the Millware ERP system, including post-login automation sequences and comprehensive text search functionality similar to browser's Ctrl+F feature.

## New Features

### 1. Post-Login Automation Sequence

Automated sequence that performs specific steps after successful login:

#### Sequence Steps:
1. **Post-Login Confirmation**
   - Waits for page load completion after login
   - Searches for element containing "ok" text (case-insensitive)
   - Adds configurable 200ms delay before proceeding
   - Clicks the found "ok" element

2. **Navigation to Target Page**
   - Navigates to: `http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx`
   - Waits for page load completion
   - Adds configurable 300ms delay after navigation

3. **New Button Interaction**
   - Searches for element containing "new" text (case-insensitive)
   - Clicks the found "new" element
   - Waits for any resulting page changes or modal dialogs

4. **Text Search Initialization**
   - Automatically initializes text search functionality
   - Makes search UI available for user interaction

#### Configuration Options:
- `pageLoadTimeout`: 10000ms (default)
- `okElementTimeout`: 5000ms (default)
- `okElementDelay`: 200ms (default)
- `navigationTimeout`: 15000ms (default)
- `navigationDelay`: 300ms (default)
- `newElementTimeout`: 10000ms (default)
- `newElementWaitAfter`: 2000ms (default)

### 2. Advanced Text Search Functionality

Comprehensive text search engine similar to browser's Ctrl+F with enhanced features:

#### Features:
- **Visual Highlighting**: Highlights all matches with yellow background
- **Current Match Highlighting**: Current match highlighted in orange
- **Navigation Controls**: Next/Previous match navigation
- **Case Sensitivity Toggle**: Option for case-sensitive search
- **Match Counter**: Shows current match position and total matches
- **Keyboard Shortcuts**: Full keyboard support
- **Real-time Search**: Instant search as you type

#### Keyboard Shortcuts:
- **Ctrl+F**: Open text search UI
- **F3**: Navigate to next match
- **Shift+F3**: Navigate to previous match
- **Escape**: Close search UI
- **Enter**: Navigate to next match
- **Shift+Enter**: Navigate to previous match

#### Search Options:
- Case sensitive/insensitive search
- Highlight all matches
- Maximum matches limit (default: 100)
- Element type filtering
- Hidden element exclusion

## Usage

### Running Post-Login Automation

1. **Via Extension Popup:**
   - Click the extension icon
   - Click "🔄 Post-Login Sequence" button
   - The automation will run automatically

2. **Via Flow Definition:**
   ```json
   {
     "type": "post_login_sequence",
     "pageLoadTimeout": 10000,
     "okElementTimeout": 5000,
     "okElementDelay": 200,
     "targetUrl": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx",
     "navigationTimeout": 15000,
     "navigationDelay": 300,
     "newElementTimeout": 10000,
     "newElementWaitAfter": 2000
   }
   ```

### Using Text Search

1. **Via Extension Popup:**
   - Go to "Text Search" tab
   - Enter search text
   - Configure options (case sensitivity, highlighting)
   - Click "🔍 Search" button

2. **Via Keyboard Shortcut:**
   - Press `Ctrl+F` on any page
   - Search UI will appear in top-right corner
   - Type search text and navigate with F3/Shift+F3

3. **Via API:**
   ```javascript
   // Send message to content script
   chrome.tabs.sendMessage(tabId, {
     action: 'executeTextSearch',
     searchText: 'your search term',
     caseSensitive: false,
     highlightMatches: true
   });
   ```

## Technical Implementation

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Popup UI      │    │  Background      │    │   Content Script    │
│                 │    │  Script          │    │                     │
│ - Controls      │◄──►│                  │◄──►│ - Automation Engine │
│ - Configuration │    │ - Message Router │    │ - Text Search       │
│ - Status        │    │ - Storage        │    │ - DOM Interaction   │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                                          │
                                                          ▼
                                               ┌─────────────────────┐
                                               │ Text Search Engine  │
                                               │                     │
                                               │ - Search Algorithm  │
                                               │ - Highlighting      │
                                               │ - Navigation        │
                                               │ - UI Components     │
                                               └─────────────────────┘
```

### Key Components

1. **AutomationBotContent** (`content.js`)
   - Main automation engine
   - Event execution and coordination
   - Message handling for popup communication

2. **VenusTextSearchEngine** (`scripts/text-search-engine.js`)
   - Advanced text search implementation
   - DOM traversal and highlighting
   - Keyboard shortcut handling
   - Search UI management

3. **AutomationBotPopup** (`scripts/popup.js`)
   - User interface controls
   - Configuration management
   - Communication with content script

### Error Handling

- **Comprehensive Error Catching**: All automation steps wrapped in try-catch
- **Retry Mechanisms**: Configurable retry attempts for network requests
- **User Feedback**: Real-time status updates and error notifications
- **Graceful Degradation**: Continues execution when non-critical elements not found
- **Detailed Logging**: Console logging for debugging and monitoring

### Security Features

- **Secure Credential Storage**: Uses Chrome storage API
- **Content Security Policy**: Manifest v3 compliance
- **Permission Management**: Minimal required permissions
- **Input Validation**: Sanitized user inputs and configurations

## Configuration

### Storage Structure

```javascript
{
  "automationConfig": {
    "apiBaseUrl": "http://localhost:5173/api",
    "targetUrl": "http://millwarep3.rebinmas.com:8003/",
    "username": "adm075",
    "password": "adm075",
    "delayInterval": 1000,
    "retryAttempts": 3
  },
  "textSearchConfig": {
    "caseSensitive": false,
    "highlightMatches": true,
    "maxMatches": 100,
    "showNavigationControls": true
  }
}
```

### Flow Definition Format

```javascript
{
  "flow_id": "post_login_automation_v1",
  "name": "Post-Login Automation Flow",
  "configuration": {
    "delays": {
      "ok_element_delay": 200,
      "navigation_delay": 300,
      "page_load_timeout": 10000
    },
    "target_url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx",
    "retry_attempts": 3
  },
  "flow_steps": [
    // Step definitions...
  ]
}
```

## Browser Compatibility

- **Chrome**: 88+ (Manifest V3 support)
- **Edge**: 88+ (Chromium-based)
- **Opera**: 74+ (Chromium-based)

## Performance Considerations

- **Optimized DOM Queries**: Efficient element selection algorithms
- **Memory Management**: Proper cleanup of event listeners and highlights
- **Throttled Search**: Debounced search input for performance
- **Lazy Loading**: Text search engine loaded only when needed

## Troubleshooting

### Common Issues

1. **"ok" element not found**
   - Check if page has fully loaded
   - Verify element text content
   - Increase `okElementTimeout` value

2. **Navigation timeout**
   - Check network connectivity
   - Verify target URL accessibility
   - Increase `navigationTimeout` value

3. **Text search not working**
   - Ensure content script is injected
   - Check for JavaScript errors in console
   - Verify page permissions

### Debug Mode

Enable debug logging by setting:
```javascript
window.venusDebugMode = true;
```

## Developer Information

- **Developer**: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)
- **Version**: 1.0.0
- **License**: Proprietary
- **Support**: Internal IT Support

## Changelog

### Version 1.0.0
- Initial implementation of post-login automation
- Advanced text search functionality
- Keyboard shortcuts support
- Comprehensive error handling
- Flow definition system
- Configuration management
