/* Chrome Extension Automation Bot - Popup Styles */

:root {
    --primary-color: #4caf50;
    --secondary-color: #2196f3;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --success-color: #4caf50;
    --background-color: #ffffff;
    --border-color: #e0e0e0;
    --text-color: #333333;
    --accent-color: #6c757d;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--background-color);
    width: 400px;
    max-height: 600px;
    overflow-y: auto;
}

.container {
    padding: 0;
}

/* Header */
.header {
    background: linear-gradient(135deg, #4caf50, #2196f3);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    flex: 1;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 8px;
    border-radius: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4caf50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Quick Run Section */
.quick-run-section {
    background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
    border-bottom: 2px solid #4caf50;
    padding: 20px;
    margin: 0;
}

.quick-run-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2e7d32;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-run-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.info-item {
    font-size: 12px;
    color: #555;
}

.info-item strong {
    color: #2e7d32;
    font-weight: 600;
}

.btn-primary-action {
    background: linear-gradient(135deg, #4caf50, #45a049) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    padding: 12px 24px !important;
    width: 100% !important;
    transition: all 0.3s ease !important;
}

.btn-primary-action:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40) !important;
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
    transform: translateY(-1px) !important;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    padding: 0;
}

.tab-button {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    font-size: 12px;
    font-weight: 500;
    color: var(--accent-color);
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: rgba(33, 150, 243, 0.1);
    color: var(--secondary-color);
}

.tab-button.active {
    color: var(--secondary-color);
    border-bottom-color: var(--secondary-color);
    background: white;
    font-weight: 600;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* Sections */
.section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 6px;
    color: var(--text-color);
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-group small {
    display: block;
    font-size: 11px;
    color: var(--accent-color);
    margin-top: 4px;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #45a049;
    border-color: #45a049;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #e68900;
    border-color: #e68900;
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #45a049;
    border-color: #45a049;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #d32f2f;
    border-color: #d32f2f;
}

.btn-large {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

/* Data Controls */
.data-controls, .flow-controls, .execution-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

/* Data Status */
.data-status {
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 12px;
}

/* Data Preview */
.data-preview {
    max-height: 300px;
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.data-table th {
    background: #f8f9fa;
    padding: 8px 6px;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
}

.data-table td {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Flow Events */
.flow-list {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: #fafafa;
}

.flow-event {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    background: white;
    margin-bottom: 1px;
}

.flow-event:last-child {
    border-bottom: none;
}

.event-info {
    flex: 1;
}

.event-type {
    font-size: 11px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 4px;
}

.event-details {
    font-size: 12px;
    color: var(--text-color);
    line-height: 1.3;
}

.event-actions {
    display: flex;
    gap: 4px;
}

/* Execution Status */
.execution-status {
    margin-bottom: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #66bb6a);
    width: 0%;
    transition: width 0.3s ease;
}

.execution-status .status-text {
    font-size: 12px;
    color: var(--accent-color);
}

/* Execution Log */
.execution-log {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-family: 'Consolas', monospace;
    font-size: 11px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 4px;
    padding: 2px 0;
}

.log-timestamp {
    color: var(--accent-color);
    font-weight: 500;
}

.log-info {
    color: var(--secondary-color);
}

.log-success {
    color: var(--success-color);
}

.log-warning {
    color: var(--warning-color);
}

.log-error {
    color: var(--danger-color);
}

/* Debug Info */
.debug-info {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

/* Flow Quick Actions */
.flow-quick-actions {
    text-align: center;
}

.flow-quick-actions h4 {
    color: var(--text-color);
    margin-bottom: 12px;
}

/* Responsive Design */
@media (max-width: 480px) {
    body {
        width: 100%;
        max-width: 400px;
    }
    
    .quick-run-info {
        grid-template-columns: 1fr;
    }
    
    .data-controls, .flow-controls, .execution-controls {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-right: 0;
    }
}

/* Animation for success states */
.success-pulse {
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
