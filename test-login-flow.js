// Test script for Venus-Millware AutoFill Login Flow
// Use this to test the improved login automation

class LoginFlowTester {
    constructor() {
        this.results = [];
    }

    async testLoginFlow() {
        console.log('🧪 Starting Login Flow Test');
        
        try {
            // Test 1: Check if login elements exist
            await this.testLoginElements();
            
            // Test 2: Test conditional popup detection
            await this.testPopupDetection();
            
            // Test 3: Test timing mechanisms
            await this.testTimingMechanisms();
            
            // Test 4: Test full flow simulation
            await this.testFullFlowSimulation();
            
            this.showResults();
            
        } catch (error) {
            console.error('❌ Test failed:', error);
        }
    }

    async testLoginElements() {
        console.log('\n📝 Test 1: Login Elements Detection');
        
        const selectors = [
            '#txtUsername',
            '#txtPassword', 
            '#btnLogin',
            'input[name="btnLogin"]',
            'input[value="LOG IN"]',
            'input[type="submit"]',
            'button[type="submit"]'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            const visible = element ? this.isElementVisible(element) : false;
            const result = element ? (visible ? '✅ Found & Visible' : '⚠️ Found but Hidden') : '❌ Not found';
            console.log(`  ${selector}: ${result}`);
            if (element) {
                console.log(`    - Text/Value: "${element.textContent || element.value || 'N/A'}"`);
                console.log(`    - Tag: ${element.tagName}, Type: ${element.type || 'N/A'}`);
            }
            this.results.push({
                test: 'Login Elements',
                selector: selector,
                found: !!element,
                visible: visible
            });
        }

        // Additional comprehensive login button search
        console.log('\n🔍 Comprehensive Login Button Search:');
        this.testLoginButtonDetection();
    }

    testLoginButtonDetection() {
        const searchMethods = [
            {
                name: 'By Value "LOG IN"',
                selector: 'input[value="LOG IN"], button[value="LOG IN"]'
            },
            {
                name: 'By Type Submit',
                selector: 'input[type="submit"], button[type="submit"]'
            },
            {
                name: 'By Text Content',
                method: 'text',
                texts: ['LOG IN', 'Login', 'Sign In']
            },
            {
                name: 'By Form Context',
                method: 'form'
            }
        ];

        for (const method of searchMethods) {
            if (method.method === 'text') {
                // Text-based search
                const elements = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
                let found = false;
                for (const el of elements) {
                    const text = (el.textContent || el.value || '').trim().toUpperCase();
                    for (const searchText of method.texts) {
                        if (text.includes(searchText.toUpperCase())) {
                            console.log(`  ✅ ${method.name}: Found "${text}" on ${el.tagName}`);
                            found = true;
                            break;
                        }
                    }
                    if (found) break;
                }
                if (!found) {
                    console.log(`  ❌ ${method.name}: Not found`);
                }
            } else if (method.method === 'form') {
                // Form context search
                const forms = document.querySelectorAll('form');
                let found = false;
                for (const form of forms) {
                    const submitBtns = form.querySelectorAll('input[type="submit"], button[type="submit"], button:not([type])');
                    if (submitBtns.length > 0) {
                        console.log(`  ✅ ${method.name}: Found ${submitBtns.length} submit button(s) in form`);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    console.log(`  ❌ ${method.name}: No submit buttons found in forms`);
                }
            } else {
                // Selector-based search
                const elements = document.querySelectorAll(method.selector);
                const visibleElements = Array.from(elements).filter(el => this.isElementVisible(el));
                if (visibleElements.length > 0) {
                    console.log(`  ✅ ${method.name}: Found ${visibleElements.length} element(s)`);
                    visibleElements.forEach((el, i) => {
                        console.log(`    - Element ${i+1}: ${el.tagName} with value/text "${el.value || el.textContent || 'N/A'}"`);
                    });
                } else if (elements.length > 0) {
                    console.log(`  ⚠️ ${method.name}: Found ${elements.length} element(s) but all hidden`);
                } else {
                    console.log(`  ❌ ${method.name}: Not found`);
                }
            }
        }
    }

    async testPopupDetection() {
        console.log('\n🔍 Test 2: Popup Detection');
        
        const popupSelectors = [
            '.PopupBoxLogin',
            'input[name="ctl00$MainContent$btnOkay"]',
            '#MainContent_btnOkay',
            'input[value="ok"]',
            'input[value="OK"]'
        ];
        
        for (const selector of popupSelectors) {
            const element = document.querySelector(selector);
            const visible = element ? this.isElementVisible(element) : false;
            console.log(`  ${selector}: ${element ? '✅ Exists' : '❌ Not found'} ${visible ? '(Visible)' : '(Hidden/Not visible)'}`);
            this.results.push({
                test: 'Popup Detection',
                selector: selector,
                found: !!element,
                visible: visible
            });
        }
    }

    async testTimingMechanisms() {
        console.log('\n⏱️ Test 3: Timing Mechanisms');
        
        // Test delay function
        const startTime = Date.now();
        await this.delay(1000);
        const actualDelay = Date.now() - startTime;
        const delayAccurate = Math.abs(actualDelay - 1000) < 100; // Allow 100ms tolerance
        
        console.log(`  Delay test: ${delayAccurate ? '✅' : '❌'} (Expected: 1000ms, Actual: ${actualDelay}ms)`);
        
        this.results.push({
            test: 'Timing',
            expected: 1000,
            actual: actualDelay,
            accurate: delayAccurate
        });
    }

    async testFullFlowSimulation() {
        console.log('\n🎯 Test 4: Full Flow Simulation (Dry Run)');
        
        // Simulate the optimized flow steps with faster timing
        const flowSteps = [
            'Navigate to Login Page',
            'Wait for Page Load (500ms)', 
            'Input Username',
            'Input Password',
            'Click Login Button (wait 500ms)',
            'Handle Login Popup (timeout 1000ms)',
            'Navigate to Task Register'
        ];
        
        for (let i = 0; i < flowSteps.length; i++) {
            console.log(`  Step ${i + 1}: ${flowSteps[i]} - Simulated ✅`);
            await this.delay(100); // Simulate fast step execution time
        }
        
        console.log('  ⚡ Optimized flow simulation completed (Total: ~3 seconds) ✅');
    }

    isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return rect.width > 0 && 
               rect.height > 0 && 
               style.visibility !== 'hidden' && 
               style.display !== 'none' &&
               style.opacity !== '0';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('='.repeat(50));
        
        const loginElements = this.results.filter(r => r.test === 'Login Elements');
        const foundElements = loginElements.filter(r => r.found).length;
        console.log(`Login Elements: ${foundElements}/${loginElements.length} found`);
        
        const popupElements = this.results.filter(r => r.test === 'Popup Detection');
        const foundPopupElements = popupElements.filter(r => r.found).length;
        console.log(`Popup Elements: ${foundPopupElements}/${popupElements.length} found`);
        
        const timingResults = this.results.filter(r => r.test === 'Timing');
        const accurateTiming = timingResults.filter(r => r.accurate).length;
        console.log(`Timing Accuracy: ${accurateTiming}/${timingResults.length} accurate`);
        
        console.log('\n🎯 Recommendations:');
        if (foundElements < 3) {
            console.log('⚠️ Missing critical login elements - check selectors');
        }
        if (foundPopupElements === 0) {
            console.log('ℹ️ No popup elements found - this is normal if not on login result page');
        }
        if (accurateTiming === 0) {
            console.log('⚠️ Timing issues detected - may affect automation reliability');
        }
        
        console.log('✅ Test completed! Check console for detailed results.');
    }
}

// Auto-run test if this script is executed
if (typeof window !== 'undefined') {
    console.log('Venus-Millware Login Flow Tester loaded. Run: new LoginFlowTester().testLoginFlow()');
    
    // Expose tester to global scope for manual testing
    window.venusLoginTester = new LoginFlowTester();
}

// Quick debug function - run this directly on login page
window.debugLoginButton = function() {
    console.log('🚀 QUICK LOGIN BUTTON DEBUG');
    console.log('=' .repeat(50));
    
    // 1. Check all submit buttons
    const submitInputs = document.querySelectorAll('input[type="submit"]');
    console.log(`\n📋 Submit Inputs Found: ${submitInputs.length}`);
    submitInputs.forEach((input, i) => {
        const visible = input.offsetWidth > 0 && input.offsetHeight > 0;
        console.log(`  ${i+1}. Value: "${input.value}" | Visible: ${visible ? '✅' : '❌'} | ID: ${input.id} | Name: ${input.name}`);
    });
    
    // 2. Check all submit buttons  
    const submitButtons = document.querySelectorAll('button[type="submit"], button:not([type])');
    console.log(`\n🔘 Submit Buttons Found: ${submitButtons.length}`);
    submitButtons.forEach((btn, i) => {
        const visible = btn.offsetWidth > 0 && btn.offsetHeight > 0;
        console.log(`  ${i+1}. Text: "${btn.textContent}" | Value: "${btn.value}" | Visible: ${visible ? '✅' : '❌'} | ID: ${btn.id}`);
    });
    
    // 3. Search by specific text
    const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
    console.log(`\n🔍 Searching for "LOG IN" text in ${allButtons.length} buttons/inputs:`);
    let found = false;
    allButtons.forEach((el, i) => {
        const text = (el.textContent || el.value || '').trim();
        if (text.toUpperCase().includes('LOG IN') || text.toUpperCase().includes('LOGIN')) {
            const visible = el.offsetWidth > 0 && el.offsetHeight > 0;
            console.log(`  ✅ Found: "${text}" | Tag: ${el.tagName} | Visible: ${visible ? '✅' : '❌'} | Selector: #${el.id || 'no-id'}`);
            found = true;
        }
    });
    if (!found) {
        console.log('  ❌ No "LOG IN" text found');
    }
    
    // 4. Check forms
    const forms = document.querySelectorAll('form');
    console.log(`\n📄 Forms Found: ${forms.length}`);
    forms.forEach((form, i) => {
        const submitElements = form.querySelectorAll('input[type="submit"], button[type="submit"], button:not([type])');
        console.log(`  Form ${i+1}: ${submitElements.length} submit element(s)`);
        submitElements.forEach((el, j) => {
            const visible = el.offsetWidth > 0 && el.offsetHeight > 0;
            console.log(`    ${j+1}. ${el.tagName} - "${el.value || el.textContent}" | Visible: ${visible ? '✅' : '❌'}`);
        });
    });
    
    console.log('\n🎯 RECOMMENDED SELECTORS:');
    
    // Generate recommended selectors
    const recommendations = [];
    
    submitInputs.forEach(input => {
        if (input.offsetWidth > 0 && input.offsetHeight > 0) {
            if (input.id) recommendations.push(`#${input.id}`);
            if (input.name) recommendations.push(`input[name="${input.name}"]`);
            if (input.value) recommendations.push(`input[value="${input.value}"]`);
        }
    });
    
    submitButtons.forEach(btn => {
        if (btn.offsetWidth > 0 && btn.offsetHeight > 0) {
            if (btn.id) recommendations.push(`#${btn.id}`);
            if (btn.name) recommendations.push(`button[name="${btn.name}"]`);
        }
    });
    
    if (recommendations.length > 0) {
        recommendations.forEach((rec, i) => {
            console.log(`  ${i+1}. ${rec}`);
        });
    } else {
        console.log('  ⚠️ No visible submit elements found!');
    }
    
    console.log('\n💡 Copy and run: window.debugLoginButton() to re-run this test');
    return {
        submitInputs: submitInputs.length,
        submitButtons: submitButtons.length,
        recommendations: recommendations
    };
};

// Quick text search debug tool - like Ctrl+F
window.debugTextSearch = function(searchText, options = {}) {
    console.log(`🔍 TEXT SEARCH DEBUG: "${searchText}"`);
    console.log('=' .repeat(60));
    
    const {
        caseSensitive = false,
        elementTypes = ['*']
    } = options;
    
    // Get all elements
    const selector = elementTypes.includes('*') ? '*' : elementTypes.join(', ');
    const allElements = document.querySelectorAll(selector);
    
    const results = [];
    let processedSearch = caseSensitive ? searchText : searchText.toUpperCase();
    
    console.log(`📋 Scanning ${allElements.length} elements...`);
    
    allElements.forEach((element, index) => {
        // Skip hidden elements
        const visible = element.offsetWidth > 0 && element.offsetHeight > 0;
        
        // Get all possible text content
        const texts = [
            element.textContent,
            element.innerText, 
            element.value,
            element.title,
            element.getAttribute('alt'),
            element.getAttribute('placeholder')
        ].filter(Boolean);
        
        texts.forEach(text => {
            let processedText = caseSensitive ? text.trim() : text.trim().toUpperCase();
            
            if (processedText.includes(processedSearch)) {
                results.push({
                    element,
                    text: text.trim(),
                    tag: element.tagName,
                    id: element.id || 'no-id',
                    className: element.className || 'no-class',
                    visible: visible,
                    selector: element.id ? `#${element.id}` : `${element.tagName.toLowerCase()}${element.className ? `.${element.className.split(' ')[0]}` : ''}`
                });
            }
        });
    });
    
    console.log(`\n🎯 FOUND ${results.length} MATCHES:`);
    
    if (results.length === 0) {
        console.log(`❌ No elements found containing "${searchText}"`);
        return { found: 0, results: [] };
    }
    
    results.forEach((result, i) => {
        const status = result.visible ? '✅ Visible' : '⚠️ Hidden';
        console.log(`\n${i + 1}. ${status} - ${result.tag}`);
        console.log(`   Text: "${result.text}"`);
        console.log(`   ID: ${result.id}`);
        console.log(`   Class: ${result.className}`);
        console.log(`   Selector: ${result.selector}`);
        
        // Add click test function
        if (result.visible) {
            console.log(`   💡 Test click: document.querySelector('${result.selector}').click()`);
        }
    });
    
    // Show quick actions
    console.log(`\n🚀 QUICK ACTIONS:`);
    const visibleResults = results.filter(r => r.visible);
    
    if (visibleResults.length > 0) {
        console.log(`✅ ${visibleResults.length} visible elements found`);
        console.log(`💡 Click first visible: window.clickFirst("${searchText}")`);
        console.log(`📋 Get all visible: window.getAllVisible("${searchText}")`);
    } else {
        console.log(`⚠️ No visible elements found`);
    }
    
    console.log(`\n🔄 Search again: window.debugTextSearch("${searchText}")`);
    
    return {
        found: results.length,
        visible: visibleResults.length,
        results: results,
        visibleResults: visibleResults
    };
};

// Helper function to click first visible match
window.clickFirst = function(searchText) {
    const result = window.debugTextSearch(searchText, { elementTypes: ['button', 'input', 'a'] });
    if (result.visibleResults.length > 0) {
        const element = result.visibleResults[0].element;
        element.click();
        console.log(`✅ Clicked: "${result.visibleResults[0].text}"`);
        return true;
    } else {
        console.log(`❌ No visible clickable elements found for "${searchText}"`);
        return false;
    }
};

// Helper function to get all visible elements
window.getAllVisible = function(searchText) {
    const result = window.debugTextSearch(searchText);
    return result.visibleResults.map(r => r.element);
}; 