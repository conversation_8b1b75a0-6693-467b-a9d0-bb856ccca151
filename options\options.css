/**
 * Auto Form Fill Pro - Options Page Styles
 * Modern, responsive design for the settings interface
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #374151;
    background: #f9fafb;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 32px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.header-text h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 4px;
}

.header-text p {
    opacity: 0.9;
    font-size: 14px;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 0 32px;
    overflow-x: auto;
}

.nav-tab {
    background: none;
    border: none;
    padding: 16px 20px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.nav-tab:hover {
    color: #374151;
    background: rgba(107, 114, 128, 0.05);
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

/* Main Content */
.main-content {
    padding: 32px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(8px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Section Headers */
.section-header {
    margin-bottom: 32px;
}

.section-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
}

.section-header p {
    color: #6b7280;
    font-size: 16px;
}

/* Settings Groups */
.settings-group {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.settings-group h3 {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.group-description {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
}

/* Setting Items */
.setting-item {
    margin-bottom: 20px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

/* Toggle Switches */
.toggle-container {
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
}

.toggle-container input {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 48px;
    height: 24px;
    background: #d1d5db;
    border-radius: 24px;
    transition: all 0.3s ease;
}

.toggle-slider:before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-container input:checked + .toggle-slider {
    background: #667eea;
}

.toggle-container input:checked + .toggle-slider:before {
    transform: translateX(24px);
}

.toggle-info {
    flex: 1;
}

.toggle-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 2px;
}

.toggle-description {
    font-size: 13px;
    color: #6b7280;
}

/* Checkboxes */
.checkbox-container {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.checkbox-container input {
    display: none;
}

.checkbox-checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: white;
    transition: all 0.2s ease;
    position: relative;
}

.checkbox-checkmark:after {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    width: 4px;
    height: 8px;
    border: 2px solid white;
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.checkbox-container input:checked + .checkbox-checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-container input:checked + .checkbox-checkmark:after {
    opacity: 1;
}

.checkbox-info {
    flex: 1;
}

.checkbox-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 2px;
}

.checkbox-description {
    font-size: 13px;
    color: #6b7280;
}

/* Input Fields */
.input-container {
    display: block;
    width: 100%;
}

.input-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.input-field {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.input-field:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-field:invalid {
    border-color: #ef4444;
}

.input-description {
    display: block;
    font-size: 13px;
    color: #6b7280;
    margin-top: 4px;
}

/* Select Fields */
.select-container {
    display: block;
    width: 100%;
}

.select-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.select-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.select-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.select-description {
    display: block;
    font-size: 13px;
    color: #6b7280;
    margin-top: 4px;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn.primary {
    background: #667eea;
    color: white;
}

.btn.primary:hover {
    background: #5b6ee8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn.secondary:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.btn.danger {
    background: #ef4444;
    color: white;
}

.btn.danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Setting Actions */
.setting-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
    flex-wrap: wrap;
}

/* Status Panel */
.status-panel {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-top: 20px;
}

.status-icon {
    font-size: 24px;
    min-width: 32px;
    text-align: center;
}

.status-text {
    flex: 1;
}

.status-title {
    font-weight: 600;
    color: #374151;
    margin-bottom: 2px;
}

.status-message {
    font-size: 13px;
    color: #6b7280;
}

.status-panel.success {
    background: #f0fdf4;
    border-color: #bbf7d0;
}

.status-panel.success .status-title {
    color: #166534;
}

.status-panel.success .status-message {
    color: #15803d;
}

.status-panel.error {
    background: #fef2f2;
    border-color: #fecaca;
}

.status-panel.error .status-title {
    color: #991b1b;
}

.status-panel.error .status-message {
    color: #dc2626;
}

/* Domain Lists */
.domain-list-container {
    margin-top: 16px;
}

.domain-input-container {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.domain-input-container .input-field {
    flex: 1;
}

.domain-list {
    list-style: none;
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
}

.domain-list:empty {
    padding: 20px;
    text-align: center;
    color: #6b7280;
    font-style: italic;
}

.domain-list:empty:before {
    content: 'No domains added';
}

.domain-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.domain-item:last-child {
    border-bottom: none;
}

.domain-name {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #374151;
}

.domain-remove {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.domain-remove:hover {
    background: #fef2f2;
}

/* Mapping Grid */
.mapping-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 16px;
}

.mapping-item {
    background: #f8fafc;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Custom Mappings */
.custom-mapping-container {
    margin-top: 16px;
}

.custom-mapping-input {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.custom-mapping-input .input-field {
    flex: 1;
    min-width: 200px;
}

.custom-mappings-list {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    max-height: 300px;
    overflow-y: auto;
}

.custom-mapping {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.custom-mapping:last-child {
    border-bottom: none;
}

.mapping-details {
    flex: 1;
}

.mapping-selector {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #374151;
    font-weight: 500;
}

.mapping-field {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #6b7280;
}

/* Storage Stats */
.storage-stats {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
}

.storage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e5e7eb;
}

.storage-item:last-child {
    border-bottom: none;
}

.storage-label {
    font-weight: 500;
    color: #374151;
}

.storage-value {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #6b7280;
}

/* Footer */
.footer {
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
    padding: 24px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.save-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.save-status {
    font-size: 13px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.save-status.visible {
    opacity: 1;
}

.save-status.success {
    background: #d1fae5;
    color: #065f46;
}

.save-status.error {
    background: #fee2e2;
    color: #991b1b;
}

.footer-info {
    text-align: right;
}

.footer-info p {
    color: #6b7280;
    font-size: 13px;
    margin-bottom: 4px;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-links a {
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.footer-links a:hover {
    text-decoration: underline;
}

.footer-links span {
    color: #d1d5db;
}

/* Modals */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
}

.modal-overlay.visible {
    display: flex;
}

.modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.modal-body {
    padding: 16px 24px;
}

.modal-body p {
    color: #6b7280;
    line-height: 1.6;
}

.modal-footer {
    padding: 16px 24px 20px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    pointer-events: none;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 12px;
    border-left: 4px solid #667eea;
    max-width: 350px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    pointer-events: auto;
}

.toast.visible {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #10b981;
}

.toast.error {
    border-left-color: #ef4444;
}

.toast.warning {
    border-left-color: #f59e0b;
}

.toast-title {
    font-weight: 600;
    color: #111827;
    margin-bottom: 4px;
}

.toast-message {
    color: #6b7280;
    font-size: 13px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }

    .header {
        padding: 20px 16px;
    }

    .nav-tabs {
        padding: 0 16px;
    }

    .main-content {
        padding: 20px 16px;
    }

    .settings-group {
        padding: 20px 16px;
    }

    .footer {
        padding: 20px 16px;
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .mapping-grid {
        grid-template-columns: 1fr;
    }

    .custom-mapping-input {
        flex-direction: column;
    }

    .custom-mapping-input .input-field {
        min-width: unset;
    }

    .domain-input-container {
        flex-direction: column;
    }

    .save-section {
        flex-direction: column;
        align-items: stretch;
    }

    .footer-info {
        text-align: center;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    html, body {
        background: #111827;
        color: #f9fafb;
    }

    .container {
        background: #1f2937;
    }

    .settings-group {
        background: #374151;
        border-color: #4b5563;
    }

    .input-field,
    .select-input {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .input-field:focus,
    .select-input:focus {
        border-color: #667eea;
        background: #374151;
    }

    .footer {
        background: #374151;
        border-color: #4b5563;
    }

    .status-panel {
        background: #374151;
        border-color: #4b5563;
    }

    .domain-list,
    .custom-mappings-list,
    .storage-stats,
    .mapping-item {
        background: #374151;
        border-color: #4b5563;
    }

    .modal {
        background: #1f2937;
    }

    .modal-header {
        border-color: #374151;
    }

    .toast {
        background: #374151;
        color: #f9fafb;
    }
}

/* Print Styles */
@media print {
    .nav-tabs,
    .footer,
    .toast-container,
    .modal-overlay {
        display: none !important;
    }

    .container {
        box-shadow: none;
    }

    .header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
    }
} 