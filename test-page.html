<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venus Auto Fill - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .form-section h3 {
            margin-top: 0;
            color: #667eea;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            display: none;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        @media (max-width: 600px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Venus Auto Fill Test Page</h1>
        
        <div class="info">
            <strong>Welcome to the Venus Auto Fill test page!</strong><br>
            Use this page to practice creating automation flows with your extension. 
            Try different selectors and test the automation features safely.
        </div>

        <!-- Employee Search Section -->
        <div class="form-section">
            <h3>Employee Search</h3>
            <div class="form-group">
                <label for="employee-id">Employee ID:</label>
                <input type="text" id="employee-id" name="employee-id" placeholder="Enter employee ID (e.g., PTRJ.241000089)">
            </div>
            <div class="form-group">
                <label for="employee-name">Employee Name:</label>
                <input type="text" id="employee-name" name="employee-name" placeholder="Enter employee name">
            </div>
            <button id="search-employee" onclick="searchEmployee()">Search Employee</button>
            <button id="clear-search" onclick="clearSearch()">Clear</button>
            <div id="search-result" class="success"></div>
        </div>

        <!-- Attendance Entry Section -->
        <div class="form-section">
            <h3>Attendance Entry</h3>
            <div class="grid">
                <div class="form-group">
                    <label for="attendance-date">Date:</label>
                    <input type="date" id="attendance-date" name="attendance-date">
                </div>
                <div class="form-group">
                    <label for="department">Department:</label>
                    <select id="department" name="department">
                        <option value="">Select Department</option>
                        <option value="hr">Human Resources</option>
                        <option value="it">Information Technology</option>
                        <option value="finance">Finance</option>
                        <option value="operations">Operations</option>
                    </select>
                </div>
            </div>
            
            <div class="grid">
                <div class="form-group">
                    <label for="regular-hours">Regular Hours:</label>
                    <input type="number" id="regular-hours" name="regular-hours" min="0" max="24" step="0.5" placeholder="8">
                </div>
                <div class="form-group">
                    <label for="overtime-hours">Overtime Hours:</label>
                    <input type="number" id="overtime-hours" name="overtime-hours" min="0" max="24" step="0.5" placeholder="0">
                </div>
            </div>
            
            <div class="form-group">
                <label for="notes">Notes:</label>
                <textarea id="notes" name="notes" rows="3" placeholder="Additional notes (optional)"></textarea>
            </div>
            
            <button id="save-attendance" onclick="saveAttendance()">Save Attendance</button>
            <button id="calculate-total" onclick="calculateTotal()">Calculate Total</button>
            <div id="attendance-result" class="success"></div>
        </div>

        <!-- Monthly Summary Section -->
        <div class="form-section">
            <h3>Monthly Summary</h3>
            <div class="grid">
                <div class="form-group">
                    <label for="month-year">Month/Year:</label>
                    <input type="month" id="month-year" name="month-year" value="2025-05">
                </div>
                <div class="form-group">
                    <label for="total-days">Total Working Days:</label>
                    <input type="number" id="total-days" name="total-days" min="1" max="31" value="22" readonly>
                </div>
            </div>
            
            <div class="grid">
                <div class="form-group">
                    <label for="total-regular">Total Regular Hours:</label>
                    <input type="number" id="total-regular" name="total-regular" step="0.5" readonly>
                </div>
                <div class="form-group">
                    <label for="total-overtime">Total Overtime Hours:</label>
                    <input type="number" id="total-overtime" name="total-overtime" step="0.5" readonly>
                </div>
            </div>
            
            <button id="generate-report" onclick="generateReport()">Generate Report</button>
            <button id="submit-monthly" onclick="submitMonthly()">Submit Monthly Data</button>
            <div id="monthly-result" class="success"></div>
        </div>

        <!-- Test Controls -->
        <div class="form-section">
            <h3>Test Controls</h3>
            <p>Use these buttons to test different automation scenarios:</p>
            <button onclick="fillSampleData()">Fill Sample Data</button>
            <button onclick="clearAllFields()">Clear All Fields</button>
            <button onclick="simulateLoading()">Simulate Loading</button>
            <button onclick="showAlert()">Show Alert</button>
        </div>
    </div>

    <script>
        // Set today's date by default
        document.getElementById('attendance-date').value = new Date().toISOString().split('T')[0];

        function searchEmployee() {
            const employeeId = document.getElementById('employee-id').value;
            const employeeName = document.getElementById('employee-name').value;
            const result = document.getElementById('search-result');
            
            if (employeeId || employeeName) {
                result.innerHTML = `✓ Employee found: ${employeeName || 'Unknown'} (${employeeId || 'No ID'})`;
                result.style.display = 'block';
                
                // Auto-fill some data if employee ID matches sample
                if (employeeId === 'PTRJ.241000089') {
                    document.getElementById('employee-name').value = 'Nursamsih';
                    document.getElementById('department').value = 'hr';
                }
            } else {
                result.innerHTML = '⚠ Please enter Employee ID or Name';
                result.style.display = 'block';
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
            }
        }

        function clearSearch() {
            document.getElementById('employee-id').value = '';
            document.getElementById('employee-name').value = '';
            document.getElementById('search-result').style.display = 'none';
        }

        function saveAttendance() {
            const regular = document.getElementById('regular-hours').value;
            const overtime = document.getElementById('overtime-hours').value;
            const result = document.getElementById('attendance-result');
            
            if (regular || overtime) {
                result.innerHTML = `✓ Attendance saved: ${regular || 0} regular hours, ${overtime || 0} overtime hours`;
                result.style.display = 'block';
                
                // Update totals
                updateTotals();
            } else {
                result.innerHTML = '⚠ Please enter at least regular or overtime hours';
                result.style.display = 'block';
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
            }
        }

        function calculateTotal() {
            const regular = parseFloat(document.getElementById('regular-hours').value) || 0;
            const overtime = parseFloat(document.getElementById('overtime-hours').value) || 0;
            const total = regular + overtime;
            
            const result = document.getElementById('attendance-result');
            result.innerHTML = `📊 Total hours for today: ${total} hours (${regular} regular + ${overtime} overtime)`;
            result.style.display = 'block';
        }

        function updateTotals() {
            const regular = parseFloat(document.getElementById('regular-hours').value) || 0;
            const overtime = parseFloat(document.getElementById('overtime-hours').value) || 0;
            
            // Simulate monthly totals (multiply by working days)
            const workingDays = parseInt(document.getElementById('total-days').value) || 22;
            document.getElementById('total-regular').value = (regular * workingDays).toFixed(1);
            document.getElementById('total-overtime').value = (overtime * workingDays).toFixed(1);
        }

        function generateReport() {
            const result = document.getElementById('monthly-result');
            const totalRegular = document.getElementById('total-regular').value;
            const totalOvertime = document.getElementById('total-overtime').value;
            
            result.innerHTML = `📋 Monthly report generated: ${totalRegular} regular hours, ${totalOvertime} overtime hours`;
            result.style.display = 'block';
        }

        function submitMonthly() {
            const result = document.getElementById('monthly-result');
            result.innerHTML = '🎉 Monthly data submitted successfully!';
            result.style.display = 'block';
        }

        function fillSampleData() {
            document.getElementById('employee-id').value = 'PTRJ.241000089';
            document.getElementById('employee-name').value = 'Nursamsih';
            document.getElementById('department').value = 'hr';
            document.getElementById('regular-hours').value = '8';
            document.getElementById('overtime-hours').value = '2';
            document.getElementById('notes').value = 'Sample attendance entry for testing';
            updateTotals();
        }

        function clearAllFields() {
            const inputs = document.querySelectorAll('input:not([readonly]), select, textarea');
            inputs.forEach(input => {
                if (input.type === 'date') {
                    input.value = new Date().toISOString().split('T')[0];
                } else if (input.type === 'month') {
                    input.value = '2025-05';
                } else {
                    input.value = '';
                }
            });
            
            // Hide all result messages
            document.querySelectorAll('.success').forEach(el => el.style.display = 'none');
        }

        function simulateLoading() {
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Loading...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                alert('Loading simulation complete!');
            }, 2000);
        }

        function showAlert() {
            alert('This is a test alert for automation testing!');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Venus Auto Fill test page loaded');
            console.log('Available test elements:', {
                'Employee ID': '#employee-id',
                'Employee Name': '#employee-name',
                'Search Button': '#search-employee',
                'Regular Hours': '#regular-hours',
                'Overtime Hours': '#overtime-hours',
                'Save Button': '#save-attendance',
                'Department Select': '#department'
            });
        });
    </script>
</body>
</html>
