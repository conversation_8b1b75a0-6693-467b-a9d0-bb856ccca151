/**
 * Test API Connection Script
 * Run with: node test-connection.js
 */

const fetch = require('node-fetch');

async function testAPIConnection() {
    const baseUrls = [
        'http://**********:5173/api',
        'http://localhost:5173/api',
        'http://127.0.0.1:5173/api'
    ];
    
    const endpoints = ['/health', '/staging/data'];
    
    console.log('🧪 Testing API Connection...\n');
    
    for (const baseUrl of baseUrls) {
        console.log(`📍 Testing base URL: ${baseUrl}`);
        
        for (const endpoint of endpoints) {
            const fullUrl = `${baseUrl}${endpoint}`;
            
            try {
                const startTime = Date.now();
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'User-Agent': 'TestScript/1.0'
                    },
                    timeout: 10000
                });
                
                const responseTime = Date.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    console.log(`  ✅ ${endpoint}: Success (${response.status}) - ${responseTime}ms`);
                    
                    if (endpoint === '/staging/data' && data.data) {
                        console.log(`     📊 Data records: ${data.data.length}`);
                    }
                } else {
                    console.log(`  ❌ ${endpoint}: HTTP ${response.status} - ${response.statusText}`);
                }
                
            } catch (error) {
                console.log(`  ❌ ${endpoint}: ${error.message}`);
            }
        }
        
        console.log('');
    }
}

// Run the test
testAPIConnection().catch(console.error); 