/**
 * Google Apps Script for Venus Auto Fill - Full Sheet Data Retrieval
 * 
 * This script receives requests from the VenusHR14 Attendance Report System
 * and retrieves all data from a specified Google Spreadsheet sheet.
 * 
 * Features:
 * - Fetches all data from a specified sheet via GET request
 * - Handles CORS properly with OPTIONS request support
 * - Returns data in JSON format with headers and rows
 * 
 * Setup Instructions:
 * 1. Create a new Google Apps Script project
 * 2. Replace the default code with this script
 * 3. Deploy as web app with:
 *    - Execute as: Me
 *    - Who has access: Anyone
 * 4. Copy the deployment URL (ends with /exec) for use in your application
 * 
 * Example API Call:
 * GET https://script.google.com/macros/s/YOUR_DEPLOYMENT_ID/exec?action=getData&sheet=monthlyGridData_May_2025
 * 
 * <AUTHOR> adapted from Cursor AI Assistant
 * @version 2.2 - CORS Fixed and Full Data Retrieval
 */

/**
 * Handle GET requests
 * @param {Object} e - Event object containing request parameters
 * @returns {ContentService.TextOutput} JSON response
 */
function doGet(e) {
    return handleRequest(e);
  }
  
  /**
   * Handle POST requests
   * @param {Object} e - Event object containing request parameters
   * @returns {ContentService.TextOutput} JSON response
   */
  function doPost(e) {
    return handleRequest(e);
  }
  
  /**
   * Handle OPTIONS requests for CORS preflight
   * @returns {ContentService.TextOutput} Empty response with CORS headers
   */
  function doOptions() {
    return ContentService
      .createTextOutput('')
      .setMimeType(ContentService.MimeType.JSON);
  }
  
  /**
   * Main request handler
   * @param {Object} e - Event object containing request parameters
   * @returns {ContentService.TextOutput} Response with CORS headers
   */
  function handleRequest(e) {
    try {
      console.log('Request received:', e.parameter);
  
      // Enable CORS by setting appropriate response headers
      const output = ContentService.createTextOutput();
      output.setMimeType(ContentService.MimeType.JSON);
  
      let response;
  
      // Handle different actions
      const action = e.parameter.action || 'test';
      const sheetName = e.parameter.sheet || 'monthlyGridData_May_2025';
  
      switch (action) {
        case 'test':
          response = testConnection(sheetName);
          break;
  
        case 'getData':
          response = getSheetData(sheetName);
          break;
  
        case 'getSheets':
          response = getAvailableSheets();
          break;
  
        case 'validateData':
          response = validateSheetData(sheetName);
          break;
  
        default:
          response = {
            success: false,
            error: `Unknown action: ${action}. Available actions: test, getData, getSheets, validateData`
          };
      }
  
      output.setContent(JSON.stringify(response));
      return output;
  
    } catch (error) {
      console.error('Error in handleRequest:', error);
      const errorResponse = {
        success: false,
        error: error.toString(),
        timestamp: new Date().toISOString()
      };
      const output = ContentService
        .createTextOutput(JSON.stringify(errorResponse))
        .setMimeType(ContentService.MimeType.JSON);
      return output;
    }
  }
  
  /**
   * Test connection to the spreadsheet and sheet
   * @param {string} sheetName - Name of the sheet to test
   * @returns {Object} Test result
   */
  function testConnection(sheetName) {
    try {
      console.log(`Testing connection to sheet: ${sheetName}`);
      const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
      const sheet = spreadsheet.getSheetByName(sheetName);
  
      if (!sheet) {
        const availableSheets = getSheetNames();
        return {
          success: false,
          error: `Sheet "${sheetName}" not found. Available sheets: ${availableSheets.join(', ')}`
        };
      }
  
      const lastRow = sheet.getLastRow();
      const lastColumn = sheet.getLastColumn();
  
      console.log(`Connection successful: ${lastRow} rows, ${lastColumn} columns`);
  
      return {
        success: true,
        message: 'Connection successful',
        sheetInfo: {
          name: sheetName,
          rows: lastRow,
          columns: lastColumn,
          spreadsheetId: spreadsheet.getId(),
          spreadsheetName: spreadsheet.getName()
        }
      };
    } catch (error) {
      console.error('Test connection error:', error);
      return {
        success: false,
        error: `Connection test failed: ${error.toString()}`
      };
    }
  }
  
  /**
   * Get all data from the specified sheet
   * @param {string} sheetName - Name of the sheet to retrieve data from
   * @returns {Object} Sheet data or error
   */
  function getSheetData(sheetName) {
    try {
      console.log(`Fetching all data from sheet: ${sheetName}`);
      const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
      const sheet = spreadsheet.getSheetByName(sheetName);
  
      if (!sheet) {
        return {
          success: false,
          error: `Sheet "${sheetName}" not found`
        };
      }
  
      const lastRow = sheet.getLastRow();
      const lastColumn = sheet.getLastColumn();
  
      if (lastRow === 0) {
        return {
          success: false,
          error: 'Sheet is empty'
        };
      }
  
      // Get all data from the sheet
      const range = sheet.getRange(1, 1, lastRow, lastColumn);
      const values = range.getValues();
  
      // Extract headers and data
      const headers = values[0];
      const dataRows = values.slice(1).filter(row => 
        row.some(cell => cell !== null && cell !== undefined && cell !== '')
      );
  
      // Convert rows to objects with header keys
      const processedData = dataRows.map(row => {
        const rowData = {};
        headers.forEach((header, index) => {
          rowData[header] = row[index] || '';
        });
        return rowData;
      });
  
      console.log(`Retrieved ${processedData.length} data rows from sheet "${sheetName}"`);
  
      return {
        success: true,
        data: processedData,
        headers: headers,
        metadata: {
          sheetName: sheetName,
          totalRows: processedData.length,
          totalColumns: headers.length,
          lastUpdated: new Date().toISOString(),
          spreadsheetId: spreadsheet.getId()
        }
      };
    } catch (error) {
      console.error('Get sheet data error:', error);
      return {
        success: false,
        error: `Failed to retrieve data: ${error.toString()}`
      };
    }
  }
  
  /**
   * Get available sheets in the spreadsheet
   * @returns {Object} List of available sheets
   */
  function getAvailableSheets() {
    try {
      const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
      const sheets = spreadsheet.getSheets();
      const sheetNames = sheets.map(sheet => sheet.getName());
  
      return {
        success: true,
        sheets: sheetNames,
        count: sheetNames.length
      };
    } catch (error) {
      console.error('Get available sheets error:', error);
      return {
        success: false,
        error: `Failed to get available sheets: ${error.toString()}`
      };
    }
  }
  
  /**
   * Validate sheet data structure
   * @param {string} sheetName - Name of the sheet to validate
   * @returns {Object} Validation result
   */
  function validateSheetData(sheetName) {
    try {
      const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
      const sheet = spreadsheet.getSheetByName(sheetName);
  
      if (!sheet) {
        return {
          success: false,
          error: `Sheet "${sheetName}" not found`
        };
      }
  
      const range = sheet.getDataRange();
      const values = range.getValues();
  
      if (values.length === 0) {
        return {
          success: false,
          error: 'Sheet is empty'
        };
      }
  
      const headers = values[0];
      const expectedHeaders = ['No', 'Employee ID', 'Employee Name'];
      const hasRequiredHeaders = expectedHeaders.every(header => 
        headers.some(h => h.toString().toLowerCase().includes(header.toLowerCase()))
      );
  
      return {
        success: true,
        validation: {
          hasData: values.length > 1,
          hasHeaders: headers.length > 0,
          hasRequiredHeaders: hasRequiredHeaders,
          rowCount: values.length,
          columnCount: headers.length,
          headers: headers
        }
      };
    } catch (error) {
      console.error('Validate sheet data error:', error);
      return {
        success: false,
        error: `Failed to validate sheet: ${error.toString()}`
      };
    }
  }
  
  /**
   * Get sheet names helper function
   * @returns {Array} Array of sheet names
   */
  function getSheetNames() {
    try {
      const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
      const sheets = spreadsheet.getSheets();
      return sheets.map(sheet => sheet.getName());
    } catch (error) {
      console.error('Get sheet names error:', error);
      return [];
    }
  }
  
  /**
   * Test function for manual testing
   * @returns {Object} Test result
   */
  function testScript() {
    console.log('Testing Venus Auto Fill Google Apps Script...');
  
    // Test connection
    const testResult = testConnection('monthlyGridData_May_2025');
    console.log('Test connection result:', testResult);
  
    // Test get data
    const dataResult = getSheetData('monthlyGridData_May_2025');
    console.log('Get data result:', dataResult);
  
    return {
      testConnection: testResult,
      getData: dataResult
    };
  }