<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venus-Millware AutoFill</title>
    <link rel="stylesheet" href="styles/popup.css">
    <style>
        /* Additional styles for visual flow integration */
        .visual-flow-section {
            margin-top: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            border: 1px solid #cbd5e1;
        }

        .visual-flow-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .visual-flow-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .flow-toggle-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            background: #3b82f6;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .flow-toggle-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .visual-flow-container {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            padding: 15px;
            position: relative;
            overflow-y: auto;
            border: 2px solid #e1e5e9;
            margin-bottom: 15px;
        }

        .flow-controls-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .flow-control-group {
            display: flex;
            gap: 8px;
        }

        .flow-control-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.9);
            color: #374151;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .flow-control-btn:hover {
            background: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .flow-control-btn.primary {
            background: #22c55e;
            color: white;
            border-color: #16a34a;
        }

        .flow-control-btn.primary:hover {
            background: #16a34a;
        }

        .flow-control-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #dc2626;
        }

        .flow-control-btn.danger:hover {
            background: #dc2626;
        }

        .flow-control-btn.warning {
            background: #f59e0b;
            color: white;
            border-color: #d97706;
        }

        .flow-control-btn.warning:hover {
            background: #d97706;
        }

        .flow-status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }

        .flow-progress-container {
            flex: 1;
            margin: 0 15px;
        }

        .flow-progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .flow-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Mini flow step styles */
        .mini-flow-step {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin: 8px auto;
            width: 250px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .mini-flow-step.executing {
            border-color: #ff4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fff5f5 100%);
            animation: mini-pulse-executing 1.5s infinite;
        }

        .mini-flow-step.completed {
            border-color: #22c55e;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        }

        .mini-flow-step.error {
            border-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
        }

        .mini-flow-step.pending {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
        }

        @keyframes mini-pulse-executing {
            0% {
                box-shadow: 0 2px 8px rgba(255, 68, 68, 0.2);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 4px 16px rgba(255, 68, 68, 0.4);
                transform: scale(1.02);
            }
            100% {
                box-shadow: 0 2px 8px rgba(255, 68, 68, 0.2);
                transform: scale(1);
            }
        }

        .mini-step-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .mini-step-icon {
            font-size: 18px;
            margin-right: 10px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(59, 130, 246, 0.1);
        }

        .mini-step-info {
            flex: 1;
        }

        .mini-step-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .mini-step-subtitle {
            font-size: 11px;
            color: #6b7280;
            margin: 2px 0 0 0;
        }

        .mini-step-status {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #d1d5db;
            background: white;
            position: relative;
        }

        .mini-step-status.executing {
            background: #ff4444;
            border-color: #ff4444;
            animation: spin 1s linear infinite;
        }

        .mini-step-status.completed {
            background: #22c55e;
            border-color: #22c55e;
        }

        .mini-step-status.error {
            background: #ef4444;
            border-color: #ef4444;
        }

        .mini-step-status.pending {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .mini-step-status.completed::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .mini-step-status.error::after {
            content: '✕';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
            font-weight: bold;
        }

        .mini-step-connector {
            width: 2px;
            height: 15px;
            background: #d1d5db;
            margin: 0 auto;
        }

        .mini-step-connector.completed {
            background: linear-gradient(to bottom, #22c55e, #16a34a);
        }

        .hidden {
            display: none;
        }

        .millware-section {
            margin-top: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border-radius: 12px;
            border: 1px solid #10b981;
        }

        .millware-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .millware-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .millware-title {
            font-size: 16px;
            font-weight: 600;
            color: #065f46;
            margin: 0;
        }

        .millware-subtitle {
            font-size: 12px;
            color: #047857;
            margin: 2px 0 0 0;
        }

        .millware-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .millware-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .millware-btn.primary {
            background: #10b981;
            color: white;
        }

        .millware-btn.primary:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .millware-btn.secondary {
            background: white;
            color: #059669;
            border: 1px solid #10b981;
        }

        .millware-btn.secondary:hover {
            background: #f0fdf4;
        }

        .tab-content.flow-tab {
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <img src="icons/icon32.png" alt="Venus-Millware AutoFill" class="logo">
            <h1>Venus-Millware AutoFill</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Siap</span>
            </div>
        </div>

        <!-- Millware Automation Section -->
        <div class="millware-section">
            <div class="millware-header">
                <div class="millware-icon">🏭</div>
                <div>
                    <h3 class="millware-title">Millware System Automation</h3>
                    <p class="millware-subtitle">Login otomatis dan navigasi ke Task Register</p>
                </div>
            </div>
            
            <div class="millware-controls">
                <button class="millware-btn primary" id="runMillwareAutomation">
                    🚀 Jalankan Otomatisasi
                </button>
                <button class="millware-btn secondary" id="showVisualFlow">
                    📊 Tampilkan Flow
                </button>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="tab-button active" data-tab="config">Konfigurasi</button>
            <button class="tab-button" data-tab="flow">Visual Flow</button>
            <button class="tab-button" data-tab="data">Data</button>
            <button class="tab-button" data-tab="execution">Eksekusi</button>
        </div>

        <!-- Configuration Tab -->
        <div class="tab-content active" id="config">
            <div class="section">
                <h3>Konfigurasi Millware System</h3>
                <div class="form-group">
                    <label for="targetUrl">Millware URL:</label>
                    <input type="url" id="targetUrl" 
                           value="http://millwarep3.rebinmas.com:8003/"
                           placeholder="http://millwarep3.rebinmas.com:8003/">
                </div>
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" 
                           value="adm075"
                           placeholder="adm075">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" 
                           value="adm075"
                           placeholder="Masukkan password">
                </div>
                <button class="btn btn-primary" id="saveConfig">Simpan Konfigurasi</button>
            </div>

            <div class="section">
                <h3>Pengaturan Otomatisasi</h3>
                <div class="form-group">
                    <label for="stepDelay">Jeda Antar Langkah (ms):</label>
                    <input type="number" id="stepDelay" 
                           value="1000" 
                           min="500" 
                           max="5000"
                           placeholder="1000">
                    <small>Jeda waktu antara setiap langkah otomatisasi</small>
                </div>
                <div class="form-group">
                    <label for="enableVisualFeedback">
                        <input type="checkbox" id="enableVisualFeedback" checked>
                        Aktifkan highlight visual element
                    </label>
                </div>
                <div class="form-group">
                    <label for="enableScreenshots">
                        <input type="checkbox" id="enableScreenshots">
                        Ambil screenshot setiap langkah
                    </label>
                </div>
            </div>
        </div>

        <!-- Visual Flow Tab -->
        <div class="tab-content flow-tab" id="flow">
            <div class="visual-flow-section">
                <div class="visual-flow-header">
                    <h3 class="visual-flow-title">Millware Login & Task Register Flow</h3>
                    <button class="flow-toggle-btn" id="toggleFlowView">Maximize</button>
                </div>

                <div class="visual-flow-container" id="visualFlowContainer">
                    <!-- Flow steps will be dynamically generated here -->
                    <div id="flowStepsContainer">
                        <!-- Mini flow steps will be rendered here -->
                    </div>
                </div>

                <div class="flow-controls-bar">
                    <div class="flow-control-group">
                        <button class="flow-control-btn primary" id="startFlowBtn">▶️ Mulai</button>
                        <button class="flow-control-btn warning" id="pauseFlowBtn">⏸️ Jeda</button>
                        <button class="flow-control-btn danger" id="stopFlowBtn">⏹️ Berhenti</button>
                    </div>

                    <div class="flow-progress-container">
                        <div class="flow-progress-bar">
                            <div class="flow-progress-fill" id="flowProgressFill"></div>
                        </div>
                    </div>

                    <div class="flow-status-bar">
                        <span>Progress: <span id="flowProgressText">0/9</span></span>
                        <span>Status: <span id="flowStatus">Siap</span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Preview Tab -->
        <div class="tab-content" id="data">
            <div class="section">
                <h3>Flow Definition JSON</h3>
                <div class="data-controls">
                    <button class="btn btn-primary" id="loadFlowDefinition">Load Flow</button>
                    <button class="btn btn-secondary" id="exportFlowDefinition">Export JSON</button>
                    <button class="btn btn-secondary" id="validateFlow">Validate</button>
                </div>
                <div class="data-status" id="flowDataStatus">
                    <span>Flow definition siap digunakan</span>
                </div>
                <div class="data-preview" id="flowDataPreview">
                    <pre id="flowJsonDisplay">Loading flow definition...</pre>
                </div>
            </div>
        </div>

        <!-- Execution Tab -->
        <div class="tab-content" id="execution">
            <div class="section">
                <h3>Real-time Execution Monitor</h3>
                <div class="execution-controls">
                    <button class="btn btn-success" id="runExecution">🚀 Jalankan Millware Automation</button>
                    <button class="btn btn-warning" id="pauseExecution">⏸️ Jeda</button>
                    <button class="btn btn-danger" id="stopExecution">⏹️ Berhenti</button>
                </div>
                <div class="execution-status" id="executionStatus">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="status-text" id="executionStatusText">Siap untuk menjalankan otomatisasi Millware</div>
                </div>
                <div class="execution-log" id="executionLog">
                    <!-- Execution logs will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/api-service.js"></script>
    <script src="scripts/millware-visual-flow.js"></script>
    <script src="scripts/popup.js"></script>
</body>
</html> 