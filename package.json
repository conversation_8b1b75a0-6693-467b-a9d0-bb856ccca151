{"name": "venus-millware-autofill-staging-api", "version": "1.0.0", "description": "Staging API server untuk Venus-Millware AutoFill - alat otomatisasi pengisian form dan input data", "main": "staging-api-server.js", "scripts": {"start": "node staging-api-server.js", "dev": "nodemon staging-api-server.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["chrome-extension", "automation", "venus-millware", "autofill", "form-filling", "timesheet", "staging-api", "testing"], "author": "Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}