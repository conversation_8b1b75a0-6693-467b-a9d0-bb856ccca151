/**
 * API Connectivity Test Script for Venus Auto Fill Extension
 * 
 * This script tests the staging API endpoint connectivity and diagnoses issues
 * Run this in the browser console to debug API connectivity problems
 */

class APIConnectivityTester {
    constructor() {
        this.apiEndpoint = 'http://localhost:5173/api/staging/data';
        this.results = {
            tests: [],
            summary: {},
            recommendations: []
        };
        this.testResults = [];
        this.startTime = null;
        this.endTime = null;
    }

    async runAllTests() {
        console.log('🔍 Starting API Connectivity Tests...');
        console.log('=' .repeat(50));
        
        await this.testBasicConnectivity();
        await this.testCORSHeaders();
        await this.testResponseFormat();
        await this.testFromExtensionContext();
        
        this.generateSummary();
        this.generateRecommendations();
        this.displayResults();
        
        return this.results;
    }

    async testBasicConnectivity() {
        console.log('📡 Test 1: Basic Connectivity');
        
        try {
            const startTime = Date.now();
            const response = await fetch(this.apiEndpoint, {
                method: 'GET',
                mode: 'cors',
                credentials: 'omit'
            });
            const endTime = Date.now();
            
            const result = {
                test: 'Basic Connectivity',
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                responseTime: endTime - startTime,
                headers: Object.fromEntries(response.headers.entries())
            };
            
            if (response.ok) {
                const data = await response.json();
                result.dataPreview = {
                    hasData: !!data,
                    isArray: Array.isArray(data),
                    hasDataProperty: !!(data && data.data),
                    recordCount: data.data ? data.data.length : (Array.isArray(data) ? data.length : 0)
                };
                console.log('✅ Basic connectivity successful');
                console.log(`   Response time: ${result.responseTime}ms`);
                console.log(`   Records found: ${result.dataPreview.recordCount}`);
            } else {
                console.log('❌ Basic connectivity failed');
                console.log(`   Status: ${response.status} ${response.statusText}`);
            }
            
            this.results.tests.push(result);
        } catch (error) {
            const result = {
                test: 'Basic Connectivity',
                success: false,
                error: error.message,
                errorType: error.name
            };
            
            console.log('❌ Basic connectivity failed with error');
            console.log(`   Error: ${error.message}`);
            
            this.results.tests.push(result);
        }
    }

    async testCORSHeaders() {
        console.log('🌐 Test 2: CORS Headers');
        
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'OPTIONS',
                headers: {
                    'Origin': 'chrome-extension://test',
                    'Access-Control-Request-Method': 'GET',
                    'Access-Control-Request-Headers': 'Content-Type'
                }
            });
            
            const corsHeaders = {
                'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
                'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
                'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
                'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
            };
            
            const result = {
                test: 'CORS Headers',
                success: response.ok,
                status: response.status,
                corsHeaders: corsHeaders,
                allowsOrigin: corsHeaders['access-control-allow-origin'] === '*' || 
                             corsHeaders['access-control-allow-origin']?.includes('chrome-extension'),
                allowsGET: corsHeaders['access-control-allow-methods']?.includes('GET')
            };
            
            if (result.allowsOrigin && result.allowsGET) {
                console.log('✅ CORS configuration looks good');
            } else {
                console.log('⚠️ CORS configuration may have issues');
                console.log(`   Allows origin: ${result.allowsOrigin}`);
                console.log(`   Allows GET: ${result.allowsGET}`);
            }
            
            this.results.tests.push(result);
        } catch (error) {
            const result = {
                test: 'CORS Headers',
                success: false,
                error: error.message
            };
            
            console.log('❌ CORS test failed');
            console.log(`   Error: ${error.message}`);
            
            this.results.tests.push(result);
        }
    }

    async testResponseFormat() {
        console.log('📋 Test 3: Response Format Validation');
        
        try {
            const response = await fetch(this.apiEndpoint);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const contentType = response.headers.get('content-type');
            const text = await response.text();
            
            let data;
            try {
                data = JSON.parse(text);
            } catch (parseError) {
                throw new Error(`Invalid JSON: ${parseError.message}`);
            }
            
            const result = {
                test: 'Response Format',
                success: true,
                contentType: contentType,
                isValidJSON: true,
                dataStructure: this.analyzeDataStructure(data),
                sampleRecord: this.getSampleRecord(data)
            };
            
            console.log('✅ Response format validation successful');
            console.log(`   Content-Type: ${contentType}`);
            console.log(`   Data structure: ${result.dataStructure.type}`);
            console.log(`   Record count: ${result.dataStructure.recordCount}`);
            
            this.results.tests.push(result);
        } catch (error) {
            const result = {
                test: 'Response Format',
                success: false,
                error: error.message
            };
            
            console.log('❌ Response format validation failed');
            console.log(`   Error: ${error.message}`);
            
            this.results.tests.push(result);
        }
    }

    async testFromExtensionContext() {
        console.log('🔌 Test 4: Extension Context Simulation');
        
        try {
            // Simulate extension fetch with exact headers used in background script
            const response = await fetch(this.apiEndpoint, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                mode: 'cors',
                credentials: 'omit'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            const result = {
                test: 'Extension Context Simulation',
                success: true,
                status: response.status,
                dataReceived: !!data,
                extensionCompatible: this.checkExtensionCompatibility(data)
            };
            
            console.log('✅ Extension context simulation successful');
            console.log(`   Extension compatible: ${result.extensionCompatible}`);
            
            this.results.tests.push(result);
        } catch (error) {
            const result = {
                test: 'Extension Context Simulation',
                success: false,
                error: error.message,
                errorType: error.name
            };
            
            console.log('❌ Extension context simulation failed');
            console.log(`   Error: ${error.message}`);
            
            this.results.tests.push(result);
        }
    }

    analyzeDataStructure(data) {
        if (Array.isArray(data)) {
            return {
                type: 'direct_array',
                recordCount: data.length,
                hasEmployeeData: data.length > 0 && data[0].employee_id !== undefined
            };
        } else if (data && data.success && Array.isArray(data.data)) {
            return {
                type: 'wrapped_response',
                recordCount: data.data.length,
                totalRecords: data.total_records,
                hasEmployeeData: data.data.length > 0 && data.data[0].employee_id !== undefined
            };
        } else {
            return {
                type: 'unknown',
                recordCount: 0,
                hasEmployeeData: false
            };
        }
    }

    getSampleRecord(data) {
        let records = [];
        
        if (Array.isArray(data) && data.length > 0) {
            records = data;
        } else if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
            records = data.data;
        }
        
        return records.length > 0 ? records[0] : null;
    }

    checkExtensionCompatibility(data) {
        const structure = this.analyzeDataStructure(data);
        return structure.hasEmployeeData && structure.recordCount > 0;
    }

    generateSummary() {
        const passedTests = this.results.tests.filter(test => test.success).length;
        const totalTests = this.results.tests.length;
        
        this.results.summary = {
            totalTests: totalTests,
            passedTests: passedTests,
            failedTests: totalTests - passedTests,
            overallSuccess: passedTests === totalTests,
            criticalIssues: this.results.tests.filter(test => 
                !test.success && ['Basic Connectivity', 'Extension Context Simulation'].includes(test.test)
            ).length
        };
    }

    generateRecommendations() {
        const failedTests = this.results.tests.filter(test => !test.success);
        
        if (failedTests.length === 0) {
            this.results.recommendations.push('✅ All tests passed! API connectivity should work properly.');
            return;
        }
        
        failedTests.forEach(test => {
            switch (test.test) {
                case 'Basic Connectivity':
                    if (test.error?.includes('Failed to fetch')) {
                        this.results.recommendations.push('❌ Network connectivity issue: Check if the staging server is running and accessible');
                        this.results.recommendations.push('🔧 Verify the IP address ********** is correct and reachable');
                        this.results.recommendations.push('🔧 Check if port 5173 is open and not blocked by firewall');
                    } else {
                        this.results.recommendations.push(`❌ HTTP error ${test.status}: Check server configuration`);
                    }
                    break;
                    
                case 'CORS Headers':
                    this.results.recommendations.push('❌ CORS configuration issue: Server needs to allow cross-origin requests');
                    this.results.recommendations.push('🔧 Add Access-Control-Allow-Origin: * header to the API server');
                    break;
                    
                case 'Response Format':
                    this.results.recommendations.push('❌ Invalid response format: API is not returning valid JSON');
                    this.results.recommendations.push('🔧 Check API server logs for errors');
                    break;
                    
                case 'Extension Context Simulation':
                    this.results.recommendations.push('❌ Extension compatibility issue: Response format not compatible with extension');
                    this.results.recommendations.push('🔧 Update background script to handle the actual response format');
                    break;
            }
        });
    }

    displayResults() {
        console.log('\n' + '=' .repeat(50));
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('=' .repeat(50));
        
        console.log(`Total Tests: ${this.results.summary.totalTests}`);
        console.log(`Passed: ${this.results.summary.passedTests}`);
        console.log(`Failed: ${this.results.summary.failedTests}`);
        console.log(`Overall Success: ${this.results.summary.overallSuccess ? '✅' : '❌'}`);
        
        if (this.results.recommendations.length > 0) {
            console.log('\n📋 RECOMMENDATIONS:');
            this.results.recommendations.forEach(rec => console.log(`   ${rec}`));
        }
        
        console.log('\n🔍 Detailed test results available in returned object');
    }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
    console.log('🚀 Starting API Connectivity Tests...');
    const tester = new APIConnectivityTester();
    tester.runAllTests().then(results => {
        window.apiTestResults = results;
        console.log('\n💾 Results saved to window.apiTestResults');
    });
}
