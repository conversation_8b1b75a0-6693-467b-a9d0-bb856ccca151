{"flow_id": "post_login_automation_v1", "name": "Post-Login Automation Flow", "description": "Automation sequence that performs specific steps after successful login", "version": "1.0.0", "author": "Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)", "created_date": "2024-01-20", "target_system": {"name": "Millware ERP System", "base_url": "http://millwarep3.rebinmas.com:8003/", "authentication_required": true}, "flow_metadata": {"estimated_duration": "15-30 seconds", "complexity": "medium", "success_rate": "high", "dependencies": ["successful_login", "target_server_accessible"]}, "configuration": {"delays": {"ok_element_delay": 200, "navigation_delay": 300, "page_load_timeout": 10000, "ok_element_timeout": 5000, "navigation_timeout": 15000, "new_element_timeout": 10000, "new_element_wait_after": 2000}, "target_url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx", "retry_attempts": 3, "error_handling": {"continue_on_ok_not_found": true, "screenshot_on_error": true, "detailed_logging": true}}, "flow_steps": [{"step_id": 1, "name": "wait_for_login_completion", "type": "wait", "description": "Wait for login process to complete and page to stabilize", "parameters": {"duration": 2000, "waitFor": "navigation", "timeout": 10000}, "visual_node": {"title": "⏳ Wait for Login Completion", "subtitle": "Ensuring login process is complete", "icon": "⏳", "color": "#f59e0b"}, "error_handling": {"continue_on_error": true, "retry_attempts": 1}}, {"step_id": 2, "name": "search_and_click_ok", "type": "text_search_click", "description": "Search for element containing 'ok' text and click it", "parameters": {"searchText": "ok", "caseSensitive": false, "elementTypes": ["button", "input", "a", "div", "span"], "timeout": 5000, "clickDelay": 200, "scrollToElement": true}, "visual_node": {"title": "🔍 Search and Click 'OK'", "subtitle": "Finding and clicking OK element", "icon": "🔍", "color": "#10b981"}, "conditional": {"enabled": true, "condition": {"type": "element_found", "continue_if_not_found": true}}, "error_handling": {"continue_on_error": true, "retry_attempts": 2, "log_level": "warning"}}, {"step_id": 3, "name": "delay_after_ok_click", "type": "wait", "description": "Add configurable delay after clicking OK", "parameters": {"duration": 200}, "visual_node": {"title": "⏱️ Delay After OK", "subtitle": "Brief pause after OK click", "icon": "⏱️", "color": "#6b7280"}}, {"step_id": 4, "name": "navigate_to_target_page", "type": "navigate", "description": "Navigate to the specific target URL", "parameters": {"url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx", "waitForLoad": true, "timeout": 15000}, "visual_node": {"title": "🌐 Navigate to Target Page", "subtitle": "Going to Task Register List", "icon": "🌐", "color": "#3b82f6"}, "error_handling": {"continue_on_error": false, "retry_attempts": 3, "retry_delay": 2000}}, {"step_id": 5, "name": "wait_for_page_load", "type": "wait_for_element", "description": "Wait for target page to load completely", "parameters": {"selectors": ["body", "#MainContent", ".content-wrapper"], "timeout": 15000, "any_selector": true}, "visual_node": {"title": "⏳ Wait for Page Load", "subtitle": "Ensuring page is ready", "icon": "⏳", "color": "#8b5cf6"}}, {"step_id": 6, "name": "delay_after_navigation", "type": "wait", "description": "Add configurable delay after navigation", "parameters": {"duration": 300}, "visual_node": {"title": "⏱️ Post-Navigation Delay", "subtitle": "Stabilizing after navigation", "icon": "⏱️", "color": "#6b7280"}}, {"step_id": 7, "name": "search_and_click_new", "type": "text_search_click", "description": "Search for element containing 'new' text and click it", "parameters": {"searchText": "new", "caseSensitive": false, "elementTypes": ["button", "input", "a"], "timeout": 10000, "scrollToElement": true, "waitAfterClick": 2000}, "visual_node": {"title": "🔍 Search and Click 'New'", "subtitle": "Finding and clicking New button", "icon": "🔍", "color": "#22c55e"}, "error_handling": {"continue_on_error": false, "retry_attempts": 3, "retry_delay": 1000}}, {"step_id": 8, "name": "wait_for_new_action_result", "type": "wait", "description": "Wait for any resulting page changes or modal dialogs", "parameters": {"duration": 2000, "waitFor": "any_change"}, "visual_node": {"title": "⏳ Wait for New Action", "subtitle": "Waiting for page response", "icon": "⏳", "color": "#f59e0b"}}, {"step_id": 9, "name": "initialize_text_search", "type": "text_search", "description": "Initialize text search functionality for user", "parameters": {"searchText": "", "caseSensitive": false, "highlightMatches": true, "showNavigationControls": true, "maxMatches": 100}, "visual_node": {"title": "🔍 Initialize Text Search", "subtitle": "Setting up search functionality", "icon": "🔍", "color": "#6366f1"}, "conditional": {"enabled": true, "condition": {"type": "feature_enabled", "feature": "text_search_ui"}}}, {"step_id": 10, "name": "completion_notification", "type": "execute_script", "description": "Mark automation as completed and notify user", "parameters": {"script": "console.log('🎉 Post-login automation completed successfully!'); window.postLoginAutomationComplete = true; if (window.venusTextSearch) { window.venusTextSearch.showSearchUI(); }", "wait_for_result": true, "timeout": 2000}, "visual_node": {"title": "🎉 Automation Complete", "subtitle": "All steps completed successfully", "icon": "🎉", "color": "#22c55e"}}], "flow_connections": [{"from": "step_1", "to": "step_2"}, {"from": "step_2", "to": "step_3"}, {"from": "step_3", "to": "step_4"}, {"from": "step_4", "to": "step_5"}, {"from": "step_5", "to": "step_6"}, {"from": "step_6", "to": "step_7"}, {"from": "step_7", "to": "step_8"}, {"from": "step_8", "to": "step_9"}, {"from": "step_9", "to": "step_10"}], "error_recovery": {"global_timeout": 120000, "screenshot_on_error": true, "retry_entire_flow": false, "recovery_actions": [{"error_type": "element_not_found", "action": "wait_and_retry", "max_retries": 3, "retry_delay": 2000}, {"error_type": "navigation_timeout", "action": "refresh_and_retry", "max_retries": 2}, {"error_type": "text_search_failed", "action": "continue_with_warning", "max_retries": 1}]}, "success_metrics": {"completion_time": "target_under_30s", "success_rate": "target_above_90%", "user_satisfaction": "high"}, "keyboard_shortcuts": {"ctrl_f": "activate_text_search", "f3": "next_search_result", "shift_f3": "previous_search_result", "escape": "close_search_ui"}}