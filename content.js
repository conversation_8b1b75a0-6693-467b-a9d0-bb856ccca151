// Venus-Millware AutoFill - Content Script
// Alat otomatisasi untuk pengisian form dan input data otomatis
// Developer: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)

class AutomationBotContent {
    constructor() {
        this.isExecuting = false;
        this.isPaused = false;
        this.currentExecution = null;
        this.automationData = null;
        this.flowEvents = [];
        this.extractedData = {};
        this.executionResults = {
            success: false,
            eventsExecuted: 0,
            errors: [],
            extractedData: {},
            startTime: null,
            endTime: null
        };
        
        this.init();
    }

    init() {
        console.log('Venus-Millware AutoFill Content Script initialized');
        this.setupMessageListener();
        this.injectAdvancedHelpers();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep the message channel open for async response
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'executeAutomationFlow':
                    await this.executeAutomationFlow(message, sendResponse);
                    break;

                case 'pauseAutomation':
                    this.pauseAutomation();
                    sendResponse({ success: true });
                    break;

                case 'stopAutomation':
                    this.stopAutomation();
                    sendResponse({ success: true });
                    break;

                case 'testEvent':
                    await this.testSingleEvent(message.event, message.index, sendResponse);
                    break;

                case 'extractData':
                    await this.extractPageData(message.selectors, sendResponse);
                    break;

                case 'getPageInfo':
                    sendResponse(this.getPageInfo());
                    break;

                default:
                    console.warn('Unknown automation action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling automation message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeAutomationFlow(message, sendResponse) {
        if (this.isExecuting) {
            sendResponse({ success: false, error: 'Automation already running' });
            return;
        }

        console.log('🚀 Starting automation flow execution');
        
        this.isExecuting = true;
        this.isPaused = false;
        this.flowEvents = message.flowEvents || [];
        this.automationData = message.automationData || [];
        this.executionResults = {
            success: false,
            eventsExecuted: 0,
            errors: [],
            extractedData: {},
            startTime: new Date().toISOString(),
            endTime: null,
            executionId: message.metadata?.executionId || this.generateExecutionId()
        };

        try {
            // Send initial response
            sendResponse({ success: true, message: 'Automation flow started' });

            // Execute the flow
            await this.runFlowSequence();

            // Mark as successful if we got here
            this.executionResults.success = true;
            this.executionResults.endTime = new Date().toISOString();

            this.notifyCompletion(true, null, this.executionResults);

        } catch (error) {
            console.error('❌ Automation flow execution failed:', error);
            this.executionResults.success = false;
            this.executionResults.endTime = new Date().toISOString();
            this.executionResults.errors.push({
                message: error.message,
                timestamp: new Date().toISOString(),
                stack: error.stack
            });
            
            this.notifyCompletion(false, error.message, this.executionResults);
        } finally {
            this.isExecuting = false;
        }
    }

    async runFlowSequence() {
        console.log(`📋 Executing ${this.flowEvents.length} automation events`);
        
        for (let i = 0; i < this.flowEvents.length; i++) {
            if (!this.isExecuting || this.isPaused) {
                console.log('⏸️ Automation paused or stopped');
                break;
            }

            const event = this.flowEvents[i];
            console.log(`🎯 Executing event ${i + 1}/${this.flowEvents.length}: ${event.type}`);

            try {
                await this.executeEvent(event, i);
                this.executionResults.eventsExecuted++;

                // Update progress
                const progress = Math.round(((i + 1) / this.flowEvents.length) * 100);
                this.notifyProgress(progress, `Completed event ${i + 1}: ${event.type}`);

                // Add delay between events
                await this.delay(100 + Math.random() * 100);

            } catch (error) {
                console.error(`❌ Event ${i + 1} failed:`, error);
                this.executionResults.errors.push({
                    eventIndex: i,
                    eventType: event.type,
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
                
                // Decide whether to continue or stop based on error type
                if (this.isCriticalError(error)) {
                    throw error;
                }
                
                // Continue with next event for non-critical errors
                console.log('⚠️ Non-critical error, continuing with next event');
            }
        }
    }

    async executeEvent(event, index) {
        console.log(`🔧 Executing ${event.type} event:`, event);

        // Evaluate conditional logic if present
        if (event.condition) {
            const conditionResult = await this.evaluateEventCondition(event.condition);
            if (!conditionResult) {
                console.log(`⏭️ Skipping event ${index + 1} - condition not met: ${JSON.stringify(event.condition)}`);
                return;
            }
        }

        // Execute the event based on type
        switch (event.type) {
            case 'click':
                await this.executeClickEvent(event);
                break;
                
            case 'input':
                await this.executeInputEvent(event);
                break;
                
            case 'wait':
                await this.executeWaitEvent(event);
                break;
                
            case 'extract':
                await this.executeExtractEvent(event);
                break;
                
            case 'navigate':
                await this.executeNavigateEvent(event);
                break;
                
            case 'scroll':
                await this.executeScrollEvent(event);
                break;
                
            case 'condition':
                await this.executeConditionEvent(event, index);
                break;

            // New event types
            case 'open_to':
                await this.executeOpenToEvent(event);
                break;

            case 'wait_for_element':
                await this.executeWaitForElementEvent(event);
                break;

            case 'hover':
                await this.executeHoverEvent(event);
                break;

            case 'scroll_to':
                await this.executeScrollToEvent(event);
                break;

            case 'select_option':
                await this.executeSelectOptionEvent(event);
                break;

            case 'alert_handle':
                await this.executeAlertHandleEvent(event);
                break;

            case 'screenshot':
                await this.executeScreenshotEvent(event);
                break;

            case 'form_fill':
                await this.executeFormFillEvent(event);
                break;

            case 'tab_switch':
                await this.executeTabSwitchEvent(event);
                break;

            case 'loop':
                await this.executeLoopEvent(event, index);
                break;

            case 'if_then_else':
                await this.executeIfThenElseEvent(event, index);
                break;

            case 'conditional_action':
                await this.executeConditionalActionEvent(event, index);
                break;

            case 'variable_set':
                await this.executeVariableSetEvent(event);
                break;

            case 'data_extract_multiple':
                await this.executeDataExtractMultipleEvent(event);
                break;

            case 'text_search_click':
                await this.executeTextSearchClickEvent(event);
                break;
                
            default:
                throw new Error(`Unknown event type: ${event.type}`);
        }

        // Notify about interaction
        this.notifyInteraction(event.type, event.selector || event.url || 'N/A', true);
    }

    async executeClickEvent(event) {
        let element = await this.findElementWithMultipleMethods(event.selector, event.selectorType, event.selector_alternatives);

        if (!element) {
            throw new Error(`Click target not found with all methods: ${event.selector}`);
        }

        // Scroll to element if needed
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Use advanced click simulation
        if (window.venusAutoFill && window.venusAutoFill.simulateHumanClick) {
            await window.venusAutoFill.simulateHumanClick(element);
        } else {
            // Fallback to simple click with multiple event types
            element.focus();
            element.click();

            // Dispatch additional events for better compatibility
            element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
            element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
            element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
        }

        console.log(`✅ Clicked element: ${event.selector}`);

        // Wait after click if specified
        if (event.wait_after_click) {
            console.log(`⏳ Waiting ${event.wait_after_click}ms after click`);
            await this.delay(event.wait_after_click);
        }
    }

    async findElementWithMultipleMethods(primarySelector, selectorType = 'css', alternatives = []) {
        console.log(`🔍 Searching for element with multiple methods: ${primarySelector}`);
        
        // Method 1: Primary selector
        let element = await this.findElement(primarySelector, selectorType, 2000);
        if (element) {
            console.log(`✅ Found with primary selector: ${primarySelector}`);
            return element;
        }

        // Method 2: Alternative selectors
        if (alternatives && alternatives.length > 0) {
            for (const altSelector of alternatives) {
                console.log(`🔍 Trying alternative selector: ${altSelector}`);
                element = await this.findElement(altSelector, 'css', 1000);
                if (element) {
                    console.log(`✅ Found with alternative selector: ${altSelector}`);
                    return element;
                }
            }
        }

        // Method 3: Text-based search for login buttons
        if (primarySelector.includes('btn') || primarySelector.includes('Login')) {
            console.log(`🔍 Trying text-based search for login button...`);
            element = await this.findElementByText(['LOG IN', 'Login', 'Sign In', 'MASUK', 'log in']);
            if (element) {
                console.log(`✅ Found with text search`);
                return element;
            }
        }

        // Method 4: Advanced search for common login button patterns
        const loginButtonSelectors = [
            // Standard patterns
            'input[type="submit"]',
            'button[type="submit"]',
            'input[value*="LOG"]',
            'input[value*="Login"]',
            'input[value*="MASUK"]',
            'button[value*="LOG"]',
            'button[value*="Login"]',
            
            // ID and name patterns
            '#btnLogin', '#loginBtn', '#login', '#submit',
            'input[name*="login"]', 'input[name*="Login"]',
            'input[name*="btn"]', 'button[name*="login"]',
            
            // Class patterns  
            '.login-btn', '.btn-login', '.submit-btn',
            '.button[class*="login"]', '.btn[class*="submit"]',
            
            // Generic button patterns
            'button:not([type="button"]):not([type="reset"])',
            'input[type="submit"]:not([style*="display: none"])',
            
            // Specific value patterns
            'input[value="LOG IN"]', 'input[value="Login"]',
            'input[value="MASUK"]', 'input[value="Submit"]',
            'button[value="LOG IN"]', 'button[value="Login"]'
        ];

        console.log(`🔍 Trying comprehensive login button search...`);
        for (const selector of loginButtonSelectors) {
            element = await this.findElement(selector, 'css', 500);
            if (element && this.isElementVisible(element)) {
                console.log(`✅ Found with comprehensive search: ${selector}`);
                return element;
            }
        }

        // Method 5: Search by form submission context
        console.log(`🔍 Trying form context search...`);
        const forms = document.querySelectorAll('form');
        for (const form of forms) {
            const submitButtons = form.querySelectorAll('input[type="submit"], button[type="submit"], button:not([type])');
            for (const btn of submitButtons) {
                if (this.isElementVisible(btn)) {
                    console.log(`✅ Found submit button in form context`);
                    return btn;
                }
            }
        }

        console.log(`❌ Element not found with any method`);
        return null;
    }

    async findElementByText(textArray, timeout = 3000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                // Search for buttons/inputs with matching text or value
                const allElements = document.querySelectorAll('button, input[type="submit"], input[type="button"], a');
                
                for (const element of allElements) {
                    if (!this.isElementVisible(element)) continue;
                    
                    const text = element.textContent || element.value || element.innerText || '';
                    const trimmedText = text.trim().toUpperCase();
                    
                    for (const searchText of textArray) {
                        if (trimmedText.includes(searchText.toUpperCase())) {
                            console.log(`Found element by text: "${searchText}" -> "${text}"`);
                            return element;
                        }
                    }
                }
                
                await this.delay(200);
            } catch (error) {
                console.warn('Error in text-based search:', error);
                await this.delay(200);
            }
        }
        
        return null;
    }

    // Advanced text search method - like Ctrl+F
    async findElementByTextAdvanced(searchText, options = {}) {
        const {
            timeout = 3000,
            caseSensitive = false,
            exactMatch = false,
            elementTypes = ['*'], // Can specify specific elements
            excludeHidden = true
        } = options;

        console.log(`🔍 Advanced text search for: "${searchText}"`);
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                // Get all elements based on type specification
                const selector = elementTypes.includes('*') ? '*' : elementTypes.join(', ');
                const allElements = document.querySelectorAll(selector);
                
                for (const element of allElements) {
                    // Skip hidden elements if specified
                    if (excludeHidden && !this.isElementVisible(element)) continue;
                    
                    // Get all possible text content
                    const texts = [
                        element.textContent,
                        element.innerText,
                        element.value,
                        element.title,
                        element.getAttribute('alt'),
                        element.getAttribute('placeholder')
                    ].filter(Boolean);
                    
                    for (const text of texts) {
                        let processedText = text.trim();
                        let processedSearch = searchText.trim();
                        
                        if (!caseSensitive) {
                            processedText = processedText.toUpperCase();
                            processedSearch = processedSearch.toUpperCase();
                        }
                        
                        const found = exactMatch ? 
                            processedText === processedSearch : 
                            processedText.includes(processedSearch);
                            
                        if (found) {
                            console.log(`✅ Advanced search found: "${searchText}" in ${element.tagName} with text "${text}"`);
                            return element;
                        }
                    }
                }
                
                await this.delay(100);
            } catch (error) {
                console.warn('Error in advanced text search:', error);
                await this.delay(100);
            }
        }
        
        console.log(`❌ Advanced search timeout: "${searchText}"`);
        return null;
    }

    // Quick text search - optimized for speed
    async quickTextSearch(searchText, timeout = 1000) {
        console.log(`⚡ Quick text search for: "${searchText}"`);
        const startTime = Date.now();
        
        // Quick search in common interactive elements
        const commonSelectors = [
            'button',
            'input[type="submit"]',
            'input[type="button"]',
            'a',
            '[onclick]',
            '.button',
            '.btn'
        ];
        
        while (Date.now() - startTime < timeout) {
            for (const selector of commonSelectors) {
                const elements = document.querySelectorAll(selector);
                
                for (const element of elements) {
                    if (!this.isElementVisible(element)) continue;
                    
                    const text = (element.textContent || element.value || '').trim().toUpperCase();
                    if (text.includes(searchText.toUpperCase())) {
                        console.log(`⚡ Quick search found: "${searchText}" in ${element.tagName}`);
                        return element;
                    }
                }
            }
            await this.delay(50);
        }
        
        return null;
    }

    async executeInputEvent(event) {
        let element = await this.findElement(event.selector, event.selectorType);

        if (!element) {
            // Try alternative selectors based on the field type
            const isPasswordField = event.value === 'adm075' && event.selector.includes('password');
            const isUsernameField = event.value === 'adm075' && !event.selector.includes('password');

            let alternativeSelectors = [];

            if (isUsernameField) {
                alternativeSelectors = [
                    'input[type="text"]',
                    'input[name*="user"]',
                    'input[id*="user"]',
                    'input[placeholder*="user"]',
                    'input[name*="login"]',
                    'input[id*="login"]',
                    'input:not([type="password"]):not([type="hidden"]):not([type="submit"])'
                ];
            } else if (isPasswordField) {
                alternativeSelectors = [
                    'input[type="password"]',
                    'input[name*="pass"]',
                    'input[id*="pass"]',
                    'input[name*="pwd"]',
                    'input[id*="pwd"]'
                ];
            }

            for (const altSelector of alternativeSelectors) {
                element = await this.findElement(altSelector, 'css');
                if (element) {
                    console.log(`Found alternative element with selector: ${altSelector}`);
                    break;
                }
            }

            if (!element) {
                throw new Error(`Input target not found: ${event.selector}`);
            }
        }

        // Determine the value to input
        let inputValue = event.value;

        if (event.dataMapping && this.automationData) {
            inputValue = this.getDataValue(event.dataMapping) || event.value;
        }

        if (!inputValue) {
            throw new Error('No value specified for input event');
        }

        // Scroll to element and focus
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Focus the element first
        element.focus();
        await this.delay(100);

        // Clear field if requested
        if (event.clearFirst !== false) {
            element.value = '';
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(100);
        }

        // Use advanced typing simulation
        if (window.venusAutoFill && window.venusAutoFill.simulateHumanType) {
            await window.venusAutoFill.simulateHumanType(element, inputValue, {
                clearFirst: event.clearFirst !== false
            });
        } else {
            // Fallback to simple input with better event simulation
            element.value = inputValue;

            // Dispatch multiple events for better compatibility
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
        }

        console.log(`✅ Input value "${inputValue}" into: ${event.selector}`);

        // Wait after input if specified
        if (event.wait_after_input) {
            console.log(`⏳ Waiting ${event.wait_after_input}ms after input`);
            await this.delay(event.wait_after_input);
        }
    }

    async executeWaitEvent(event) {
        const duration = event.duration || 1000;
        
        if (event.waitFor === 'element' && event.condition) {
            // Wait for element to appear
            console.log(`⏳ Waiting for element: ${event.condition}`);
            await this.waitForElement(event.condition, duration);
        } else if (event.waitFor === 'navigation') {
            // Wait for navigation to complete
            console.log(`⏳ Waiting for navigation (max ${duration}ms)`);
            await this.waitForNavigation(duration);
        } else {
            // Simple time delay
            console.log(`⏳ Waiting ${duration}ms`);
            await this.delay(duration);
        }

        console.log(`✅ Wait completed`);
    }

    async executeExtractEvent(event) {
        const element = await this.findElement(event.selector, event.selectorType);
        
        if (!element) {
            throw new Error(`Extract target not found: ${event.selector}`);
        }

        let extractedValue;
        
        switch (event.attribute) {
            case 'text':
                extractedValue = element.textContent?.trim();
                break;
            case 'value':
                extractedValue = element.value;
                break;
            case 'href':
                extractedValue = element.href;
                break;
            case 'src':
                extractedValue = element.src;
                break;
            case 'innerHTML':
                extractedValue = element.innerHTML;
                break;
            default:
                extractedValue = element.getAttribute(event.attribute);
        }

        // Store extracted data
        if (event.variableName) {
            this.extractedData[event.variableName] = extractedValue;
            this.executionResults.extractedData[event.variableName] = extractedValue;
        }

        console.log(`✅ Extracted ${event.attribute} from ${event.selector}: "${extractedValue}"`);
        
        // Notify about data extraction
        this.notifyDataExtraction(1);
    }

    async executeNavigateEvent(event) {
        if (!event.url) {
            throw new Error('Navigate event requires URL');
        }

        console.log(`🌐 Navigating to: ${event.url}`);
        
        window.location.href = event.url;
        
        if (event.waitForLoad !== false) {
            await this.waitForNavigation(event.timeout || 30000);
        }

        console.log(`✅ Navigation completed`);
    }

    async executeScrollEvent(event) {
        const distance = event.distance || 500;
        const direction = event.direction || 'down';
        
        let scrollOptions = { behavior: 'smooth' };
        
        if (event.target) {
            // Scroll to specific element
            const targetElement = await this.findElement(event.target);
            if (targetElement) {
                targetElement.scrollIntoView(scrollOptions);
            }
        } else {
            // Scroll page
            let scrollX = 0, scrollY = 0;
            
            switch (direction) {
                case 'down':
                    scrollY = distance;
                    break;
                case 'up':
                    scrollY = -distance;
                    break;
                case 'right':
                    scrollX = distance;
                    break;
                case 'left':
                    scrollX = -distance;
                    break;
            }
            
            window.scrollBy({ left: scrollX, top: scrollY, ...scrollOptions });
        }

        await this.delay(1000); // Wait for scroll to complete
        console.log(`✅ Scrolled ${direction} ${distance}px`);
    }

    async executeConditionEvent(event, currentIndex) {
        // Basic condition evaluation
        const conditionResult = await this.evaluateCondition(event.condition);
        
        console.log(`🔍 Condition "${event.condition}" evaluated to: ${conditionResult}`);
        
        // This is a simplified implementation
        // In a full implementation, you'd handle jumping to different events
        if (conditionResult && event.trueAction) {
            console.log(`✅ Condition true, would execute: ${event.trueAction}`);
        } else if (!conditionResult && event.falseAction) {
            console.log(`❌ Condition false, would execute: ${event.falseAction}`);
        }
    }

    async findElement(selector, selectorType = 'css', timeout = 3000) {
        if (!selector) {
            throw new Error('Selector is required');
        }

        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            let element;
            
            try {
                switch (selectorType) {
                    case 'xpath':
                        const result = document.evaluate(
                            selector,
                            document,
                            null,
                            XPathResult.FIRST_ORDERED_NODE_TYPE,
                            null
                        );
                        element = result.singleNodeValue;
                        break;
                        
                    case 'text':
                        if (window.venusAutoFill && window.venusAutoFill.findElementByText) {
                            element = window.venusAutoFill.findElementByText(selector);
                        } else {
                            // Fallback text search
                            element = Array.from(document.querySelectorAll('*'))
                                .find(el => el.textContent?.includes(selector));
                        }
                        break;
                        
                    default: // css
                        element = document.querySelector(selector);
                }
                
                if (element && this.isElementVisible(element)) {
                    return element;
                }
            } catch (error) {
                console.warn(`Error finding element with ${selectorType} selector "${selector}":`, error);
            }
            
            await this.delay(50);
        }
        
        return null;
    }

    isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return rect.width > 0 && 
               rect.height > 0 && 
               style.visibility !== 'hidden' && 
               style.display !== 'none' &&
               style.opacity !== '0';
    }

    scrollElementIntoView(element) {
        if (element && typeof element.scrollIntoView === 'function') {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        }
    }

    getDataValue(path) {
        // Extract value from automation data using dot notation
        // e.g., "user.email" -> automationData.user.email
        if (!this.automationData || !Array.isArray(this.automationData) || this.automationData.length === 0) {
            return null;
        }

        const pathParts = path.split('.');
        let value = this.automationData[0]; // Use first record for simplicity
        
        for (const part of pathParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            } else {
                return null;
            }
        }
        
        return value;
    }

    async waitForElement(selector, timeout = 10000) {
        const element = await this.findElement(selector, 'css', timeout);
        if (!element) {
            throw new Error(`Element not found within ${timeout}ms: ${selector}`);
        }
        return element;
    }

    async waitForNavigation(timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkReady = () => {
                if (document.readyState === 'complete' || Date.now() - startTime > timeout) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            
            checkReady();
        });
    }

    async evaluateCondition(condition) {
        // Simple condition evaluation
        // In a full implementation, this would be much more sophisticated
        try {
            if (condition.includes('element.exists(')) {
                const selectorMatch = condition.match(/element\.exists\(['"`]([^'"`]+)['"`]\)/);
                if (selectorMatch) {
                    const selector = selectorMatch[1];
                    const element = document.querySelector(selector);
                    return !!element;
                }
            }
            
            // Add more condition types as needed
            return false;
        } catch (error) {
            console.error('Error evaluating condition:', error);
            return false;
        }
    }

    isCriticalError(error) {
        const criticalPatterns = [
            'Navigation failed',
            'Page not loaded',
            'Authentication required'
        ];
        
        return criticalPatterns.some(pattern => error.message.includes(pattern));
    }

    pauseAutomation() {
        this.isPaused = true;
        console.log('⏸️ Automation paused');
    }

    stopAutomation() {
        this.isExecuting = false;
        this.isPaused = false;
        console.log('⏹️ Automation stopped');
    }

    async testSingleEvent(event, index, sendResponse) {
        try {
            console.log(`🧪 Testing event: ${event.type}`);
            
            // Create a test environment
            const originalData = this.automationData;
            const originalExecuting = this.isExecuting;
            
            this.isExecuting = true;
            
            await this.executeEvent(event, index);
            
            // Restore state
            this.automationData = originalData;
            this.isExecuting = originalExecuting;
            
            sendResponse({ success: true, message: 'Event test passed' });
        } catch (error) {
            console.error('Event test failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async extractPageData(selectors, sendResponse) {
        const results = {};
        
        for (const [key, selector] of Object.entries(selectors)) {
            try {
                const element = document.querySelector(selector);
                if (element) {
                    results[key] = element.textContent?.trim() || element.value || '';
                }
            } catch (error) {
                console.error(`Failed to extract data for ${key}:`, error);
                results[key] = null;
            }
        }
        
        sendResponse({ success: true, data: results });
    }

    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
            timestamp: new Date().toISOString()
        };
    }

    generateExecutionId() {
        return 'exec_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    notifyProgress(progress, step) {
        chrome.runtime.sendMessage({
            action: 'executionProgress',
            progress: progress,
            step: step
        });
    }

    notifyCompletion(success, error = null, results = null) {
        chrome.runtime.sendMessage({
            action: 'executionComplete',
            success: success,
            error: error,
            results: results
        });
    }

    notifyInteraction(type, selector, success) {
        chrome.runtime.sendMessage({
            action: 'elementInteraction',
            type: type,
            selector: selector,
            success: success,
            status: success ? 'completed' : 'failed'
        });
    }

    notifyDataExtraction(dataPoints) {
        chrome.runtime.sendMessage({
            action: 'dataExtracted',
            dataPoints: dataPoints
        });
    }

    injectAdvancedHelpers() {
        // Inject the advanced automation helpers if not already present
        if (!window.venusAutoFillInjected) {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('injected.js');
            script.onload = () => {
                console.log('✅ Advanced automation helpers injected');
            };
            (document.head || document.documentElement).appendChild(script);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ================== NEW EVENT TYPE IMPLEMENTATIONS ==================

    async executeOpenToEvent(event) {
        if (!event.url) {
            throw new Error('open_to event requires URL');
        }

        console.log(`🌐 Opening URL: ${event.url}`);
        
        if (event.newTab) {
            // Open in new tab
            window.open(event.url, '_blank');
        } else {
            // Navigate current tab
            window.location.href = event.url;
            
            if (event.waitForLoad !== false) {
                await this.waitForNavigation(event.timeout || 30000);
            }
        }

        console.log(`✅ Successfully opened: ${event.url}`);
    }

    async executeWaitForElementEvent(event) {
        if (!event.selector) {
            throw new Error('wait_for_element event requires selector');
        }

        const timeout = event.timeout || 10000;
        const expectVisible = event.expectVisible !== false; // Default to true
        
        console.log(`⏳ Waiting for element ${expectVisible ? 'to appear' : 'to disappear'}: ${event.selector}`);
        
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(event.selector);
            const isVisible = element && this.isElementVisible(element);
            
            if (expectVisible && isVisible) {
                console.log(`✅ Element appeared: ${event.selector}`);
                return element;
            } else if (!expectVisible && !isVisible) {
                console.log(`✅ Element disappeared: ${event.selector}`);
                return;
            }
            
            await this.delay(100);
        }
        
        throw new Error(`Element ${expectVisible ? 'did not appear' : 'did not disappear'} within ${timeout}ms: ${event.selector}`);
    }

    async executeHoverEvent(event) {
        const element = await this.findElement(event.selector, event.selectorType);
        
        if (!element) {
            throw new Error(`Hover target not found: ${event.selector}`);
        }

        // Scroll to element if needed
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Create and dispatch mouse events for hover
        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top + rect.height / 2;

        const mouseEvents = ['mouseover', 'mouseenter', 'mousemove'];
        
        for (const eventType of mouseEvents) {
            element.dispatchEvent(new MouseEvent(eventType, {
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y
            }));
            await this.delay(50);
        }

        if (event.duration) {
            await this.delay(event.duration);
        }

        console.log(`✅ Hovered over element: ${event.selector}`);
    }

    async executeScrollToEvent(event) {
        if (event.selector) {
            // Scroll to specific element
            const element = await this.findElement(event.selector, event.selectorType);
            if (!element) {
                throw new Error(`Scroll target not found: ${event.selector}`);
            }
            
            element.scrollIntoView({
                behavior: event.smooth !== false ? 'smooth' : 'auto',
                block: event.block || 'center',
                inline: event.inline || 'center'
            });
        } else if (event.position) {
            // Scroll to specific position
            const { x = 0, y = 0 } = event.position;
            window.scrollTo({
                left: x,
                top: y,
                behavior: event.smooth !== false ? 'smooth' : 'auto'
            });
        } else {
            throw new Error('scroll_to event requires either selector or position');
        }

        await this.delay(event.waitAfter || 1000);
        console.log(`✅ Scrolled to target`);
    }

    async executeSelectOptionEvent(event) {
        const selectElement = await this.findElement(event.selector, event.selectorType);
        
        if (!selectElement || selectElement.tagName !== 'SELECT') {
            throw new Error(`Select element not found: ${event.selector}`);
        }

        // Scroll to element
        this.scrollElementIntoView(selectElement);
        await this.delay(100);

        // Find option by value, text, or index
        let optionElement = null;
        
        if (event.value !== undefined) {
            optionElement = selectElement.querySelector(`option[value="${event.value}"]`);
        } else if (event.text) {
            optionElement = Array.from(selectElement.options)
                .find(option => option.textContent.trim() === event.text);
        } else if (event.index !== undefined) {
            optionElement = selectElement.options[event.index];
        }

        if (!optionElement) {
            throw new Error(`Option not found in select ${event.selector}`);
        }

        // Select the option
        optionElement.selected = true;
        selectElement.value = optionElement.value;

        // Dispatch events
        selectElement.dispatchEvent(new Event('change', { bubbles: true }));
        selectElement.dispatchEvent(new Event('input', { bubbles: true }));

        console.log(`✅ Selected option: ${optionElement.textContent} (${optionElement.value})`);
    }

    async executeAlertHandleEvent(event) {
        const action = event.action || 'accept'; // accept, dismiss, text
        const timeout = event.timeout || 5000;
        
        console.log(`🚨 Waiting for alert to handle with action: ${action}`);
        
        // Set up alert handler
        const originalAlert = window.alert;
        const originalConfirm = window.confirm;
        const originalPrompt = window.prompt;
        
        let alertHandled = false;
        let alertResult = null;
        
        const handleAlert = (originalFn, defaultResult) => {
            return function(...args) {
                alertHandled = true;
                
                if (action === 'accept') {
                    alertResult = defaultResult;
                    return defaultResult;
                } else if (action === 'dismiss') {
                    alertResult = false;
                    return false;
                } else if (action === 'text' && event.text) {
                    alertResult = event.text;
                    return event.text;
                }
                
                return originalFn.apply(this, args);
            };
        };
        
        window.alert = handleAlert(originalAlert, true);
        window.confirm = handleAlert(originalConfirm, action === 'accept');
        window.prompt = handleAlert(originalPrompt, event.text || '');
        
        // Wait for alert or timeout
        const startTime = Date.now();
        while (!alertHandled && Date.now() - startTime < timeout) {
            await this.delay(100);
        }
        
        // Restore original functions
        window.alert = originalAlert;
        window.confirm = originalConfirm;
        window.prompt = originalPrompt;
        
        if (!alertHandled) {
            throw new Error(`No alert appeared within ${timeout}ms`);
        }
        
        console.log(`✅ Alert handled with result: ${alertResult}`);
    }

    async executeScreenshotEvent(event) {
        console.log(`📸 Taking screenshot: ${event.name || 'unnamed'}`);
        
        // Store screenshot metadata
        const screenshotData = {
            name: event.name || `screenshot_${Date.now()}`,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            description: event.description
        };
        
        // Store in extracted data for reporting
        if (!this.extractedData.screenshots) {
            this.extractedData.screenshots = [];
        }
        this.extractedData.screenshots.push(screenshotData);
        
        console.log(`✅ Screenshot metadata recorded: ${screenshotData.name}`);
    }

    async executeFormFillEvent(event) {
        if (!event.fields || !Array.isArray(event.fields)) {
            throw new Error('form_fill event requires fields array');
        }

        console.log(`📝 Filling form with ${event.fields.length} fields`);
        
        for (const field of event.fields) {
            try {
                const element = await this.findElement(field.selector, field.selectorType || 'css');
                
                if (!element) {
                    if (field.required !== false) {
                        throw new Error(`Required form field not found: ${field.selector}`);
                    }
                    console.warn(`⚠️ Optional field not found: ${field.selector}`);
                    continue;
                }

                // Determine value
                let value = field.value;
                if (field.dataMapping && this.automationData) {
                    value = this.getDataValue(field.dataMapping) || field.value;
                }

                // Fill based on element type
                if (element.tagName === 'SELECT') {
                    await this.executeSelectOptionEvent({
                        selector: field.selector,
                        value: value,
                        text: field.text
                    });
                } else if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = Boolean(value);
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    // Regular input
                    await this.executeInputEvent({
                        selector: field.selector,
                        selectorType: field.selectorType,
                        value: value,
                        clearFirst: field.clearFirst
                    });
                }

                await this.delay(event.fieldDelay || 100);
            } catch (error) {
                console.error(`Error filling field ${field.selector}:`, error);
                if (field.required !== false) {
                    throw error;
                }
            }
        }

        console.log(`✅ Form filled successfully`);
    }

    async executeTabSwitchEvent(event) {
        if (event.tabIndex !== undefined) {
            // Switch by index (requires background script communication)
            console.log(`🔄 Switching to tab index: ${event.tabIndex}`);
            
            // Send message to background to switch tabs
            chrome.runtime.sendMessage({
                action: 'switchTab',
                tabIndex: event.tabIndex
            });
        } else if (event.url) {
            // Open URL in new tab
            window.open(event.url, '_blank');
            console.log(`🔄 Opened new tab with URL: ${event.url}`);
        } else {
            throw new Error('tab_switch event requires either tabIndex or url');
        }
    }

    async executeLoopEvent(event, currentIndex) {
        if (!event.iterations || event.iterations < 1) {
            throw new Error('loop event requires iterations > 0');
        }

        if (!event.events || !Array.isArray(event.events)) {
            throw new Error('loop event requires events array');
        }

        console.log(`🔄 Starting loop: ${event.iterations} iterations`);
        
        for (let i = 0; i < event.iterations; i++) {
            console.log(`🔄 Loop iteration ${i + 1}/${event.iterations}`);
            
            // Set loop variables
            this.extractedData.loopIndex = i;
            this.extractedData.loopIteration = i + 1;
            
            for (const loopEvent of event.events) {
                if (!this.isExecuting) {
                    throw new Error('Automation stopped during loop');
                }
                
                try {
                    await this.executeEvent(loopEvent, `${currentIndex}.${i}`);
                    await this.delay(event.eventDelay || 100);
                } catch (error) {
                    if (event.continueOnError) {
                        console.warn(`⚠️ Loop event failed but continuing: ${error.message}`);
                    } else {
                        throw error;
                    }
                }
            }
            
            if (i < event.iterations - 1) {
                await this.delay(event.iterationDelay || 500);
            }
        }

        console.log(`✅ Loop completed: ${event.iterations} iterations`);
    }

    async executeIfThenElseEvent(event, currentIndex) {
        if (!event.condition) {
            throw new Error('if_then_else event requires condition');
        }

        console.log(`🔍 Evaluating if-then-else condition: ${JSON.stringify(event.condition)}`);
        
        const conditionResult = await this.evaluateEventCondition(event.condition);
        
        let eventsToExecute = [];
        
        if (conditionResult && event.thenEvents) {
            console.log(`✅ Condition true, executing 'then' events`);
            eventsToExecute = event.thenEvents;
        } else if (!conditionResult && event.elseEvents) {
            console.log(`❌ Condition false, executing 'else' events`);
            eventsToExecute = event.elseEvents;
        } else {
            console.log(`⏭️ No matching events to execute`);
            return;
        }

        for (let i = 0; i < eventsToExecute.length; i++) {
            if (!this.isExecuting) {
                throw new Error('Automation stopped during conditional execution');
            }
            
            await this.executeEvent(eventsToExecute[i], `${currentIndex}.${conditionResult ? 'then' : 'else'}.${i}`);
            await this.delay(event.eventDelay || 100);
        }

        console.log(`✅ Conditional execution completed`);
    }

    async executeConditionalActionEvent(event, currentIndex) {
        if (!event.condition) {
            throw new Error('conditional_action event requires condition');
        }

        console.log(`🔍 Evaluating conditional_action condition: ${JSON.stringify(event.condition)}`);
        
        const conditionResult = await this.evaluateEventCondition(event.condition);
        
        if (conditionResult && event.true_action) {
            console.log(`✅ Condition true, executing true_action`);
            await this.executeSingleAction(event.true_action, `${currentIndex}.true`);
        } else if (conditionResult && event.thenEvents) {
            console.log(`✅ Condition true, executing 'then' events`);
            await this.executeEvents(event.thenEvents, `${currentIndex}.then`);
        } else if (!conditionResult && event.false_action) {
            console.log(`❌ Condition false, executing false_action`);
            await this.executeSingleAction(event.false_action, `${currentIndex}.false`);
        } else if (!conditionResult && event.elseEvents) {
            console.log(`❌ Condition false, executing 'else' events`);
            await this.executeEvents(event.elseEvents, `${currentIndex}.else`);
        } else {
            console.log(`⏭️ No matching events to execute`);
        }

        console.log(`✅ Conditional action completed`);
    }

    async executeSingleAction(action, actionIndex) {
        if (!action || !action.type) {
            console.log('⚠️ Invalid action provided');
            return;
        }

        console.log(`🎯 Executing single action: ${action.type}`);
        
        // Convert action to event format and execute
        const actionEvent = { ...action };
        await this.executeEvent(actionEvent, actionIndex);
        
        // Wait after action if specified
        if (action.wait_after_click || action.wait_after_action) {
            const waitTime = action.wait_after_click || action.wait_after_action;
            console.log(`⏳ Waiting ${waitTime}ms after action`);
            await this.delay(waitTime);
        }
    }

    async executeEvents(events, baseIndex) {
        if (!events || !Array.isArray(events)) {
            return;
        }

        for (let i = 0; i < events.length; i++) {
            if (!this.isExecuting) {
                throw new Error('Automation stopped during event execution');
            }
            
            await this.executeEvent(events[i], `${baseIndex}.${i}`);
            await this.delay(100); // Small delay between events
        }
    }

    async executeVariableSetEvent(event) {
        if (!event.variableName) {
            throw new Error('variable_set event requires variableName');
        }

        let value = event.value;
        
        // Support dynamic value setting
        if (event.dataMapping) {
            value = this.getDataValue(event.dataMapping) || value;
        } else if (event.selector) {
            // Extract value from element
            const element = await this.findElement(event.selector, event.selectorType);
            if (element) {
                value = element.textContent || element.value || element.getAttribute(event.attribute || 'value');
            }
        } else if (event.expression) {
            // Evaluate JavaScript expression safely
            try {
                value = this.evaluateExpression(event.expression);
            } catch (error) {
                console.warn(`⚠️ Expression evaluation failed: ${error.message}`);
            }
        }

        // Store variable
        this.extractedData[event.variableName] = value;
        this.executionResults.extractedData[event.variableName] = value;

        console.log(`✅ Variable set: ${event.variableName} = ${value}`);
    }

    async executeDataExtractMultipleEvent(event) {
        if (!event.extractions || !Array.isArray(event.extractions)) {
            throw new Error('data_extract_multiple event requires extractions array');
        }

        console.log(`📊 Extracting ${event.extractions.length} data points`);
        
        const extractedData = {};
        
        for (const extraction of event.extractions) {
            try {
                const element = await this.findElement(extraction.selector, extraction.selectorType);
                
                if (!element) {
                    if (extraction.required !== false) {
                        throw new Error(`Required extraction element not found: ${extraction.selector}`);
                    }
                    console.warn(`⚠️ Optional extraction element not found: ${extraction.selector}`);
                    continue;
                }

                let value;
                switch (extraction.attribute || 'text') {
                    case 'text':
                        value = element.textContent?.trim();
                        break;
                    case 'value':
                        value = element.value;
                        break;
                    case 'html':
                        value = element.innerHTML;
                        break;
                    default:
                        value = element.getAttribute(extraction.attribute);
                }

                // Apply transformation if specified
                if (extraction.transform) {
                    value = this.transformValue(value, extraction.transform);
                }

                extractedData[extraction.name] = value;
                
                if (extraction.storeGlobally) {
                    this.extractedData[extraction.name] = value;
                    this.executionResults.extractedData[extraction.name] = value;
                }

            } catch (error) {
                console.error(`Error extracting ${extraction.name}:`, error);
                if (extraction.required !== false) {
                    throw error;
                }
            }
        }

        console.log(`✅ Data extraction completed:`, extractedData);
        return extractedData;
    }

    async executeTextSearchClickEvent(event) {
        if (!event.searchText && !event.searchTexts) {
            throw new Error('text_search_click event requires searchText or searchTexts');
        }

        const searchTexts = event.searchTexts || [event.searchText];
        const timeout = event.timeout || 1000;
        
        console.log(`🔍 Text search click for: ${searchTexts.join(', ')}`);
        
        let element = null;
        
        // Try quick search first (optimized for speed)
        if (event.quick !== false) {
            for (const text of searchTexts) {
                element = await this.quickTextSearch(text, timeout / 2);
                if (element) break;
            }
        }
        
        // Fallback to advanced search if quick search fails
        if (!element) {
            console.log(`⚡ Quick search failed, trying advanced search...`);
            for (const text of searchTexts) {
                element = await this.findElementByTextAdvanced(text, {
                    timeout: timeout / 2,
                    caseSensitive: event.caseSensitive || false,
                    exactMatch: event.exactMatch || false,
                    elementTypes: event.elementTypes || ['button', 'input', 'a']
                });
                if (element) break;
            }
        }
        
        if (!element) {
            throw new Error(`Text search click failed: No element found with text "${searchTexts.join(', ')}"`);
        }

        // Scroll to element if needed
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Click the element
        element.focus();
        element.click();

        // Dispatch additional events for better compatibility
        element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('click', { bubbles: true }));

        console.log(`✅ Text search click completed: "${searchTexts[0]}"`);

        // Wait after click if specified
        if (event.wait_after_click) {
            console.log(`⏳ Waiting ${event.wait_after_click}ms after text search click`);
            await this.delay(event.wait_after_click);
        }
    }

    // ================== ENHANCED CONDITION EVALUATION ==================

    async evaluateEventCondition(condition) {
        try {
            switch (condition.type) {
                case 'element_exists':
                    return await this.waitForElementCondition(
                        condition.selector, 
                        condition.timeout || 5000, 
                        condition.visible !== false
                    );

                case 'element_text_contains':
                    const textElement = await this.waitForElementCondition(
                        condition.selector, 
                        condition.timeout || 3000, 
                        true
                    );
                    if (!textElement) return false;
                    const text = textElement.textContent || '';
                    return text.includes(condition.value);

                case 'url_contains':
                    return window.location.href.includes(condition.value);

                case 'variable_equals':
                    const varValue = this.extractedData[condition.variableName];
                    return varValue === condition.value;

                case 'page_title_contains':
                    return document.title.includes(condition.value);

                case 'element_count':
                    const elements = document.querySelectorAll(condition.selector);
                    const count = elements.length;
                    
                    switch (condition.operator) {
                        case 'equals': return count === condition.value;
                        case 'greater_than': return count > condition.value;
                        case 'less_than': return count < condition.value;
                        default: return count > 0;
                    }

                case 'custom_script':
                    // Execute custom JavaScript for complex conditions
                    return this.evaluateExpression(condition.script);

                default:
                    console.warn(`Unknown condition type: ${condition.type}`);
                    return false;
            }
        } catch (error) {
            console.error('Error evaluating condition:', error);
            return false;
        }
    }

    async waitForElementCondition(selector, timeout = 5000, mustBeVisible = true) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                const element = document.querySelector(selector);
                
                if (element) {
                    if (!mustBeVisible || this.isElementVisible(element)) {
                        console.log(`✅ Element condition met: ${selector}`);
                        return element;
                    }
                }
                
                // Wait a bit before checking again
                await this.delay(100);
            } catch (error) {
                console.warn(`Error checking element condition: ${error.message}`);
                await this.delay(100);
            }
        }
        
        console.log(`⏰ Element condition timeout: ${selector} (${timeout}ms)`);
        return null;
    }

    // ================== UTILITY METHODS ==================

    transformValue(value, transform) {
        switch (transform.type) {
            case 'trim':
                return value?.toString().trim();
            case 'uppercase':
                return value?.toString().toUpperCase();
            case 'lowercase':
                return value?.toString().toLowerCase();
            case 'number':
                return parseFloat(value) || 0;
            case 'regex_extract':
                const match = value?.toString().match(new RegExp(transform.pattern));
                return match ? match[transform.group || 0] : null;
            default:
                return value;
        }
    }

    evaluateExpression(expression) {
        // Safe expression evaluation with limited scope
        const context = {
            data: this.automationData,
            extracted: this.extractedData,
            url: window.location.href,
            title: document.title,
            Math: Math,
            Date: Date
        };
        
        try {
            const func = new Function(...Object.keys(context), `return ${expression}`);
            return func(...Object.values(context));
        } catch (error) {
            console.error('Expression evaluation error:', error);
            throw error;
        }
    }
}

// Initialize the Venus-Millware AutoFill content script
const automationBotContent = new AutomationBotContent();