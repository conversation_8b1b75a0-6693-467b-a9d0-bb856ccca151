{"flow_id": "millware_enhanced_automation_v2", "name": "Millware Enhanced Automation Flow", "description": "Enhanced automation flow dengan berbagai jenis event dan conditional logic", "version": "2.0.0", "author": "Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)", "created_date": "2024-01-20", "target_system": {"name": "Millware ERP System", "base_url": "http://millwarep3.rebinmas.com:8003/", "authentication_required": true}, "flow_metadata": {"estimated_duration": "30-60 seconds", "complexity": "advanced", "success_rate": "high", "dependencies": ["active_internet", "target_server_accessible"]}, "supported_events": {"navigation": ["open_url", "navigate_to", "go_back", "go_forward", "refresh_page"], "interaction": ["click", "double_click", "right_click", "hover", "drag_drop"], "input": ["type_text", "clear_field", "select_dropdown", "check_checkbox", "uncheck_checkbox", "select_radio", "upload_file"], "verification": ["verify_text", "verify_element_exists", "verify_element_visible", "verify_url_contains", "verify_page_title"], "utility": ["wait_seconds", "wait_for_element", "scroll_to_element", "scroll_page", "capture_screenshot", "execute_script", "set_variable", "get_text"], "conditional": ["if_element_exists", "if_text_contains", "if_url_contains", "if_variable_equals", "switch_case"]}, "flow_steps": [{"step_id": 1, "name": "open_millware_homepage", "type": "open_url", "description": "Membuka halaman utama Millware system", "parameters": {"url": "http://millwarep3.rebinmas.com:8003/", "new_tab": false, "wait_for_load": true, "timeout": 30000}, "visual_node": {"title": "🌐 Buka Homepage Millware", "subtitle": "Navi<PERSON>i ke halaman utama", "icon": "🌐", "color": "#3b82f6"}, "visual_feedback": {"show_page_highlight": true, "highlight_duration": 2000, "highlight_color": "#3b82f6"}, "conditional": {"enabled": true, "condition": {"type": "verify_url_contains", "value": "millwarep3.rebinmas.com", "negate": true}, "on_true": "continue", "on_false": "skip_step"}, "error_handling": {"continue_on_error": false, "retry_attempts": 3, "retry_delay": 5000}}, {"step_id": 2, "name": "wait_for_page_ready", "type": "wait_seconds", "description": "<PERSON>ng<PERSON> beberapa detik untuk memastikan halaman ter-load", "parameters": {"duration": 3, "show_countdown": true}, "visual_node": {"title": "⏳ <PERSON>nggu <PERSON>", "subtitle": "Menunggu 3 detik", "icon": "⏳", "color": "#f59e0b"}, "conditional": {"enabled": false}, "error_handling": {"continue_on_error": true}}, {"step_id": 3, "name": "verify_login_form", "type": "verify_element_exists", "description": "Verifikasi form login tersedia", "parameters": {"selector": "#txtUsername", "selector_alternatives": ["input[name='txtUsername']", "input[placeholder*='User']"], "timeout": 10000, "must_be_visible": true}, "visual_node": {"title": "🔍 Verifikasi Form Login", "subtitle": "Pastikan form tersedia", "icon": "🔍", "color": "#8b5cf6"}, "conditional": {"enabled": true, "condition": {"type": "verify_element_exists", "selector": ".already-logged-in", "timeout": 2000}, "on_true": "jump_to_step", "jump_target": 7, "on_false": "continue"}, "error_handling": {"continue_on_error": false, "retry_attempts": 2}}, {"step_id": 4, "name": "clear_username_field", "type": "clear_field", "description": "Bersihkan field username jika ada isi sebelumnya", "parameters": {"selector": "#txtUsername", "force_clear": true}, "visual_node": {"title": "🧹 Bersihkan Field Username", "subtitle": "Reset field username", "icon": "🧹", "color": "#6b7280"}, "conditional": {"enabled": true, "condition": {"type": "verify_element_visible", "selector": "#txtUsername", "timeout": 3000}, "on_true": "continue", "on_false": "skip_step"}, "visual_feedback": {"highlight_element": true, "highlight_color": "#6b7280", "highlight_duration": 1000}}, {"step_id": 5, "name": "input_username", "type": "type_text", "description": "Ketik username ke field login", "parameters": {"selector": "#txtUsername", "selector_alternatives": ["input[name='txtUsername']", "input[type='text']:first"], "text": "{{username}}", "clear_first": false, "simulate_typing": true, "typing_speed": 100, "trigger_events": ["input", "change"]}, "visual_node": {"title": "👤 Ketik Username", "subtitle": "Input kredensial pengguna", "icon": "👤", "color": "#10b981"}, "conditional": {"enabled": false}, "visual_feedback": {"highlight_element": true, "highlight_color": "#10b981", "highlight_duration": 2000, "show_typing_indicator": true}, "error_handling": {"continue_on_error": false, "retry_attempts": 3}}, {"step_id": 6, "name": "input_password", "type": "type_text", "description": "Ke<PERSON>k password ke field login", "parameters": {"selector": "#txtPassword", "selector_alternatives": ["input[name='txtPassword']", "input[type='password']:first"], "text": "{{password}}", "clear_first": true, "simulate_typing": true, "typing_speed": 80, "mask_in_logs": true, "trigger_events": ["input", "change"]}, "visual_node": {"title": "🔐 Ketik Password", "subtitle": "Input kata sandi", "icon": "🔐", "color": "#ef4444"}, "conditional": {"enabled": false}, "visual_feedback": {"highlight_element": true, "highlight_color": "#ef4444", "highlight_duration": 2000, "show_typing_indicator": true}, "error_handling": {"continue_on_error": false, "retry_attempts": 3}}, {"step_id": 7, "name": "hover_login_button", "type": "hover", "description": "Hover ke tombol login untuk memastikan interaktif", "parameters": {"selector": "#btnLogin", "selector_alternatives": ["input[name='btnLogin']", "input[value='LOG IN']"], "duration": 1000}, "visual_node": {"title": "👆 Hover ke Tombol Login", "subtitle": "Persiapan klik login", "icon": "👆", "color": "#f59e0b"}, "conditional": {"enabled": true, "condition": {"type": "verify_element_visible", "selector": "#btnLogin", "timeout": 5000}, "on_true": "continue", "on_false": "skip_step"}, "visual_feedback": {"highlight_element": true, "highlight_color": "#f59e0b", "highlight_duration": 1000}}, {"step_id": 8, "name": "capture_before_login", "type": "capture_screenshot", "description": "Ambil screenshot sebelum login", "parameters": {"filename": "before_login_{{timestamp}}.png", "quality": 90, "full_page": false}, "visual_node": {"title": "📸 Screenshot Sebelum Login", "subtitle": "Dokumentasi step", "icon": "📸", "color": "#6366f1"}, "conditional": {"enabled": true, "condition": {"type": "verify_variable_equals", "variable": "enable_screenshots", "value": true}, "on_true": "continue", "on_false": "skip_step"}}, {"step_id": 9, "name": "click_login_button", "type": "click", "description": "Klik tombol login untuk masuk sistem", "parameters": {"selector": "#btnLogin", "selector_alternatives": ["input[name='btnLogin']", "input[value='LOG IN']"], "click_type": "left", "wait_after_click": 2000, "scroll_to_element": true}, "visual_node": {"title": "🚀 <PERSON><PERSON>", "subtitle": "<PERSON>ses autentikasi", "icon": "🚀", "color": "#ef4444"}, "conditional": {"enabled": false}, "visual_feedback": {"highlight_element": true, "highlight_color": "#ef4444", "click_animation": true, "highlight_duration": 2000}, "error_handling": {"continue_on_error": false, "retry_attempts": 2}}, {"step_id": 10, "name": "wait_for_login_response", "type": "wait_for_element", "description": "<PERSON><PERSON><PERSON> respon login (popup atau redirect)", "parameters": {"selectors": [".PopupBoxLogin", "#MainContent", ".error-message"], "timeout": 10000, "any_selector": true}, "visual_node": {"title": "⏱️ <PERSON><PERSON><PERSON><PERSON> Login", "subtitle": "Menunggu feedback sistem", "icon": "⏱️", "color": "#8b5cf6"}, "conditional": {"enabled": false}}, {"step_id": 11, "name": "handle_login_popup", "type": "if_element_exists", "description": "Handle popup login jika muncul", "parameters": {"condition": {"selector": ".PopupBoxLogin", "timeout": 3000, "visible": true}, "true_actions": [{"type": "get_text", "selector": ".PopupBoxLogin .message", "store_in": "popup_message"}, {"type": "click", "selector": "#MainContent_btnOkay", "selector_alternatives": ["input[name*='btnOkay']", "input[value='ok']"]}, {"type": "wait_seconds", "duration": 2}], "false_actions": [{"type": "set_variable", "variable": "popup_message", "value": "No popup detected"}]}, "visual_node": {"title": "🔔 <PERSON><PERSON>up Login", "subtitle": "<PERSON>ani popup jika ada", "icon": "🔔", "color": "#f59e0b"}, "conditional": {"enabled": false}}, {"step_id": 12, "name": "verify_login_success", "type": "verify_url_contains", "description": "Verifikasi login berhasil dengan cek URL", "parameters": {"expected_url_part": "/en/PR/trx/", "timeout": 5000, "case_sensitive": false}, "visual_node": {"title": "✅ Veri<PERSON><PERSON><PERSON>", "subtitle": "Pastikan masuk sistem", "icon": "✅", "color": "#22c55e"}, "conditional": {"enabled": true, "condition": {"type": "verify_url_contains", "value": "login", "negate": false}, "on_true": "jump_to_step", "jump_target": 3, "on_false": "continue"}, "error_handling": {"continue_on_error": false, "on_error_action": "stop_execution"}}, {"step_id": 13, "name": "scroll_to_top", "type": "scroll_page", "description": "<PERSON>roll ke atas halaman", "parameters": {"direction": "top", "smooth": true, "duration": 1000}, "visual_node": {"title": "⬆️ Scroll ke Atas", "subtitle": "Reset posisi halaman", "icon": "⬆️", "color": "#6b7280"}, "conditional": {"enabled": false}}, {"step_id": 14, "name": "navigate_to_task_register", "type": "open_url", "description": "<PERSON><PERSON> halaman Task Register List", "parameters": {"url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx", "new_tab": false, "wait_for_load": true, "timeout": 30000}, "visual_node": {"title": "📋 Buka Task Register", "subtitle": "Navi<PERSON>i ke halaman target", "icon": "📋", "color": "#8b5cf6"}, "conditional": {"enabled": false}, "visual_feedback": {"show_page_highlight": true, "highlight_duration": 2000}, "error_handling": {"continue_on_error": false, "retry_attempts": 2}}, {"step_id": 15, "name": "wait_for_task_page", "type": "wait_for_element", "description": "Tunggu halaman Task Register ter-load", "parameters": {"selector": "#MainContent_btnNew", "timeout": 15000, "visible": true, "interactable": true}, "visual_node": {"title": "⏳ <PERSON><PERSON><PERSON> Task", "subtitle": "Memastikan halaman siap", "icon": "⏳", "color": "#f59e0b"}, "conditional": {"enabled": false}}, {"step_id": 16, "name": "scroll_to_new_button", "type": "scroll_to_element", "description": "<PERSON>roll ke tombol New jika perlu", "parameters": {"selector": "#MainContent_btnNew", "behavior": "smooth", "block": "center", "inline": "center"}, "visual_node": {"title": "📍 Scroll ke Tombol New", "subtitle": "Pastikan tombol terlihat", "icon": "📍", "color": "#6b7280"}, "conditional": {"enabled": true, "condition": {"type": "verify_element_visible", "selector": "#MainContent_btnNew", "timeout": 3000}, "on_true": "skip_step", "on_false": "continue"}}, {"step_id": 17, "name": "double_click_new_button", "type": "double_click", "description": "Double klik tombol New untuk membuat task baru", "parameters": {"selector": "#MainContent_btnNew", "selector_alternatives": ["input[name*='btnNew']", "input[value='New']"], "delay_between_clicks": 100, "wait_after_click": 3000}, "visual_node": {"title": "➕ Double Klik New", "subtitle": "Buat task register baru", "icon": "➕", "color": "#22c55e"}, "conditional": {"enabled": false}, "visual_feedback": {"highlight_element": true, "highlight_color": "#22c55e", "click_animation": true, "highlight_duration": 3000}, "error_handling": {"continue_on_error": false, "retry_attempts": 3}}, {"step_id": 18, "name": "capture_final_result", "type": "capture_screenshot", "description": "<PERSON><PERSON> screenshot hasil akhir", "parameters": {"filename": "final_result_{{timestamp}}.png", "quality": 95, "full_page": true}, "visual_node": {"title": "📸 Screenshot Hasil", "subtitle": "Dokumentasi final", "icon": "📸", "color": "#6366f1"}, "conditional": {"enabled": true, "condition": {"type": "verify_variable_equals", "variable": "enable_screenshots", "value": true}, "on_true": "continue", "on_false": "skip_step"}}, {"step_id": 19, "name": "execute_completion_script", "type": "execute_script", "description": "Jalankan script JavaScript untuk menandai selesai", "parameters": {"script": "console.log('Millware automation completed successfully!'); window.millwareAutomationComplete = true; document.title = '✅ ' + document.title;", "wait_for_result": true, "timeout": 5000}, "visual_node": {"title": "🔧 Execute Script", "subtitle": "<PERSON><PERSON><PERSON> otom<PERSON>", "icon": "🔧", "color": "#8b5cf6"}, "conditional": {"enabled": false}}, {"step_id": 20, "name": "final_wait", "type": "wait_seconds", "description": "<PERSON><PERSON><PERSON> sebentar sebelum selesai", "parameters": {"duration": 2, "show_countdown": false}, "visual_node": {"title": "🏁 <PERSON><PERSON><PERSON>", "subtitle": "<PERSON>tomati<PERSON><PERSON> berhasil!", "icon": "🏁", "color": "#22c55e"}, "conditional": {"enabled": false}, "completion_action": {"type": "success_notification", "message": "🎉 Millware automation completed successfully!", "auto_close_after": 5000}}], "global_variables": {"username": "adm075", "password": "adm075", "enable_screenshots": false, "timestamp": "{{current_timestamp}}", "popup_message": ""}, "flow_connections": [{"from": "step_1", "to": "step_2"}, {"from": "step_2", "to": "step_3"}, {"from": "step_3", "to": "step_4", "condition": "default"}, {"from": "step_3", "to": "step_7", "condition": "already_logged_in"}, {"from": "step_4", "to": "step_5"}, {"from": "step_5", "to": "step_6"}, {"from": "step_6", "to": "step_7"}, {"from": "step_7", "to": "step_8"}, {"from": "step_8", "to": "step_9"}, {"from": "step_9", "to": "step_10"}, {"from": "step_10", "to": "step_11"}, {"from": "step_11", "to": "step_12"}, {"from": "step_12", "to": "step_13", "condition": "login_success"}, {"from": "step_12", "to": "step_3", "condition": "login_failed"}, {"from": "step_13", "to": "step_14"}, {"from": "step_14", "to": "step_15"}, {"from": "step_15", "to": "step_16"}, {"from": "step_16", "to": "step_17"}, {"from": "step_17", "to": "step_18"}, {"from": "step_18", "to": "step_19"}, {"from": "step_19", "to": "step_20"}], "error_recovery": {"global_timeout": 180000, "screenshot_on_error": true, "retry_entire_flow": false, "recovery_actions": [{"error_type": "element_not_found", "action": "wait_and_retry", "max_retries": 3, "retry_delay": 2000}, {"error_type": "navigation_timeout", "action": "refresh_and_retry", "max_retries": 2}, {"error_type": "verification_failed", "action": "capture_screenshot_and_continue", "max_retries": 1}]}, "success_metrics": {"completion_time": "target_under_60s", "success_rate": "target_above_95%", "user_satisfaction": "high"}}