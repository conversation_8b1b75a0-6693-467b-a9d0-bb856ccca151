/**
 * Venus Auto Fill Extension Verification Script
 * 
 * This script verifies that all extension files are present and properly configured.
 * Run this in Node.js to check the extension before installation.
 * 
 * Usage: node verify-extension.js
 */

const fs = require('fs');
const path = require('path');

class ExtensionVerifier {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.success = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[${timestamp}] [${type.toUpperCase()}]`;
        
        switch (type) {
            case 'error':
                this.errors.push(message);
                console.error(`${prefix} ${message}`);
                break;
            case 'warning':
                this.warnings.push(message);
                console.warn(`${prefix} ${message}`);
                break;
            case 'success':
                this.success.push(message);
                console.log(`${prefix} ${message}`);
                break;
            default:
                console.log(`${prefix} ${message}`);
        }
    }

    checkFileExists(filePath, required = true) {
        if (fs.existsSync(filePath)) {
            this.log(`✓ Found: ${filePath}`, 'success');
            return true;
        } else {
            const message = `✗ Missing: ${filePath}`;
            if (required) {
                this.log(message, 'error');
            } else {
                this.log(message, 'warning');
            }
            return false;
        }
    }

    checkDirectoryExists(dirPath, required = true) {
        if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
            this.log(`✓ Directory found: ${dirPath}`, 'success');
            return true;
        } else {
            const message = `✗ Directory missing: ${dirPath}`;
            if (required) {
                this.log(message, 'error');
            } else {
                this.log(message, 'warning');
            }
            return false;
        }
    }

    validateManifest() {
        this.log('Validating manifest.json...', 'info');
        
        if (!this.checkFileExists('manifest.json')) {
            return false;
        }

        try {
            const manifestContent = fs.readFileSync('manifest.json', 'utf8');
            const manifest = JSON.parse(manifestContent);

            // Check required fields
            const requiredFields = ['manifest_version', 'name', 'version', 'permissions'];
            requiredFields.forEach(field => {
                if (manifest[field]) {
                    this.log(`✓ Manifest has ${field}`, 'success');
                } else {
                    this.log(`✗ Manifest missing ${field}`, 'error');
                }
            });

            // Check manifest version
            if (manifest.manifest_version === 3) {
                this.log('✓ Using Manifest V3', 'success');
            } else {
                this.log('✗ Should use Manifest V3', 'error');
            }

            // Check permissions
            const recommendedPermissions = ['storage', 'activeTab', 'scripting'];
            recommendedPermissions.forEach(permission => {
                if (manifest.permissions && manifest.permissions.includes(permission)) {
                    this.log(`✓ Has permission: ${permission}`, 'success');
                } else {
                    this.log(`⚠ Missing recommended permission: ${permission}`, 'warning');
                }
            });

            return true;
        } catch (error) {
            this.log(`✗ Invalid manifest.json: ${error.message}`, 'error');
            return false;
        }
    }

    validateHTML() {
        this.log('Validating HTML files...', 'info');
        
        if (!this.checkFileExists('popup.html')) {
            return false;
        }

        try {
            const htmlContent = fs.readFileSync('popup.html', 'utf8');
            
            // Check for required elements
            const requiredElements = [
                'nav-tabs',
                'config',
                'data',
                'flow',
                'execution'
            ];

            requiredElements.forEach(elementId => {
                if (htmlContent.includes(`id="${elementId}"`)) {
                    this.log(`✓ HTML contains element: ${elementId}`, 'success');
                } else {
                    this.log(`⚠ HTML missing element: ${elementId}`, 'warning');
                }
            });

            // Check for CSS and JS references
            if (htmlContent.includes('popup.css')) {
                this.log('✓ HTML references CSS file', 'success');
            } else {
                this.log('⚠ HTML missing CSS reference', 'warning');
            }

            if (htmlContent.includes('popup.js')) {
                this.log('✓ HTML references JS file', 'success');
            } else {
                this.log('⚠ HTML missing JS reference', 'warning');
            }

            return true;
        } catch (error) {
            this.log(`✗ Error reading popup.html: ${error.message}`, 'error');
            return false;
        }
    }

    validateJavaScript() {
        this.log('Validating JavaScript files...', 'info');
        
        const jsFiles = [
            'background.js',
            'content.js',
            'injected.js',
            'scripts/popup.js'
        ];

        let allValid = true;

        jsFiles.forEach(file => {
            if (this.checkFileExists(file)) {
                try {
                    const content = fs.readFileSync(file, 'utf8');
                    
                    // Basic syntax check (very simple)
                    if (content.includes('function') || content.includes('class') || content.includes('=>')) {
                        this.log(`✓ ${file} appears to contain valid JavaScript`, 'success');
                    } else {
                        this.log(`⚠ ${file} might be empty or invalid`, 'warning');
                    }
                } catch (error) {
                    this.log(`✗ Error reading ${file}: ${error.message}`, 'error');
                    allValid = false;
                }
            } else {
                allValid = false;
            }
        });

        return allValid;
    }

    validateCSS() {
        this.log('Validating CSS files...', 'info');
        
        if (!this.checkFileExists('styles/popup.css')) {
            return false;
        }

        try {
            const cssContent = fs.readFileSync('styles/popup.css', 'utf8');
            
            // Check for basic CSS structure
            if (cssContent.includes('{') && cssContent.includes('}')) {
                this.log('✓ CSS file appears valid', 'success');
            } else {
                this.log('⚠ CSS file might be invalid', 'warning');
            }

            // Check for key classes
            const keyClasses = ['.container', '.tab-button', '.btn'];
            keyClasses.forEach(className => {
                if (cssContent.includes(className)) {
                    this.log(`✓ CSS contains class: ${className}`, 'success');
                } else {
                    this.log(`⚠ CSS missing class: ${className}`, 'warning');
                }
            });

            return true;
        } catch (error) {
            this.log(`✗ Error reading CSS file: ${error.message}`, 'error');
            return false;
        }
    }

    validateIcons() {
        this.log('Validating icon files...', 'info');
        
        if (!this.checkDirectoryExists('icons')) {
            return false;
        }

        const iconSizes = ['16', '32', '48', '128'];
        let allIconsPresent = true;

        iconSizes.forEach(size => {
            const iconPath = `icons/icon${size}.png`;
            if (!this.checkFileExists(iconPath)) {
                allIconsPresent = false;
            }
        });

        return allIconsPresent;
    }

    validateSampleFiles() {
        this.log('Validating sample and documentation files...', 'info');
        
        const sampleFiles = [
            'README.md',
            'sample-flows.json',
            'google-apps-script-sample.js'
        ];

        sampleFiles.forEach(file => {
            this.checkFileExists(file, false); // Not required but recommended
        });
    }

    generateReport() {
        console.log('\n' + '='.repeat(60));
        console.log('VENUS AUTO FILL EXTENSION VERIFICATION REPORT');
        console.log('='.repeat(60));
        
        console.log(`\n✓ Successful checks: ${this.success.length}`);
        console.log(`⚠ Warnings: ${this.warnings.length}`);
        console.log(`✗ Errors: ${this.errors.length}`);

        if (this.warnings.length > 0) {
            console.log('\nWarnings:');
            this.warnings.forEach(warning => console.log(`  ⚠ ${warning}`));
        }

        if (this.errors.length > 0) {
            console.log('\nErrors:');
            this.errors.forEach(error => console.log(`  ✗ ${error}`));
        }

        console.log('\n' + '='.repeat(60));
        
        if (this.errors.length === 0) {
            console.log('🎉 VERIFICATION PASSED! Extension is ready for installation.');
            console.log('\nNext steps:');
            console.log('1. Run install.bat (Windows) to get installation instructions');
            console.log('2. Or manually load the extension in Chrome developer mode');
            console.log('3. Configure your Google Apps Script using the sample provided');
        } else {
            console.log('❌ VERIFICATION FAILED! Please fix the errors above.');
            console.log('\nThe extension may not work properly until all errors are resolved.');
        }
        
        console.log('='.repeat(60));
    }

    run() {
        console.log('Starting Venus Auto Fill Extension verification...\n');
        
        this.validateManifest();
        this.validateHTML();
        this.validateJavaScript();
        this.validateCSS();
        this.validateIcons();
        this.validateSampleFiles();
        
        this.generateReport();
        
        return this.errors.length === 0;
    }
}

// Run verification if this script is executed directly
if (require.main === module) {
    const verifier = new ExtensionVerifier();
    const success = verifier.run();
    process.exit(success ? 0 : 1);
}

module.exports = ExtensionVerifier;
