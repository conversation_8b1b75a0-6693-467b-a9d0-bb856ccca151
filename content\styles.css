/**
 * Auto Form Fill Pro - Content Script Styles
 * Additional styles for content script UI elements
 */

/* Form detection overlay styles */
.autofill-form-overlay {
  position: absolute !important;
  pointer-events: none !important;
  border: 2px dashed #3b82f6 !important;
  border-radius: 4px !important;
  background: rgba(59, 130, 246, 0.05) !important;
  z-index: 999 !important;
  transition: all 0.3s ease !important;
}

.autofill-form-overlay.active {
  border-color: #10b981 !important;
  background: rgba(16, 185, 129, 0.05) !important;
}

/* Field count badge */
.autofill-field-count {
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  background: #3b82f6 !important;
  color: white !important;
  border-radius: 50% !important;
  width: 20px !important;
  height: 20px !important;
  font-size: 10px !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe <PERSON>', <PERSON><PERSON>, sans-serif !important;
  z-index: 1001 !important;
  pointer-events: none !important;
}

/* Debug info overlay */
.autofill-debug-info {
  position: fixed !important;
  bottom: 20px !important;
  left: 20px !important;
  background: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 12px !important;
  z-index: 10000 !important;
  max-width: 300px !important;
  opacity: 0 !important;
  transform: translateY(10px) !important;
  transition: all 0.3s ease !important;
  pointer-events: none !important;
}

.autofill-debug-info.visible {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.autofill-debug-info h4 {
  margin: 0 0 8px 0 !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #60a5fa !important;
}

.autofill-debug-info ul {
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

.autofill-debug-info li {
  margin: 4px 0 !important;
  padding: 0 !important;
}

.autofill-debug-info .debug-label {
  color: #9ca3af !important;
}

.autofill-debug-info .debug-value {
  color: #34d399 !important;
  font-weight: 600 !important;
}

/* Fill confirmation dialog */
.autofill-confirm-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  z-index: 10001 !important;
  min-width: 320px !important;
  max-width: 480px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.autofill-confirm-overlay {
  position: fixed !important;
  inset: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: 10000 !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.autofill-confirm-overlay.visible {
  opacity: 1 !important;
}

.autofill-confirm-dialog {
  opacity: 0 !important;
  transform: translate(-50%, -50%) scale(0.95) !important;
  transition: all 0.3s ease !important;
}

.autofill-confirm-dialog.visible {
  opacity: 1 !important;
  transform: translate(-50%, -50%) scale(1) !important;
}

.autofill-confirm-header {
  padding: 20px 24px 16px !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.autofill-confirm-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #111827 !important;
  margin: 0 !important;
}

.autofill-confirm-subtitle {
  font-size: 14px !important;
  color: #6b7280 !important;
  margin: 4px 0 0 0 !important;
}

.autofill-confirm-body {
  padding: 16px 24px !important;
}

.autofill-field-list {
  max-height: 200px !important;
  overflow-y: auto !important;
  margin: 12px 0 !important;
}

.autofill-field-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 12px !important;
  background: #f9fafb !important;
  border-radius: 6px !important;
  margin: 4px 0 !important;
  font-size: 13px !important;
}

.autofill-field-name {
  font-weight: 500 !important;
  color: #374151 !important;
}

.autofill-field-value {
  color: #6b7280 !important;
  font-family: 'Courier New', monospace !important;
  max-width: 150px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.autofill-confirm-footer {
  padding: 16px 24px 20px !important;
  display: flex !important;
  gap: 12px !important;
  justify-content: flex-end !important;
}

.autofill-confirm-btn {
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border: none !important;
  outline: none !important;
}

.autofill-confirm-btn.primary {
  background: #3b82f6 !important;
  color: white !important;
}

.autofill-confirm-btn.primary:hover {
  background: #2563eb !important;
}

.autofill-confirm-btn.secondary {
  background: #f3f4f6 !important;
  color: #374151 !important;
}

.autofill-confirm-btn.secondary:hover {
  background: #e5e7eb !important;
}

/* Loading spinner for inline elements */
.autofill-inline-spinner {
  display: inline-block !important;
  width: 16px !important;
  height: 16px !important;
  border: 2px solid #e5e7eb !important;
  border-top-color: #3b82f6 !important;
  border-radius: 50% !important;
  animation: autofill-spin 1s linear infinite !important;
  margin-right: 8px !important;
}

@keyframes autofill-spin {
  to {
    transform: rotate(360deg) !important;
  }
}

/* Field type indicators */
.autofill-field-type-indicator {
  position: absolute !important;
  top: -6px !important;
  left: -6px !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  font-size: 8px !important;
  font-weight: 600 !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1002 !important;
  pointer-events: none !important;
}

.autofill-field-type-indicator.email {
  background: #3b82f6 !important;
}

.autofill-field-type-indicator.phone {
  background: #10b981 !important;
}

.autofill-field-type-indicator.name {
  background: #f59e0b !important;
}

.autofill-field-type-indicator.address {
  background: #8b5cf6 !important;
}

.autofill-field-type-indicator.default {
  background: #6b7280 !important;
}

/* Success/Error notifications */
.autofill-notification {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  padding: 16px !important;
  max-width: 320px !important;
  z-index: 10000 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  border-left: 4px solid #3b82f6 !important;
  transform: translateX(100%) !important;
  transition: transform 0.3s ease !important;
}

.autofill-notification.visible {
  transform: translateX(0) !important;
}

.autofill-notification.success {
  border-left-color: #10b981 !important;
}

.autofill-notification.error {
  border-left-color: #ef4444 !important;
}

.autofill-notification.warning {
  border-left-color: #f59e0b !important;
}

.autofill-notification-title {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #111827 !important;
  margin: 0 0 4px 0 !important;
}

.autofill-notification-message {
  font-size: 13px !important;
  color: #6b7280 !important;
  margin: 0 !important;
}

.autofill-notification-close {
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  background: none !important;
  border: none !important;
  color: #9ca3af !important;
  cursor: pointer !important;
  font-size: 16px !important;
  padding: 4px !important;
  line-height: 1 !important;
}

.autofill-notification-close:hover {
  color: #6b7280 !important;
}

/* Responsive design for smaller screens */
@media (max-width: 480px) {
  .autofill-confirm-dialog {
    min-width: calc(100vw - 40px) !important;
    margin: 0 20px !important;
  }
  
  .autofill-notification {
    max-width: calc(100vw - 40px) !important;
    margin: 0 20px !important;
  }
  
  .autofill-debug-info {
    max-width: calc(100vw - 40px) !important;
    margin: 0 20px !important;
  }
}

/* Accessibility improvements */
.autofill-highlight:focus-visible {
  outline: 3px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .autofill-highlight.filling::before {
    border-color: #000 !important;
    background: rgba(0, 0, 0, 0.1) !important;
  }
  
  .autofill-highlight.success::before {
    border-color: #000 !important;
    background: rgba(0, 100, 0, 0.1) !important;
  }
  
  .autofill-highlight.error::before {
    border-color: #000 !important;
    background: rgba(200, 0, 0, 0.1) !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .autofill-confirm-dialog {
    background: #1f2937 !important;
    color: #f9fafb !important;
  }
  
  .autofill-confirm-header {
    border-bottom-color: #374151 !important;
  }
  
  .autofill-confirm-title {
    color: #f9fafb !important;
  }
  
  .autofill-confirm-subtitle {
    color: #9ca3af !important;
  }
  
  .autofill-field-item {
    background: #374151 !important;
  }
  
  .autofill-field-name {
    color: #f9fafb !important;
  }
  
  .autofill-notification {
    background: #1f2937 !important;
    color: #f9fafb !important;
  }
  
  .autofill-notification-title {
    color: #f9fafb !important;
  }
  
  .autofill-notification-message {
    color: #d1d5db !important;
  }
}

/* Print styles - hide all autofill elements when printing */
@media print {
  .autofill-highlight,
  .autofill-tooltip,
  .autofill-form-outline,
  .autofill-confirm-dialog,
  .autofill-confirm-overlay,
  .autofill-notification,
  .autofill-debug-info,
  .autofill-progress,
  .autofill-form-overlay,
  .autofill-field-count,
  .autofill-field-type-indicator {
    display: none !important;
  }
} 