/**
 * Auto Form Fill Pro - Popup Interface Logic
 * Handles user interactions and UI state management
 */

class PopupInterface {
  constructor() {
    this.isEnabled = false;
    this.currentTab = null;
    this.extensionStatus = null;
    this.apiStatus = null;
    this.flowManager = null;
    
    this.init();
  }

  /**
   * Initialize the popup interface
   */
  async init() {
    try {
      // Initialize FlowManager first
      await this.initializeFlowManager();
      
      // Get current tab
      await this.getCurrentTab();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Load initial data
      await this.loadInitialData();
      
      // Update UI
      this.updateUI();
      
    } catch (error) {
      console.error('Failed to initialize popup:', error);
      this.showToast('Failed to initialize extension', 'error');
    }
  }

  /**
   * Initialize FlowManager
   */
  async initializeFlowManager() {
    try {
      // Load FlowManager script if not already loaded
      if (!window.FlowManager) {
        await this.loadScript('utils/flow-manager.js');
      }
      
      this.flowManager = new FlowManager();
      console.log('✅ FlowManager initialized');
      
      // Load current flow if exists
      await this.loadCurrentFlow();
      
    } catch (error) {
      console.error('❌ Failed to initialize FlowManager:', error);
      this.showToast('Flow management features may not work', 'warning');
    }
  }

  /**
   * Load script dynamically
   */
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  /**
   * Load current flow from storage
   */
  async loadCurrentFlow() {
    try {
      const result = await chrome.storage.local.get(['currentFlow']);
      if (result.currentFlow && this.flowManager) {
        this.flowManager.currentFlow = result.currentFlow;
        await this.flowManager.updateFlowUI();
      }
    } catch (error) {
      console.error('Failed to load current flow:', error);
    }
  }

  /**
   * Get current active tab
   */
  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tab;
  }

  /**
   * Set up all event listeners
   */
  setupEventListeners() {
    // Extension toggle
    const extensionToggle = document.getElementById('extensionToggle');
    extensionToggle?.addEventListener('change', this.handleExtensionToggle.bind(this));

    // Action buttons
    const runAutomationBtn = document.getElementById('runAutomationBtn');
    runAutomationBtn?.addEventListener('click', this.handleRunAutomation.bind(this));

    const testConnectionBtn = document.getElementById('testConnectionBtn');
    testConnectionBtn?.addEventListener('click', this.handleTestConnection.bind(this));

    const detectFormsBtn = document.getElementById('detectFormsBtn');
    detectFormsBtn?.addEventListener('click', this.handleDetectForms.bind(this));

    // Page info refresh
    const refreshPageInfo = document.getElementById('refreshPageInfo');
    refreshPageInfo?.addEventListener('click', this.handleRefreshPageInfo.bind(this));

    // API test
    const testApiBtn = document.getElementById('testApiBtn');
    testApiBtn?.addEventListener('click', this.handleTestApi.bind(this));

    // Configuration button
    const configBtn = document.getElementById('configBtn');
    configBtn?.addEventListener('click', this.handleOpenConfig.bind(this));

    // Quick settings
    this.setupQuickSettings();

    // Footer links
    const openOptions = document.getElementById('openOptions');
    openOptions?.addEventListener('click', this.handleOpenOptions.bind(this));

    const showHelp = document.getElementById('showHelp');
    showHelp?.addEventListener('click', this.handleShowHelp.bind(this));

    const showLogs = document.getElementById('showLogs');
    showLogs?.addEventListener('click', this.handleShowLogs.bind(this));

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    settingsBtn?.addEventListener('click', this.handleOpenOptions.bind(this));

    // Enhanced Flow Management Event Listeners
    this.setupEnhancedFlowManagement();
  }

  /**
   * Set up quick settings event listeners
   */
  setupQuickSettings() {
    const settings = [
      'highlightFields',
      'confirmBeforeFill',
      'showNotifications',
      'smartDetection'
    ];

    settings.forEach(settingId => {
      const element = document.getElementById(settingId);
      element?.addEventListener('change', (e) => {
        this.handleQuickSettingChange(settingId, e.target.checked);
      });
    });
  }

  /**
   * Set up enhanced flow management event listeners
   */
  setupEnhancedFlowManagement() {
    // File Operations
    const saveFlowToFile = document.getElementById('saveFlowToFile');
    saveFlowToFile?.addEventListener('click', this.handleSaveFlowToFile.bind(this));

    const loadFlowFromFile = document.getElementById('loadFlowFromFile');
    loadFlowFromFile?.addEventListener('click', this.handleLoadFlowFromFile.bind(this));

    const deleteFlowFile = document.getElementById('deleteFlowFile');
    deleteFlowFile?.addEventListener('click', this.handleDeleteFlowFile.bind(this));

    // Validation Controls
    const validateFlow = document.getElementById('validateFlow');
    validateFlow?.addEventListener('click', this.handleValidateFlow.bind(this));

    const dryRunFlow = document.getElementById('dryRunFlow');
    dryRunFlow?.addEventListener('click', this.handleDryRunFlow.bind(this));

    const preflightCheck = document.getElementById('preflightCheck');
    preflightCheck?.addEventListener('click', this.handlePreflightCheck.bind(this));

    // Enhanced Flow Event Listeners
    this.setupFlowEventListeners();
  }

  /**
   * Set up flow event listeners for individual testing
   */
  setupFlowEventListeners() {
    document.addEventListener('click', (e) => {
      // Test Single Event
      if (e.target.classList.contains('test-single-event')) {
        const eventIndex = parseInt(e.target.dataset.eventIndex);
        this.handleTestSingleEvent(eventIndex);
      }

      // Test All Events
      if (e.target.classList.contains('test-all-events')) {
        this.handleTestAllEvents();
      }

      // Edit Event
      if (e.target.classList.contains('edit-event')) {
        const eventIndex = parseInt(e.target.dataset.eventIndex);
        this.handleEditEvent(eventIndex);
      }
    });
  }

  /**
   * Load initial data from background script
   */
  async loadInitialData() {
    try {
      // Get extension status
      const statusResponse = await this.sendMessage({ action: 'GET_EXTENSION_STATUS' });
      if (statusResponse.success) {
        this.extensionStatus = statusResponse.data;
        this.isEnabled = statusResponse.data.isEnabled;
      }

      // Get configuration
      const configResponse = await this.sendMessage({ action: 'GET_CONFIGURATION' });
      if (configResponse.success) {
        this.configuration = configResponse.data;
      }

      // Test API connection
      await this.testApiConnection();

      // Get page information
      await this.refreshPageInfo();

    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }

  /**
   * Handle extension toggle
   */
  async handleExtensionToggle(event) {
    const isEnabled = event.target.checked;
    
    try {
      this.showLoading('Updating extension status...');
      
      const response = await this.sendMessage({ action: 'TOGGLE_EXTENSION' });
      
      if (response.success) {
        this.isEnabled = response.data.isEnabled;
        this.updateToggleState();
        this.showToast(
          `Extension ${this.isEnabled ? 'enabled' : 'disabled'}`,
          'success'
        );
      } else {
        throw new Error(response.error || 'Failed to toggle extension');
      }
    } catch (error) {
      console.error('Toggle error:', error);
      this.showToast('Failed to toggle extension', 'error');
      // Revert toggle state
      event.target.checked = this.isEnabled;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle run automation action
   */
  async handleRunAutomation(event) {
    if (!this.isEnabled) {
      this.showToast('Extension is disabled', 'warning');
      return;
    }

    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);

      this.showLoading('Starting automation flow...');

      // Step 1: Test API connection first
      this.updateLoadingText('Testing API connection...');
      const apiTest = await this.sendMessage({ action: 'TEST_API_CONNECTION' });

      if (!apiTest.success) {
        throw new Error(`API connection failed: ${apiTest.error}`);
      }

      // Step 2: Fetch staging data
      this.updateLoadingText('Fetching staging data...');
      const dataResponse = await this.sendMessage({ action: 'FETCH_STAGING_DATA' });

      if (!dataResponse.success) {
        throw new Error(`Failed to fetch staging data: ${dataResponse.error}`);
      }

      // Step 3: Navigate to target website and run automation
      this.updateLoadingText('Starting login automation...');
      const automationResponse = await this.sendMessage({
        action: 'RUN_COMPLETE_AUTOMATION',
        data: {
          tabId: this.currentTab.id,
          stagingData: dataResponse.data,
          targetUrl: 'http://millwarep3.rebinmas.com:8003/',
          credentials: {
            username: 'adm075',
            password: 'adm075'
          }
        }
      });

      if (automationResponse.success) {
        this.showToast('Automation completed successfully!', 'success');
        await this.refreshPageInfo();
      } else {
        throw new Error(automationResponse.error || 'Automation failed');
      }
    } catch (error) {
      console.error('Automation error:', error);
      this.showToast(`Automation failed: ${error.message}`, 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
      this.hideLoading();
    }
  }

  /**
   * Handle test connection action
   */
  async handleTestConnection(event) {
    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);

      const response = await this.sendMessage({ action: 'TEST_API_CONNECTION' });

      if (response.success) {
        this.showToast('API connection successful!', 'success');
        this.updateApiStatus(response.data);
      } else {
        throw new Error(response.error || 'Connection test failed');
      }
    } catch (error) {
      console.error('Connection test error:', error);
      this.showToast(`Connection test failed: ${error.message}`, 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
    }
  }

  /**
   * Handle open configuration
   */
  handleOpenConfig() {
    this.showToast('Configuration panel coming soon', 'info');
  }

  /**
   * Handle detect forms action
   */
  async handleDetectForms(event) {
    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);

      // Send message to content script to detect forms
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'DETECT_FORMS'
      });

      if (response?.success) {
        const { forms, fields } = response.data;
        this.updatePageInfo({
          formsCount: forms || 0,
          fieldsCount: fields || 0
        });
        this.showToast(`Found ${forms} forms with ${fields} fields`, 'success');
      } else {
        this.showToast('No forms detected on this page', 'warning');
      }
    } catch (error) {
      console.error('Detect forms error:', error);
      this.showToast('Failed to detect forms', 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
    }
  }

  /**
   * Handle refresh page info
   */
  async handleRefreshPageInfo() {
    try {
      await this.refreshPageInfo();
      this.showToast('Page information refreshed', 'success');
    } catch (error) {
      console.error('Refresh error:', error);
      this.showToast('Failed to refresh page info', 'error');
    }
  }

  /**
   * Handle API test
   */
  async handleTestApi() {
    try {
      await this.testApiConnection();
      this.showToast('API connection tested', 'success');
    } catch (error) {
      console.error('API test error:', error);
      this.showToast('API test failed', 'error');
    }
  }

  /**
   * Handle quick setting changes
   */
  async handleQuickSettingChange(settingId, value) {
    try {
      const settingMapping = {
        highlightFields: 'autoFill.highlightFields',
        confirmBeforeFill: 'autoFill.confirmBeforeFill',
        showNotifications: 'ui.showNotifications',
        smartDetection: 'autoFill.smartFieldDetection'
      };

      const configPath = settingMapping[settingId];
      if (!configPath) return;

      const [section, key] = configPath.split('.');
      const updates = { [section]: { [key]: value } };

      const response = await this.sendMessage({
        action: 'UPDATE_CONFIGURATION',
        data: updates
      });

      if (response.success) {
        this.showToast('Setting updated', 'success');
      } else {
        throw new Error(response.error || 'Failed to update setting');
      }
    } catch (error) {
      console.error('Setting update error:', error);
      this.showToast('Failed to update setting', 'error');
    }
  }

  /**
   * Handle open options page
   */
  handleOpenOptions() {
    chrome.runtime.openOptionsPage();
  }

  /**
   * Handle show help
   */
  handleShowHelp() {
    const helpUrl = chrome.runtime.getURL('help/help.html');
    chrome.tabs.create({ url: helpUrl });
  }

  /**
   * Handle show logs
   */
  async handleShowLogs() {
    try {
      // For now, just show a simple logs dialog
      // In a full implementation, you might open a dedicated logs page
      this.showToast('Logs feature coming soon', 'info');
    } catch (error) {
      console.error('Show logs error:', error);
    }
  }

  /**
   * Test API connection
   */
  async testApiConnection() {
    try {
      const response = await this.sendMessage({ action: 'TEST_API_CONNECTION' });
      
      if (response.success) {
        this.apiStatus = response.data;
        this.updateApiStatus();
      } else {
        this.apiStatus = { success: false, error: response.error };
        this.updateApiStatus();
      }
    } catch (error) {
      this.apiStatus = { success: false, error: error.message };
      this.updateApiStatus();
    }
  }

  /**
   * Refresh page information
   */
  async refreshPageInfo() {
    try {
      if (!this.currentTab) return;

      // Update domain info
      const domain = new URL(this.currentTab.url).hostname;
      this.updatePageInfo({ domain });

      // Try to get form information from content script
      try {
        const response = await chrome.tabs.sendMessage(this.currentTab.id, {
          action: 'GET_PAGE_STATS'
        });

        if (response?.success) {
          this.updatePageInfo(response.data);
        }
      } catch (error) {
        // Content script might not be injected yet
        this.updatePageInfo({ formsCount: 0, fieldsCount: 0 });
      }
    } catch (error) {
      console.error('Refresh page info error:', error);
    }
  }

  /**
   * Update UI components
   */
  updateUI() {
    this.updateToggleState();
    this.updateStatusIndicator();
    this.updateActionButtons();
    this.updateQuickSettings();
  }

  /**
   * Update toggle state
   */
  updateToggleState() {
    const toggle = document.getElementById('extensionToggle');
    const description = document.getElementById('toggleDescription');
    
    if (toggle) {
      toggle.checked = this.isEnabled;
    }
    
    if (description) {
      description.textContent = this.isEnabled 
        ? 'Extension is active and ready to fill forms'
        : 'Extension is disabled';
    }
  }

  /**
   * Update status indicator
   */
  updateStatusIndicator() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    if (!statusDot || !statusText) return;

    if (this.isEnabled && this.apiStatus?.success) {
      statusDot.className = 'status-dot connected';
      statusText.textContent = 'Ready';
    } else if (!this.isEnabled) {
      statusDot.className = 'status-dot';
      statusText.textContent = 'Disabled';
    } else {
      statusDot.className = 'status-dot error';
      statusText.textContent = 'API Error';
    }
  }

  /**
   * Update action buttons
   */
  updateActionButtons() {
    const runBtn = document.getElementById('runAutomationBtn');
    const testBtn = document.getElementById('testConnectionBtn');

    if (runBtn) {
      runBtn.disabled = !this.isEnabled;
    }

    if (testBtn) {
      testBtn.disabled = false; // Always allow testing connection
    }
  }

  /**
   * Update quick settings
   */
  updateQuickSettings() {
    if (!this.configuration) return;

    const settings = {
      highlightFields: this.configuration.autoFill?.highlightFields,
      confirmBeforeFill: this.configuration.autoFill?.confirmBeforeFill,
      showNotifications: this.configuration.ui?.showNotifications,
      smartDetection: this.configuration.autoFill?.smartFieldDetection
    };

    Object.entries(settings).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element && typeof value === 'boolean') {
        element.checked = value;
      }
    });
  }

  /**
   * Update page information display
   */
  updatePageInfo(info) {
    const elements = {
      currentDomain: info.domain,
      formsCount: info.formsCount,
      fieldsCount: info.fieldsCount
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element && value !== undefined) {
        element.textContent = value;
      }
    });
  }

  /**
   * Update API status display
   */
  updateApiStatus(statusData = null) {
    const data = statusData || this.apiStatus;

    const elements = {
      apiConnection: document.getElementById('apiConnection'),
      apiAuth: document.getElementById('apiAuth'),
      apiLastResponse: document.getElementById('apiLastResponse'),
      apiEndpoint: document.getElementById('apiEndpoint')
    };

    if (elements.apiConnection) {
      if (data?.success || data?.status === 'connected') {
        elements.apiConnection.textContent = 'Connected';
        elements.apiConnection.className = 'detail-value success';
      } else {
        elements.apiConnection.textContent = 'Failed';
        elements.apiConnection.className = 'detail-value error';
      }
    }

    if (elements.apiAuth) {
      const isConnected = data?.success || data?.status === 'connected';
      elements.apiAuth.textContent = isConnected ? 'Valid' : 'Invalid';
      elements.apiAuth.className = isConnected ? 'detail-value success' : 'detail-value error';
    }

    if (elements.apiLastResponse) {
      const timestamp = data?.timestamp;
      if (timestamp) {
        const time = new Date(timestamp).toLocaleTimeString();
        elements.apiLastResponse.textContent = time;
      }
    }

    // Update API endpoint display with connection details
    if (elements.apiEndpoint) {
      if (data.success) {
        elements.apiEndpoint.textContent = `localhost:5173 (${data.recordCount} records)`;
        elements.apiEndpoint.className = 'status connected';
      } else {
        elements.apiEndpoint.textContent = 'localhost:5173 (offline)';
        elements.apiEndpoint.className = 'status error';
      }
    }
  }

  /**
   * Set button loading state
   */
  setButtonLoading(button, isLoading) {
    if (!button) return;

    if (isLoading) {
      button.classList.add('loading');
      button.disabled = true;
    } else {
      button.classList.remove('loading');
      button.disabled = false;
    }
  }

  /**
   * Show loading overlay
   */
  showLoading(text = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const loadingText = document.getElementById('loadingText');
    
    if (overlay) {
      overlay.classList.add('visible');
    }
    
    if (loadingText) {
      loadingText.textContent = text;
    }
  }

  /**
   * Hide loading overlay
   */
  hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      overlay.classList.remove('visible');
    }
  }

  /**
   * Update loading text
   */
  updateLoadingText(text) {
    const loadingText = document.getElementById('loadingText');
    if (loadingText) {
      loadingText.textContent = text;
    }
  }

  /**
   * Show toast notification
   */
  showToast(message, type = 'info', duration = 3000) {
    const container = document.getElementById('toastContainer');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-text">${message}</div>
        <button class="toast-close">&times;</button>
      </div>
    `;

    // Add close functionality
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', () => {
      this.removeToast(toast);
    });

    container.appendChild(toast);

    // Show toast
    setTimeout(() => {
      toast.classList.add('visible');
    }, 10);

    // Auto-remove after duration
    setTimeout(() => {
      this.removeToast(toast);
    }, duration);
  }

  /**
   * Remove toast notification
   */
  removeToast(toast) {
    toast.classList.remove('visible');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }

  /**
   * Send message to background script
   */
  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          resolve({ success: false, error: chrome.runtime.lastError.message });
        } else {
          resolve(response || { success: false, error: 'No response' });
        }
      });
    });
  }

  // ================== ENHANCED FLOW MANAGEMENT HANDLERS ==================

  /**
   * Handle save flow to file
   */
  async handleSaveFlowToFile() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Saving flow to file...');
      await this.flowManager.saveFlowToFile();
      
    } catch (error) {
      console.error('❌ Error saving flow to file:', error);
      this.showToast(`Failed to save flow: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle load flow from file
   */
  async handleLoadFlowFromFile() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Loading flow from file...');
      await this.flowManager.loadFlowFromFile();
      
      // Update flow UI after loading
      await this.updateFlowDisplays();
      
    } catch (error) {
      console.error('❌ Error loading flow from file:', error);
      this.showToast(`Failed to load flow: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle delete flow file
   */
  async handleDeleteFlowFile() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Deleting flow...');
      await this.flowManager.deleteFlowFile();
      
      // Update flow UI after deletion
      await this.updateFlowDisplays();
      
    } catch (error) {
      console.error('❌ Error deleting flow:', error);
      this.showToast(`Failed to delete flow: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle validate flow
   */
  async handleValidateFlow() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Validating flow...');
      const validation = await this.flowManager.validateCurrentFlow();
      
      // Update validation status in UI
      this.updateValidationStatus(validation);
      
    } catch (error) {
      console.error('❌ Error validating flow:', error);
      this.showToast(`Validation failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle dry run flow
   */
  async handleDryRunFlow() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Performing dry run...');
      await this.flowManager.performDryRun();
      
    } catch (error) {
      console.error('❌ Error performing dry run:', error);
      this.showToast(`Dry run failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle preflight check
   */
  async handlePreflightCheck() {
    try {
      if (!this.flowManager || !this.flowManager.currentFlow) {
        throw new Error('No flow available for preflight check');
      }

      this.showLoading('Performing preflight check...');
      
      const preflightResult = await this.flowManager.flowValidator.preflightCheck(
        this.flowManager.currentFlow
      );
      
      if (preflightResult) {
        const message = `Preflight Check: ${preflightResult.elementsFound}/${preflightResult.totalElements} elements found`;
        const type = preflightResult.elementsFound === preflightResult.totalElements ? 'success' : 'warning';
        this.showToast(message, type);
        
        if (preflightResult.missingElements.length > 0) {
          console.warn('Missing elements:', preflightResult.missingElements);
        }
      }
      
    } catch (error) {
      console.error('❌ Error performing preflight check:', error);
      this.showToast(`Preflight check failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle test single event
   */
  async handleTestSingleEvent(eventIndex) {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading(`Testing event ${eventIndex + 1}...`);
      await this.flowManager.testSingleEvent(eventIndex);
      
    } catch (error) {
      console.error('❌ Error testing single event:', error);
      this.showToast(`Event test failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle test all events
   */
  async handleTestAllEvents() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Testing all events...');
      await this.flowManager.testAllEvents();
      
    } catch (error) {
      console.error('❌ Error testing all events:', error);
      this.showToast(`Event testing failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle edit event
   */
  async handleEditEvent(eventIndex) {
    try {
      if (!this.flowManager || !this.flowManager.currentFlow) {
        throw new Error('No flow available for editing');
      }

      const event = this.flowManager.currentFlow.flow_steps[eventIndex];
      if (!event) {
        throw new Error(`Event at index ${eventIndex} not found`);
      }

      // Show event edit modal (could be implemented as a separate feature)
      this.showEventEditModal(event, eventIndex);
      
    } catch (error) {
      console.error('❌ Error editing event:', error);
      this.showToast(`Failed to edit event: ${error.message}`, 'error');
    }
  }

  // ================== ENHANCED FLOW MANAGEMENT UTILITIES ==================

  /**
   * Update flow displays in UI
   */
  async updateFlowDisplays() {
    try {
      if (this.flowManager && this.flowManager.currentFlow) {
        // Update enhanced flow list
        const enhancedFlowContainer = document.getElementById('flowEventsContainer');
        if (enhancedFlowContainer) {
          enhancedFlowContainer.innerHTML = this.generateEnhancedFlowHTML();
        }

        // Update traditional flow list
        await this.flowManager.updateFlowUI();

        // Save current flow to storage
        await chrome.storage.local.set({ 
          currentFlow: this.flowManager.currentFlow 
        });
      }
    } catch (error) {
      console.error('Error updating flow displays:', error);
    }
  }

  /**
   * Generate enhanced flow HTML
   */
  generateEnhancedFlowHTML() {
    if (!this.flowManager || !this.flowManager.currentFlow || !this.flowManager.currentFlow.flow_steps) {
      return '<p class="no-flow-message">No flow loaded. Load or create a flow to get started.</p>';
    }

    const flow = this.flowManager.currentFlow;
    
    return `
      <div class="flow-info">
        <h5>${flow.name || 'Untitled Flow'}</h5>
        <p class="flow-description">${flow.description || 'No description available'}</p>
        <div class="flow-stats">
          <span class="stat">📋 ${flow.flow_steps.length} Events</span>
          <span class="stat">📝 ${flow.version || 'v1.0.0'}</span>
        </div>
      </div>
      
      <div class="flow-events">
        ${flow.flow_steps.map((event, index) => this.generateEventCardHTML(event, index)).join('')}
      </div>

      <div class="flow-controls-enhanced">
        <button class="btn btn-primary test-all-events">
          🧪 Test All Events
        </button>
        <button class="btn btn-success" id="executeFlow">
          🚀 Execute Flow
        </button>
      </div>
    `;
  }

  /**
   * Generate individual event card HTML
   */
  generateEventCardHTML(event, index) {
    return `
      <div class="event-card" data-index="${index}">
        <div class="event-header">
          <div class="event-type-badge ${this.getEventTypeClass(event.type)}">
            ${this.getEventIcon(event.type)} ${event.type}
          </div>
          <div class="event-status" id="eventStatus_${index}">
            <span class="status-dot" data-status="pending" title="Not tested"></span>
          </div>
        </div>
        
        <div class="event-content">
          <h6 class="event-name">${event.name || `Event ${index + 1}`}</h6>
          <p class="event-description">${event.description || 'No description'}</p>
          
          <div class="event-details">
            ${this.generateEventDetailsHTML(event)}
          </div>
        </div>
        
        <div class="event-actions">
          <button class="btn btn-sm btn-outline test-single-event" data-event-index="${index}" title="Test this event">
            🧪 Test
          </button>
          <button class="btn btn-sm btn-outline edit-event" data-event-index="${index}" title="Edit event parameters">
            ✏️ Edit
          </button>
          <button class="btn btn-sm btn-outline" onclick="this.showEventDetails(${index})" title="View full event details">
            👁️ Details
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Generate event details HTML
   */
  generateEventDetailsHTML(event) {
    const details = [];
    
    if (event.selector) {
      details.push(`<span class="detail-item"><strong>Selector:</strong> ${event.selector}</span>`);
    }
    if (event.value) {
      details.push(`<span class="detail-item"><strong>Value:</strong> ${event.value}</span>`);
    }
    if (event.url) {
      details.push(`<span class="detail-item"><strong>URL:</strong> ${event.url}</span>`);
    }
    if (event.duration) {
      details.push(`<span class="detail-item"><strong>Duration:</strong> ${event.duration}ms</span>`);
    }
    if (event.searchText || event.searchTexts) {
      const searchText = event.searchText || (event.searchTexts && event.searchTexts.join(', '));
      details.push(`<span class="detail-item"><strong>Search:</strong> ${searchText}</span>`);
    }
    
    return details.length > 0 ? details.join('') : '<span class="detail-item">No specific parameters</span>';
  }

  /**
   * Get event type CSS class
   */
  getEventTypeClass(type) {
    const typeClasses = {
      'click': 'event-type-click',
      'input': 'event-type-input',
      'navigate': 'event-type-navigate',
      'wait': 'event-type-wait',
      'text_search_click': 'event-type-search',
      'conditional_action': 'event-type-conditional',
      'extract': 'event-type-extract'
    };
    return typeClasses[type] || 'event-type-default';
  }

  /**
   * Get event type icon
   */
  getEventIcon(type) {
    const typeIcons = {
      'click': '👆',
      'input': '⌨️',
      'navigate': '🌐',
      'wait': '⏳',
      'text_search_click': '🔍',
      'conditional_action': '🔀',
      'extract': '📊',
      'scroll': '📜'
    };
    return typeIcons[type] || '⚙️';
  }

  /**
   * Update validation status display
   */
  updateValidationStatus(validation) {
    const statusElement = document.getElementById('validationStatus');
    if (!statusElement) return;

    let statusClass = 'neutral';
    let statusText = 'No validation performed';

    if (validation) {
      if (validation.isValid) {
        statusClass = 'valid';
        statusText = '✅ Flow is valid';
        if (validation.warnings.length > 0) {
          statusText += ` (${validation.warnings.length} warnings)`;
          statusClass = 'warning';
        }
      } else {
        statusClass = 'invalid';
        statusText = `❌ Flow has ${validation.errors.length} error(s)`;
      }
    }

    statusElement.className = `validation-status ${statusClass}`;
    statusElement.innerHTML = `<span class="status-text">${statusText}</span>`;
  }

  /**
   * Show event edit modal
   */
  showEventEditModal(event, eventIndex) {
    const modalHTML = `
      <div class="event-edit-form">
        <div class="form-group">
          <label for="eventName">Event Name:</label>
          <input type="text" id="eventName" value="${event.name || ''}" placeholder="Enter event name">
        </div>
        
        <div class="form-group">
          <label for="eventDescription">Description:</label>
          <textarea id="eventDescription" placeholder="Enter event description">${event.description || ''}</textarea>
        </div>
        
        <div class="form-group">
          <label for="eventType">Event Type:</label>
          <select id="eventType" value="${event.type}">
            <option value="click">Click</option>
            <option value="input">Input</option>
            <option value="navigate">Navigate</option>
            <option value="wait">Wait</option>
            <option value="text_search_click">Text Search Click</option>
            <option value="conditional_action">Conditional Action</option>
            <option value="extract">Extract</option>
          </select>
        </div>
        
        <div id="eventSpecificFields">
          ${this.generateEventSpecificFields(event)}
        </div>
        
        <div class="form-actions">
          <button class="btn btn-primary" onclick="this.saveEventChanges(${eventIndex})">Save Changes</button>
          <button class="btn btn-secondary" onclick="this.testEventChanges(${eventIndex})">Test Changes</button>
        </div>
      </div>
    `;

    if (this.flowManager && this.flowManager.showModal) {
      this.flowManager.showModal(`Edit Event ${eventIndex + 1}`, modalHTML);
    }
  }

  /**
   * Generate event-specific form fields
   */
  generateEventSpecificFields(event) {
    switch (event.type) {
      case 'click':
        return `
          <div class="form-group">
            <label for="eventSelector">Selector:</label>
            <input type="text" id="eventSelector" value="${event.selector || ''}" placeholder="CSS selector">
          </div>
        `;
      
      case 'input':
        return `
          <div class="form-group">
            <label for="eventSelector">Selector:</label>
            <input type="text" id="eventSelector" value="${event.selector || ''}" placeholder="CSS selector">
          </div>
          <div class="form-group">
            <label for="eventValue">Value:</label>
            <input type="text" id="eventValue" value="${event.value || ''}" placeholder="Input value">
          </div>
        `;
      
      case 'navigate':
        return `
          <div class="form-group">
            <label for="eventUrl">URL:</label>
            <input type="url" id="eventUrl" value="${event.url || ''}" placeholder="https://example.com">
          </div>
        `;
      
      case 'wait':
        return `
          <div class="form-group">
            <label for="eventDuration">Duration (ms):</label>
            <input type="number" id="eventDuration" value="${event.duration || 1000}" min="100" max="30000">
          </div>
        `;
      
      case 'text_search_click':
        return `
          <div class="form-group">
            <label for="eventSearchText">Search Text:</label>
            <input type="text" id="eventSearchText" value="${event.searchText || ''}" placeholder="Text to search for">
          </div>
        `;
      
      default:
        return '<p>No specific fields for this event type</p>';
    }
  }

  /**
   * Show event details in modal
   */
  showEventDetails(eventIndex) {
    if (!this.flowManager || !this.flowManager.currentFlow) return;

    const event = this.flowManager.currentFlow.flow_steps[eventIndex];
    if (!event) return;

    const detailsHTML = `
      <div class="event-details-view">
        <div class="detail-section">
          <h5>Basic Information</h5>
          <table class="details-table">
            <tr><td><strong>Type:</strong></td><td>${event.type}</td></tr>
            <tr><td><strong>Name:</strong></td><td>${event.name || 'Unnamed'}</td></tr>
            <tr><td><strong>Order:</strong></td><td>${event.order || eventIndex + 1}</td></tr>
          </table>
        </div>
        
        <div class="detail-section">
          <h5>Parameters</h5>
          <pre class="json-display">${JSON.stringify(event.parameters || event, null, 2)}</pre>
        </div>
        
        ${event.success_criteria ? `
          <div class="detail-section">
            <h5>Success Criteria</h5>
            <pre class="json-display">${JSON.stringify(event.success_criteria, null, 2)}</pre>
          </div>
        ` : ''}
      </div>
    `;

    if (this.flowManager && this.flowManager.showModal) {
      this.flowManager.showModal(`Event ${eventIndex + 1} Details`, detailsHTML);
    }
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new PopupInterface();
}); 