/**
 * Auto Form Fill Pro - Storage Manager
 * Handles secure storage of configuration and user data
 */

import { Logger } from '../utils/logger.js';
import { CryptoUtils } from '../utils/crypto.js';

export class StorageManager {
  constructor() {
    this.logger = new Logger('StorageManager');
    this.defaultConfig = {
      isEnabled: true,
      api: {
        baseUrl: 'http://localhost:3000',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      autoFill: {
        enabled: true,
        highlightFields: true,
        animationSpeed: 'medium',
        confirmBeforeFill: false,
        smartFieldDetection: true
      },
      domains: {
        whitelist: [],
        blacklist: []
      },
      fieldMappings: {},
      ui: {
        showNotifications: true,
        theme: 'auto',
        compactMode: false
      },
      security: {
        encryptData: true,
        clearDataOnLogout: false,
        sessionTimeout: 3600000 // 1 hour
      }
    };
  }

  /**
   * Set default configuration on first install
   */
  async setDefaultConfiguration() {
    try {
      const existing = await chrome.storage.sync.get(['configuration']);
      
      if (!existing.configuration) {
        await chrome.storage.sync.set({ 
          configuration: this.defaultConfig,
          version: '1.0.0',
          installedAt: new Date().toISOString()
        });
        
        this.logger.info('Default configuration set');
      }
    } catch (error) {
      this.logger.error('Failed to set default configuration:', error);
      throw error;
    }
  }

  /**
   * Get current configuration
   */
  async getConfiguration() {
    try {
      const stored = await chrome.storage.sync.get(['configuration']);
      
      if (stored.configuration) {
        // Merge with defaults to ensure all properties exist
        return this.mergeWithDefaults(stored.configuration);
      }
      
      return this.defaultConfig;
    } catch (error) {
      this.logger.error('Failed to get configuration:', error);
      return this.defaultConfig;
    }
  }

  /**
   * Update configuration
   */
  async updateConfiguration(updates) {
    try {
      const current = await this.getConfiguration();
      const updated = this.deepMerge(current, updates);
      
      await chrome.storage.sync.set({ 
        configuration: updated,
        lastUpdated: new Date().toISOString()
      });
      
      this.logger.info('Configuration updated');
      return updated;
    } catch (error) {
      this.logger.error('Failed to update configuration:', error);
      throw error;
    }
  }

  /**
   * Store encrypted credentials
   */
  async storeCredentials(credentials) {
    try {
      if (!credentials || typeof credentials !== 'object') {
        throw new Error('Invalid credentials format');
      }

      const encrypted = await CryptoUtils.encrypt(JSON.stringify(credentials));
      
      await chrome.storage.local.set({
        encryptedCredentials: encrypted,
        credentialsUpdated: new Date().toISOString()
      });
      
      this.logger.info('Credentials stored securely');
    } catch (error) {
      this.logger.error('Failed to store credentials:', error);
      throw error;
    }
  }

  /**
   * Get decrypted credentials
   */
  async getCredentials() {
    try {
      const stored = await chrome.storage.local.get(['encryptedCredentials']);
      
      if (!stored.encryptedCredentials) {
        return null;
      }

      const decrypted = await CryptoUtils.decrypt(stored.encryptedCredentials);
      return JSON.parse(decrypted);
    } catch (error) {
      this.logger.error('Failed to get credentials:', error);
      return null;
    }
  }

  /**
   * Clear stored credentials
   */
  async clearCredentials() {
    try {
      await chrome.storage.local.remove(['encryptedCredentials', 'credentialsUpdated']);
      this.logger.info('Credentials cleared');
    } catch (error) {
      this.logger.error('Failed to clear credentials:', error);
      throw error;
    }
  }

  /**
   * Store field mappings for a domain
   */
  async storeFieldMappings(domain, mappings) {
    try {
      const config = await this.getConfiguration();
      
      if (!config.fieldMappings) {
        config.fieldMappings = {};
      }
      
      config.fieldMappings[domain] = {
        mappings,
        updatedAt: new Date().toISOString()
      };
      
      await this.updateConfiguration({ fieldMappings: config.fieldMappings });
      
      this.logger.info(`Field mappings stored for domain: ${domain}`);
    } catch (error) {
      this.logger.error('Failed to store field mappings:', error);
      throw error;
    }
  }

  /**
   * Get field mappings for a domain
   */
  async getFieldMappings(domain) {
    try {
      const config = await this.getConfiguration();
      
      if (config.fieldMappings && config.fieldMappings[domain]) {
        return config.fieldMappings[domain].mappings || {};
      }
      
      return {};
    } catch (error) {
      this.logger.error('Failed to get field mappings:', error);
      return {};
    }
  }

  /**
   * Store form data cache
   */
  async storeFormDataCache(key, data, ttl = 3600000) { // 1 hour default TTL
    try {
      const cacheEntry = {
        data,
        timestamp: Date.now(),
        ttl,
        expiresAt: Date.now() + ttl
      };

      await chrome.storage.local.set({
        [`cache_${key}`]: cacheEntry
      });
      
      this.logger.info(`Form data cached with key: ${key}`);
    } catch (error) {
      this.logger.error('Failed to store form data cache:', error);
      throw error;
    }
  }

  /**
   * Get cached form data
   */
  async getFormDataCache(key) {
    try {
      const stored = await chrome.storage.local.get([`cache_${key}`]);
      const cacheEntry = stored[`cache_${key}`];
      
      if (!cacheEntry) {
        return null;
      }

      // Check if cache has expired
      if (Date.now() > cacheEntry.expiresAt) {
        await this.clearFormDataCache(key);
        return null;
      }

      return cacheEntry.data;
    } catch (error) {
      this.logger.error('Failed to get form data cache:', error);
      return null;
    }
  }

  /**
   * Clear specific cached form data
   */
  async clearFormDataCache(key) {
    try {
      await chrome.storage.local.remove([`cache_${key}`]);
      this.logger.info(`Cache cleared for key: ${key}`);
    } catch (error) {
      this.logger.error('Failed to clear form data cache:', error);
    }
  }

  /**
   * Clear all cached form data
   */
  async clearAllCache() {
    try {
      const all = await chrome.storage.local.get();
      const cacheKeys = Object.keys(all).filter(key => key.startsWith('cache_'));
      
      if (cacheKeys.length > 0) {
        await chrome.storage.local.remove(cacheKeys);
        this.logger.info(`Cleared ${cacheKeys.length} cache entries`);
      }
    } catch (error) {
      this.logger.error('Failed to clear all cache:', error);
    }
  }

  /**
   * Store user activity log
   */
  async logActivity(activity) {
    try {
      const config = await this.getConfiguration();
      
      if (!config.security.logActivity) {
        return; // Activity logging is disabled
      }

      const logEntry = {
        ...activity,
        timestamp: new Date().toISOString(),
        id: this.generateId()
      };

      // Get existing logs
      const stored = await chrome.storage.local.get(['activityLog']);
      const logs = stored.activityLog || [];
      
      // Add new entry and keep only last 1000 entries
      logs.push(logEntry);
      const trimmedLogs = logs.slice(-1000);
      
      await chrome.storage.local.set({ activityLog: trimmedLogs });
      
      this.logger.debug('Activity logged:', activity.type);
    } catch (error) {
      this.logger.error('Failed to log activity:', error);
    }
  }

  /**
   * Get activity logs
   */
  async getActivityLogs(limit = 100) {
    try {
      const stored = await chrome.storage.local.get(['activityLog']);
      const logs = stored.activityLog || [];
      
      return logs.slice(-limit);
    } catch (error) {
      this.logger.error('Failed to get activity logs:', error);
      return [];
    }
  }

  /**
   * Clear activity logs
   */
  async clearActivityLogs() {
    try {
      await chrome.storage.local.remove(['activityLog']);
      this.logger.info('Activity logs cleared');
    } catch (error) {
      this.logger.error('Failed to clear activity logs:', error);
    }
  }

  /**
   * Export configuration and data
   */
  async exportData() {
    try {
      const syncData = await chrome.storage.sync.get();
      const localData = await chrome.storage.local.get();
      
      // Don't export sensitive data
      const exportData = {
        configuration: syncData.configuration,
        fieldMappings: syncData.fieldMappings,
        version: syncData.version,
        exportedAt: new Date().toISOString()
      };
      
      this.logger.info('Data exported');
      return exportData;
    } catch (error) {
      this.logger.error('Failed to export data:', error);
      throw error;
    }
  }

  /**
   * Import configuration and data
   */
  async importData(data) {
    try {
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid import data format');
      }

      // Validate the imported data
      const validatedData = this.validateImportData(data);
      
      // Backup current configuration
      const backup = await this.exportData();
      await chrome.storage.local.set({ 
        configBackup: backup,
        backupCreated: new Date().toISOString()
      });
      
      // Import the new configuration
      if (validatedData.configuration) {
        await chrome.storage.sync.set({ 
          configuration: validatedData.configuration,
          lastUpdated: new Date().toISOString()
        });
      }
      
      this.logger.info('Data imported successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to import data:', error);
      throw error;
    }
  }

  /**
   * Migrate configuration for version updates
   */
  async migrateConfiguration(config, fromVersion) {
    try {
      let migrated = { ...config };
      
      // Add migration logic for different versions
      if (this.compareVersions(fromVersion, '1.0.0') < 0) {
        // Migration from pre-1.0.0
        migrated = this.mergeWithDefaults(migrated);
      }
      
      this.logger.info(`Configuration migrated from version ${fromVersion}`);
      return migrated;
    } catch (error) {
      this.logger.error('Failed to migrate configuration:', error);
      return config;
    }
  }

  /**
   * Deep merge two objects
   */
  deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Merge configuration with defaults
   */
  mergeWithDefaults(config) {
    return this.deepMerge(this.defaultConfig, config);
  }

  /**
   * Validate imported data
   */
  validateImportData(data) {
    const validated = {};
    
    if (data.configuration && typeof data.configuration === 'object') {
      validated.configuration = this.mergeWithDefaults(data.configuration);
    }
    
    return validated;
  }

  /**
   * Compare version strings
   */
  compareVersions(a, b) {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);
    
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;
      
      if (aPart < bPart) return -1;
      if (aPart > bPart) return 1;
    }
    
    return 0;
  }

  /**
   * Generate unique ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats() {
    try {
      const syncUsage = await chrome.storage.sync.getBytesInUse();
      const localUsage = await chrome.storage.local.getBytesInUse();
      
      return {
        sync: {
          used: syncUsage,
          quota: chrome.storage.sync.QUOTA_BYTES,
          percentage: (syncUsage / chrome.storage.sync.QUOTA_BYTES) * 100
        },
        local: {
          used: localUsage,
          quota: chrome.storage.local.QUOTA_BYTES,
          percentage: (localUsage / chrome.storage.local.QUOTA_BYTES) * 100
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Failed to get storage stats:', error);
      return null;
    }
  }
} 