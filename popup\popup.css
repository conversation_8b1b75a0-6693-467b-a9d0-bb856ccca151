/**
 * Auto Form Fill Pro - Popup Styles
 * Modern, responsive design for 320x400px popup
 */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 320px;
    height: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    overflow: hidden;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 16px;
    flex-shrink: 0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

.header-text {
    flex: 1;
}

.header-text h1 {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin-bottom: 2px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ffd700;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #4ade80;
    animation: none;
}

.status-dot.error {
    background: #f87171;
    animation: pulse 1s infinite;
}

.status-text {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.settings-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.settings-btn:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 16px;
    background: white;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #e5e7eb #f9fafb;
}

.main-content::-webkit-scrollbar {
    width: 4px;
}

.main-content::-webkit-scrollbar-track {
    background: #f9fafb;
}

.main-content::-webkit-scrollbar-thumb {
    background: #e5e7eb;
    border-radius: 2px;
}

/* Sections */
section {
    margin-bottom: 20px;
}

section:last-child {
    margin-bottom: 0;
}

/* Toggle Switch */
.control-section {
    margin-bottom: 24px;
}

.toggle-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.toggle-switch {
    position: relative;
    width: 44px;
    height: 24px;
    cursor: pointer;
}

.toggle-switch input {
    display: none;
}

.toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #cbd5e1;
    border-radius: 24px;
    transition: all 0.3s ease;
}

.toggle-slider:before {
    content: '';
    position: absolute;
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.toggle-switch input:checked + .toggle-slider {
    background: #667eea;
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.toggle-info {
    flex: 1;
}

.toggle-label {
    display: block;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.toggle-description {
    font-size: 12px;
    color: #64748b;
}

/* Action Buttons */
.actions-section {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
}

.action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 1px solid transparent;
}

.action-btn.primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
}

.action-btn.secondary:hover:not(:disabled) {
    background: #f9fafb;
    border-color: #9ca3af;
}

.btn-icon {
    transition: transform 0.2s ease;
}

.action-btn:hover .btn-icon {
    transform: scale(1.1);
}

.btn-loader {
    position: absolute;
    inset: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background: inherit;
}

.action-btn.loading .btn-text,
.action-btn.loading .btn-icon {
    opacity: 0;
}

.action-btn.loading .btn-loader {
    display: flex;
}

.loader-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Info Sections */
.info-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.info-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
}

.refresh-btn,
.test-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.refresh-btn:hover,
.test-btn:hover {
    color: #374151;
    background: #f1f5f9;
}

.page-details,
.api-details {
    background: #f8fafc;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e2e8f0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
}

.detail-item:not(:last-child) {
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 4px;
    padding-bottom: 8px;
}

.detail-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

.detail-value {
    font-size: 12px;
    color: #1e293b;
    font-weight: 500;
}

.detail-value.success {
    color: #059669;
}

.detail-value.error {
    color: #dc2626;
}

.detail-value.warning {
    color: #d97706;
}

/* Quick Settings */
.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.setting-item:hover {
    background: #f1f5f9;
}

.setting-item input {
    width: 14px;
    height: 14px;
    accent-color: #667eea;
}

.setting-label {
    font-size: 12px;
    color: #374151;
    font-weight: 500;
}

/* Footer */
.footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: 12px 16px;
    flex-shrink: 0;
}

.shortcuts-info {
    text-align: center;
    margin-bottom: 8px;
}

.shortcut-text {
    font-size: 11px;
    color: #64748b;
}

.shortcut-text kbd {
    background: #e2e8f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
    color: #374151;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.link-btn {
    background: none;
    border: none;
    color: #667eea;
    font-size: 11px;
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.link-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #5b6ee8;
}

.separator {
    color: #cbd5e1;
    font-size: 11px;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.visible {
    display: flex;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top-color: #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

.loading-text {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 16px;
    right: 16px;
    z-index: 2000;
    pointer-events: none;
}

.toast {
    background: white;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #667eea;
    max-width: 280px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    pointer-events: auto;
}

.toast.visible {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #10b981;
}

.toast.error {
    border-left-color: #ef4444;
}

.toast.warning {
    border-left-color: #f59e0b;
}

.toast-content {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.toast-text {
    flex: 1;
    font-size: 13px;
    color: #374151;
    line-height: 1.4;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 2px;
}

.toast-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0;
    font-size: 14px;
    line-height: 1;
}

.toast-close:hover {
    color: #6b7280;
}

/* Animations */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* Responsive Design */
@media (max-height: 360px) {
    .main-content {
        padding: 12px;
    }
    
    section {
        margin-bottom: 16px;
    }
    
    .toggle-container {
        padding: 12px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .main-content {
        background: #1e293b;
        color: #e2e8f0;
    }
    
    .toggle-container,
    .page-details,
    .api-details {
        background: #334155;
        border-color: #475569;
    }
    
    .toggle-label {
        color: #e2e8f0;
    }
    
    .toggle-description,
    .detail-label {
        color: #94a3b8;
    }
    
    .detail-value {
        color: #e2e8f0;
    }
    
    .info-header h3 {
        color: #e2e8f0;
    }
    
    .action-btn.secondary {
        background: #334155;
        color: #e2e8f0;
        border-color: #475569;
    }
    
    .action-btn.secondary:hover:not(:disabled) {
        background: #475569;
    }
    
    .setting-item:hover {
        background: #475569;
    }
    
    .setting-label {
        color: #e2e8f0;
    }
    
    .footer {
        background: #334155;
        border-color: #475569;
    }
    
    .toast {
        background: #334155;
        color: #e2e8f0;
    }
} 