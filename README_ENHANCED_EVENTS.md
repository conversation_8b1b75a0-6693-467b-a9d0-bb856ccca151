# 🚀 Enhanced Flow Events - Panduan Lengkap

## 📋 Ringkasan

Sistem Enhanced Flow Events menyediakan **31+ jenis event** untuk automasi web yang kompleks dan fleksibel. Setiap event dapat memiliki **conditional logic opsional** untuk mengatur alur eksekusi secara dinamis.

---

## 🎯 Event Categories

### 🌐 **Navigation Events** (5 jenis)
Event untuk navigasi halaman web:

| Event | Deskripsi | Contoh Pengg<PERSON>an |
|-------|-----------|-------------------|
| `open_url` | Buka URL tertentu | Navigasi ke halaman login |
| `navigate_to` | Alias untuk open_url | Sama dengan open_url |
| `go_back` | Kembali ke halaman sebelumnya | Undo navigasi |
| `go_forward` | Maju ke halaman berikutnya | Redo navigasi |
| `refresh_page` | Refresh halaman saat ini | Reload untuk update data |

### 🖱️ **Interaction Events** (5 jenis) 
Event untuk interaksi dengan element:

| Event | Deskripsi | Contoh <PERSON> |
|-------|-----------|-------------------|
| `click` | Klik pada element | Klik tombol, link |
| `double_click` | Double click element | Buka file, edit cell |
| `right_click` | Klik kanan (context menu) | Menu konteks |
| `hover` | Hover mouse ke element | Tooltip, dropdown |
| `drag_drop` | Drag and drop element | Reorder list, upload |

### ⌨️ **Input Events** (7 jenis)
Event untuk input data:

| Event | Deskripsi | Contoh Penggunaan |
|-------|-----------|-------------------|
| `type_text` | Ketik teks ke input field | Isi form, search |
| `clear_field` | Bersihkan field input | Reset form |
| `select_dropdown` | Pilih option dropdown | Pilih kategori |
| `check_checkbox` | Centang checkbox | Pilih opsi |
| `uncheck_checkbox` | Hapus centang checkbox | Deselect opsi |
| `select_radio` | Pilih radio button | Pilih satu opsi |
| `upload_file` | Upload file | Kirim dokumen |

### ✅ **Verification Events** (5 jenis)
Event untuk verifikasi kondisi:

| Event | Deskripsi | Contoh Penggunaan |
|-------|-----------|-------------------|
| `verify_text` | Verifikasi teks pada halaman | Cek pesan sukses |
| `verify_element_exists` | Verifikasi element ada | Cek form tersedia |
| `verify_element_visible` | Verifikasi element terlihat | Cek modal terbuka |
| `verify_url_contains` | Verifikasi URL mengandung teks | Cek halaman yang benar |
| `verify_page_title` | Verifikasi judul halaman | Konfirmasi navigasi |

### 🛠️ **Utility Events** (8 jenis)
Event untuk utilitas dan helper:

| Event | Deskripsi | Contoh Penggunaan |
|-------|-----------|-------------------|
| `wait_seconds` | Tunggu beberapa detik | Delay eksekusi |
| `wait_for_element` | Tunggu element muncul | Tunggu loading selesai |
| `scroll_to_element` | Scroll ke element | Pastikan element terlihat |
| `scroll_page` | Scroll halaman | Navigasi vertikal |
| `capture_screenshot` | Ambil screenshot | Dokumentasi |
| `execute_script` | Jalankan JavaScript | Custom logic |
| `set_variable` | Set variabel global | Simpan data |
| `get_text` | Ambil teks dari element | Extract informasi |

### 🔀 **Conditional Events** (5 jenis)
Event untuk logika kondisional:

| Event | Deskripsi | Contoh Penggunaan |
|-------|-----------|-------------------|
| `if_element_exists` | Conditional berdasarkan element | Handle popup opsional |
| `if_text_contains` | Conditional berdasarkan teks | Cek pesan error |
| `if_url_contains` | Conditional berdasarkan URL | Routing logic |
| `if_variable_equals` | Conditional berdasarkan variabel | Status checking |
| `switch_case` | Switch case untuk multiple kondisi | Multi-path logic |

---

## 🎛️ **Conditional Logic di Setiap Event**

**SEMUA EVENT** dapat memiliki conditional logic opsional:

```json
{
  "conditional": {
    "enabled": true,
    "condition": {
      "type": "verify_element_exists",
      "selector": "#popup",
      "timeout": 3000
    },
    "on_true": "continue|skip_step|jump_to_step",
    "on_false": "continue|skip_step|jump_to_step",
    "jump_target": 10
  }
}
```

### **Condition Types:**
- `verify_element_exists` - Cek element ada
- `verify_element_visible` - Cek element terlihat  
- `verify_text` - Cek teks
- `verify_url_contains` - Cek URL
- `verify_variable_equals` - Cek variabel

### **Action Types:**
- `continue` - Lanjut ke step berikutnya
- `skip_step` - Skip step ini
- `jump_to_step` - Lompat ke step tertentu

---

## 📝 **Contoh Implementasi**

### 1. **Login Flow dengan Conditional**

```json
{
  "step_id": 1,
  "name": "cek_sudah_login",
  "type": "verify_element_exists",
  "description": "Cek apakah sudah login sebelumnya",
  "parameters": {
    "selector": ".user-avatar",
    "timeout": 3000
  },
  "conditional": {
    "enabled": true,
    "condition": {
      "type": "verify_element_exists", 
      "selector": ".user-avatar"
    },
    "on_true": "jump_to_step",
    "jump_target": 10,  // Skip ke dashboard
    "on_false": "continue"  // Lanjut proses login
  }
}
```

### 2. **Form Input dengan Validation**

```json
{
  "step_id": 5,
  "name": "isi_email",
  "type": "type_text",
  "description": "Isi field email",
  "parameters": {
    "selector": "#email",
    "text": "{{user_email}}",
    "simulate_typing": true
  },
  "conditional": {
    "enabled": true,
    "condition": {
      "type": "verify_element_visible",
      "selector": "#email",
      "timeout": 5000
    },
    "on_true": "continue",
    "on_false": "skip_step"
  },
  "visual_feedback": {
    "highlight_element": true,
    "highlight_color": "#10b981"
  }
}
```

### 3. **Popup Handler dengan Multiple Actions**

```json
{
  "step_id": 8,
  "name": "handle_notification",
  "type": "if_element_exists",
  "description": "Handle popup notifikasi",
  "parameters": {
    "condition": {
      "selector": ".notification-modal",
      "timeout": 5000
    },
    "true_actions": [
      {
        "type": "get_text",
        "selector": ".notification-modal .message",
        "store_in": "notification_text"
      },
      {
        "type": "click",
        "selector": ".notification-modal .close-btn"
      }
    ],
    "false_actions": [
      {
        "type": "set_variable",
        "variable": "notification_text",
        "value": "No notification"
      }
    ]
  }
}
```

### 4. **Dynamic Navigation dengan Switch Case**

```json
{
  "step_id": 12,
  "name": "navigate_by_user_type",
  "type": "switch_case",
  "description": "Navigasi berdasarkan tipe user",
  "parameters": {
    "variable": "user_type",
    "cases": {
      "admin": [
        {
          "type": "open_url",
          "url": "/admin/dashboard"
        }
      ],
      "user": [
        {
          "type": "open_url", 
          "url": "/user/profile"
        }
      ],
      "guest": [
        {
          "type": "open_url",
          "url": "/guest/welcome"
        }
      ],
      "default": [
        {
          "type": "open_url",
          "url": "/login"
        }
      ]
    }
  }
}
```

### 5. **Screenshot dengan Conditional**

```json
{
  "step_id": 15,
  "name": "screenshot_jika_error",
  "type": "capture_screenshot",
  "description": "Ambil screenshot jika ada error",
  "parameters": {
    "filename": "error_{{timestamp}}.png",
    "quality": 95,
    "full_page": true
  },
  "conditional": {
    "enabled": true,
    "condition": {
      "type": "verify_element_exists",
      "selector": ".error-message"
    },
    "on_true": "continue",
    "on_false": "skip_step"
  }
}
```

---

## 🌍 **Global Variables**

Flow dapat menggunakan variabel global yang dapat diakses di semua step:

```json
{
  "global_variables": {
    "base_url": "https://mysite.com",
    "username": "admin",
    "password": "secret123",
    "user_type": "admin",
    "enable_screenshots": true,
    "timeout": 10000,
    "current_timestamp": "{{auto_generated}}"
  }
}
```

**Penggunaan variabel:** `{{variable_name}}`

---

## 🎨 **Visual Feedback**

Setiap event dapat memiliki visual feedback:

```json
{
  "visual_feedback": {
    "highlight_element": true,
    "highlight_color": "#3b82f6",
    "highlight_duration": 2000,
    "show_page_highlight": false,
    "click_animation": true,
    "show_typing_indicator": true
  }
}
```

---

## 🛡️ **Error Handling**

Setiap event dapat memiliki error handling:

```json
{
  "error_handling": {
    "continue_on_error": false,
    "retry_attempts": 3,
    "retry_delay": 2000,
    "on_error_action": "stop_execution|continue|retry|skip_step"
  }
}
```

---

## 🔥 **Keunggulan Sistem**

### ✅ **Fleksibilitas Maksimal**
- 31+ jenis event untuk semua kebutuhan automasi
- Conditional logic di setiap event
- Global variables dan parameter dinamis

### ✅ **Visual Feedback Real-time**
- Element highlighting dengan warna berbeda
- Animasi click dan typing
- Progress tracking visual

### ✅ **Error Recovery**
- Retry mechanism otomatis
- Alternative selector fallback
- Graceful error handling

### ✅ **Easy to Use**
- JSON-based configuration
- Intuitive event naming
- Comprehensive documentation

---

## 📚 **Template Lengkap**

```json
{
  "flow_id": "my_automation_flow",
  "name": "My Automation Flow",
  "description": "Description of what this flow does",
  "version": "1.0.0",
  "author": "Your Name",
  "flow_steps": [
    {
      "step_id": 1,
      "name": "step_name",
      "type": "event_type",
      "description": "What this step does",
      "parameters": {
        // Event-specific parameters
      },
      "visual_node": {
        "title": "🎯 Step Title",
        "subtitle": "Step description",
        "icon": "🎯",
        "color": "#3b82f6"
      },
      "conditional": {
        "enabled": true,
        "condition": {
          "type": "condition_type",
          // Condition parameters
        },
        "on_true": "continue",
        "on_false": "skip_step"
      },
      "visual_feedback": {
        "highlight_element": true,
        "highlight_color": "#3b82f6"
      },
      "error_handling": {
        "continue_on_error": false,
        "retry_attempts": 3
      }
    }
  ],
  "global_variables": {
    "var1": "value1",
    "var2": "value2"
  }
}
```

---

## 🚀 **Mulai Menggunakan**

1. **Pilih Event Type** dari 31+ yang tersedia
2. **Atur Parameters** sesuai kebutuhan  
3. **Tambahkan Conditional** jika perlu logic bercabang
4. **Set Visual Feedback** untuk monitoring
5. **Configure Error Handling** untuk reliability
6. **Test dan Deploy** flow automation Anda

---

**Sistem Enhanced Flow Events** memberikan fleksibilitas penuh untuk membuat automasi web yang kompleks, robust, dan mudah di-maintain. Dengan conditional logic di setiap event, Anda dapat membuat flow yang adaptif terhadap berbagai skenario dan kondisi halaman web. 