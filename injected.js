// Venus Auto Fill - Injected Script for Advanced DOM Manipulation

(function() {
    'use strict';

    // Prevent multiple injections
    if (window.venusAutoFillInjected) {
        return;
    }
    window.venusAutoFillInjected = true;

    class VenusAutoFillInjected {
        constructor() {
            this.init();
        }

        init() {
            console.log('Venus Auto Fill injected script loaded');
            this.setupAdvancedSelectors();
            this.setupEventHelpers();
        }

        setupAdvancedSelectors() {
            // Add advanced selector methods to window for content script access
            window.venusAutoFill = {
                findElementByText: this.findElementByText.bind(this),
                findElementByAttribute: this.findElementByAttribute.bind(this),
                findElementByPosition: this.findElementByPosition.bind(this),
                waitForElement: this.waitForElement.bind(this),
                simulateHumanClick: this.simulateHumanClick.bind(this),
                simulateHumanType: this.simulateHumanType.bind(this),
                getElementPath: this.getElementPath.bind(this),
                isElementVisible: this.isElementVisible.bind(this),
                scrollToElement: this.scrollToElement.bind(this)
            };
        }

        setupEventHelpers() {
            // Enhanced event simulation methods
            this.eventHelpers = {
                createMouseEvent: this.createMouseEvent.bind(this),
                createKeyboardEvent: this.createKeyboardEvent.bind(this),
                createInputEvent: this.createInputEvent.bind(this),
                triggerFocusEvents: this.triggerFocusEvents.bind(this)
            };
        }

        findElementByText(text, tagName = '*', exact = false) {
            const xpath = exact 
                ? `//${tagName}[text()='${text}']`
                : `//${tagName}[contains(text(),'${text}')]`;
            
            const result = document.evaluate(
                xpath,
                document,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            );
            
            return result.singleNodeValue;
        }

        findElementByAttribute(attribute, value, tagName = '*', exact = true) {
            const xpath = exact
                ? `//${tagName}[@${attribute}='${value}']`
                : `//${tagName}[contains(@${attribute},'${value}')]`;
            
            const result = document.evaluate(
                xpath,
                document,
                null,
                XPathResult.FIRST_ORDERED_NODE_TYPE,
                null
            );
            
            return result.singleNodeValue;
        }

        findElementByPosition(x, y) {
            return document.elementFromPoint(x, y);
        }

        async waitForElement(selector, timeout = 10000, interval = 100) {
            const startTime = Date.now();
            
            while (Date.now() - startTime < timeout) {
                const element = typeof selector === 'string' 
                    ? document.querySelector(selector)
                    : selector();
                
                if (element && this.isElementVisible(element)) {
                    return element;
                }
                
                await this.delay(interval);
            }
            
            throw new Error(`Element not found within ${timeout}ms: ${selector}`);
        }

        async simulateHumanClick(element, options = {}) {
            if (!element) {
                throw new Error('Element is required for click simulation');
            }

            const rect = element.getBoundingClientRect();
            const x = rect.left + (rect.width / 2) + (Math.random() - 0.5) * 10;
            const y = rect.top + (rect.height / 2) + (Math.random() - 0.5) * 10;

            // Scroll element into view if needed
            if (!this.isElementInViewport(element)) {
                await this.scrollToElement(element);
                await this.delay(300);
            }

            // Simulate mouse movement and hover
            const mouseMoveEvent = this.createMouseEvent('mousemove', x, y);
            element.dispatchEvent(mouseMoveEvent);
            await this.delay(50 + Math.random() * 100);

            const mouseOverEvent = this.createMouseEvent('mouseover', x, y);
            element.dispatchEvent(mouseOverEvent);
            await this.delay(50 + Math.random() * 100);

            // Simulate mouse down
            const mouseDownEvent = this.createMouseEvent('mousedown', x, y);
            element.dispatchEvent(mouseDownEvent);
            await this.delay(50 + Math.random() * 150);

            // Focus the element
            if (element.focus) {
                element.focus();
            }

            // Simulate mouse up and click
            const mouseUpEvent = this.createMouseEvent('mouseup', x, y);
            element.dispatchEvent(mouseUpEvent);
            
            const clickEvent = this.createMouseEvent('click', x, y);
            element.dispatchEvent(clickEvent);

            // Additional delay to simulate human behavior
            await this.delay(100 + Math.random() * 200);
        }

        async simulateHumanType(element, text, options = {}) {
            if (!element) {
                throw new Error('Element is required for typing simulation');
            }

            const { 
                delay: baseDelay = 100,
                variation = 50,
                clearFirst = true 
            } = options;

            // Focus the element
            element.focus();
            this.triggerFocusEvents(element);

            // Clear existing content if requested
            if (clearFirst) {
                element.select();
                await this.delay(100);
                
                // Simulate Ctrl+A and Delete
                const selectAllEvent = this.createKeyboardEvent('keydown', 'a', { ctrlKey: true });
                element.dispatchEvent(selectAllEvent);
                
                const deleteEvent = this.createKeyboardEvent('keydown', 'Delete');
                element.dispatchEvent(deleteEvent);
                
                element.value = '';
            }

            // Type each character with human-like timing
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                
                // Simulate keydown
                const keyDownEvent = this.createKeyboardEvent('keydown', char);
                element.dispatchEvent(keyDownEvent);
                
                // Simulate keypress
                const keyPressEvent = this.createKeyboardEvent('keypress', char);
                element.dispatchEvent(keyPressEvent);
                
                // Update value
                element.value += char;
                
                // Simulate input event
                const inputEvent = this.createInputEvent();
                element.dispatchEvent(inputEvent);
                
                // Simulate keyup
                const keyUpEvent = this.createKeyboardEvent('keyup', char);
                element.dispatchEvent(keyUpEvent);
                
                // Human-like delay with variation
                const charDelay = baseDelay + (Math.random() - 0.5) * variation;
                await this.delay(Math.max(10, charDelay));
            }

            // Trigger change event
            const changeEvent = new Event('change', { bubbles: true });
            element.dispatchEvent(changeEvent);

            // Trigger blur event
            const blurEvent = new Event('blur', { bubbles: true });
            element.dispatchEvent(blurEvent);
        }

        createMouseEvent(type, x, y, options = {}) {
            return new MouseEvent(type, {
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y,
                screenX: x + window.screenX,
                screenY: y + window.screenY,
                button: options.button || 0,
                buttons: options.buttons || 1,
                ctrlKey: options.ctrlKey || false,
                shiftKey: options.shiftKey || false,
                altKey: options.altKey || false,
                metaKey: options.metaKey || false
            });
        }

        createKeyboardEvent(type, key, options = {}) {
            return new KeyboardEvent(type, {
                bubbles: true,
                cancelable: true,
                key: key,
                code: this.getKeyCode(key),
                keyCode: this.getKeyCodeNumber(key),
                which: this.getKeyCodeNumber(key),
                ctrlKey: options.ctrlKey || false,
                shiftKey: options.shiftKey || false,
                altKey: options.altKey || false,
                metaKey: options.metaKey || false
            });
        }

        createInputEvent() {
            return new Event('input', {
                bubbles: true,
                cancelable: true
            });
        }

        triggerFocusEvents(element) {
            const focusEvent = new Event('focus', { bubbles: true });
            element.dispatchEvent(focusEvent);
            
            const focusInEvent = new Event('focusin', { bubbles: true });
            element.dispatchEvent(focusInEvent);
        }

        getKeyCode(key) {
            const keyCodes = {
                'Enter': 'Enter',
                'Tab': 'Tab',
                'Escape': 'Escape',
                'Backspace': 'Backspace',
                'Delete': 'Delete',
                'ArrowUp': 'ArrowUp',
                'ArrowDown': 'ArrowDown',
                'ArrowLeft': 'ArrowLeft',
                'ArrowRight': 'ArrowRight'
            };
            
            return keyCodes[key] || `Key${key.toUpperCase()}`;
        }

        getKeyCodeNumber(key) {
            const keyCodeNumbers = {
                'Enter': 13,
                'Tab': 9,
                'Escape': 27,
                'Backspace': 8,
                'Delete': 46,
                'ArrowUp': 38,
                'ArrowDown': 40,
                'ArrowLeft': 37,
                'ArrowRight': 39
            };
            
            if (keyCodeNumbers[key]) {
                return keyCodeNumbers[key];
            }
            
            if (key.length === 1) {
                return key.toUpperCase().charCodeAt(0);
            }
            
            return 0;
        }

        getElementPath(element) {
            const path = [];
            let current = element;
            
            while (current && current.nodeType === Node.ELEMENT_NODE) {
                let selector = current.nodeName.toLowerCase();
                
                if (current.id) {
                    selector += `#${current.id}`;
                    path.unshift(selector);
                    break;
                } else {
                    let sibling = current;
                    let nth = 1;
                    
                    while (sibling = sibling.previousElementSibling) {
                        if (sibling.nodeName.toLowerCase() === selector) {
                            nth++;
                        }
                    }
                    
                    if (nth > 1) {
                        selector += `:nth-of-type(${nth})`;
                    }
                }
                
                path.unshift(selector);
                current = current.parentNode;
            }
            
            return path.join(' > ');
        }

        isElementVisible(element) {
            if (!element) return false;
            
            const style = window.getComputedStyle(element);
            return style.display !== 'none' && 
                   style.visibility !== 'hidden' && 
                   style.opacity !== '0' &&
                   element.offsetWidth > 0 && 
                   element.offsetHeight > 0;
        }

        isElementInViewport(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }

        async scrollToElement(element, options = {}) {
            const { 
                behavior = 'smooth',
                block = 'center',
                inline = 'center'
            } = options;
            
            element.scrollIntoView({
                behavior,
                block,
                inline
            });
            
            // Wait for scroll to complete
            await this.delay(500);
        }

        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }

    // Initialize the injected script
    new VenusAutoFillInjected();

})();
