/**
 * Auto Form Fill Pro - Crypto Utilities
 * Provides secure encryption/decryption for sensitive data storage
 */

export class CryptoUtils {
  /**
   * Generate a random key for encryption
   */
  static async generateKey() {
    return await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Get or create encryption key from storage
   */
  static async getEncryptionKey() {
    try {
      // Try to load existing key
      const stored = await chrome.storage.local.get(['encryptionKey']);
      
      if (stored.encryptionKey) {
        // Import the stored key
        return await crypto.subtle.importKey(
          'jwk',
          stored.encryptionKey,
          { name: 'AES-GCM' },
          true,
          ['encrypt', 'decrypt']
        );
      } else {
        // Generate new key and store it
        const key = await this.generateKey();
        const exportedKey = await crypto.subtle.exportKey('jwk', key);
        
        await chrome.storage.local.set({ encryptionKey: exportedKey });
        
        return key;
      }
    } catch (error) {
      console.error('Failed to get encryption key:', error);
      throw new Error('Encryption key generation failed');
    }
  }

  /**
   * Encrypt data using AES-GCM
   */
  static async encrypt(data) {
    try {
      if (typeof data !== 'string') {
        throw new Error('Data must be a string');
      }

      const key = await this.getEncryptionKey();
      const encoder = new TextEncoder();
      const encodedData = encoder.encode(data);
      
      // Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      // Encrypt the data
      const encryptedData = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        key,
        encodedData
      );
      
      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encryptedData.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encryptedData), iv.length);
      
      // Convert to base64 for storage
      return this.arrayBufferToBase64(combined);
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt data using AES-GCM
   */
  static async decrypt(encryptedData) {
    try {
      if (typeof encryptedData !== 'string') {
        throw new Error('Encrypted data must be a string');
      }

      const key = await this.getEncryptionKey();
      
      // Convert from base64
      const combined = this.base64ToArrayBuffer(encryptedData);
      
      // Extract IV and encrypted data
      const iv = combined.slice(0, 12);
      const encrypted = combined.slice(12);
      
      // Decrypt the data
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        key,
        encrypted
      );
      
      // Convert back to string
      const decoder = new TextDecoder();
      return decoder.decode(decryptedData);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Hash data using SHA-256
   */
  static async hash(data) {
    try {
      const encoder = new TextEncoder();
      const encodedData = encoder.encode(data);
      
      const hashBuffer = await crypto.subtle.digest('SHA-256', encodedData);
      
      return this.arrayBufferToHex(hashBuffer);
    } catch (error) {
      console.error('Hashing failed:', error);
      throw new Error('Failed to hash data');
    }
  }

  /**
   * Generate secure random string
   */
  static generateRandomString(length = 32) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const randomBytes = crypto.getRandomValues(new Uint8Array(length));
    
    return Array.from(randomBytes)
      .map(byte => charset[byte % charset.length])
      .join('');
  }

  /**
   * Generate UUID v4
   */
  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Secure compare two strings (constant time)
   */
  static secureCompare(a, b) {
    if (a.length !== b.length) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }
    
    return result === 0;
  }

  /**
   * Derive key from password using PBKDF2
   */
  static async deriveKeyFromPassword(password, salt = null) {
    try {
      if (!salt) {
        salt = crypto.getRandomValues(new Uint8Array(16));
      }
      
      const encoder = new TextEncoder();
      const passwordBuffer = encoder.encode(password);
      
      // Import password as key material
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        passwordBuffer,
        { name: 'PBKDF2' },
        false,
        ['deriveBits', 'deriveKey']
      );
      
      // Derive key
      const key = await crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: salt,
          iterations: 100000,
          hash: 'SHA-256'
        },
        keyMaterial,
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );
      
      return { key, salt };
    } catch (error) {
      console.error('Key derivation failed:', error);
      throw new Error('Failed to derive key from password');
    }
  }

  /**
   * Encrypt with password
   */
  static async encryptWithPassword(data, password) {
    try {
      const { key, salt } = await this.deriveKeyFromPassword(password);
      
      const encoder = new TextEncoder();
      const encodedData = encoder.encode(data);
      
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      const encryptedData = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        key,
        encodedData
      );
      
      // Combine salt, IV and encrypted data
      const combined = new Uint8Array(salt.length + iv.length + encryptedData.byteLength);
      combined.set(salt);
      combined.set(iv, salt.length);
      combined.set(new Uint8Array(encryptedData), salt.length + iv.length);
      
      return this.arrayBufferToBase64(combined);
    } catch (error) {
      console.error('Password encryption failed:', error);
      throw new Error('Failed to encrypt with password');
    }
  }

  /**
   * Decrypt with password
   */
  static async decryptWithPassword(encryptedData, password) {
    try {
      const combined = this.base64ToArrayBuffer(encryptedData);
      
      // Extract salt, IV and encrypted data
      const salt = combined.slice(0, 16);
      const iv = combined.slice(16, 28);
      const encrypted = combined.slice(28);
      
      const { key } = await this.deriveKeyFromPassword(password, salt);
      
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        key,
        encrypted
      );
      
      const decoder = new TextDecoder();
      return decoder.decode(decryptedData);
    } catch (error) {
      console.error('Password decryption failed:', error);
      throw new Error('Failed to decrypt with password');
    }
  }

  /**
   * Convert ArrayBuffer to Base64
   */
  static arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    const binary = Array.from(bytes).map(byte => String.fromCharCode(byte)).join('');
    return btoa(binary);
  }

  /**
   * Convert Base64 to ArrayBuffer
   */
  static base64ToArrayBuffer(base64) {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * Convert ArrayBuffer to Hex
   */
  static arrayBufferToHex(buffer) {
    const bytes = new Uint8Array(buffer);
    return Array.from(bytes)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Convert Hex to ArrayBuffer
   */
  static hexToArrayBuffer(hex) {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
    }
    return bytes;
  }

  /**
   * Clear encryption key (for logout/reset)
   */
  static async clearEncryptionKey() {
    try {
      await chrome.storage.local.remove(['encryptionKey']);
    } catch (error) {
      console.error('Failed to clear encryption key:', error);
    }
  }

  /**
   * Validate encrypted data format
   */
  static isValidEncryptedData(data) {
    try {
      if (typeof data !== 'string') {
        return false;
      }
      
      // Try to decode base64
      const decoded = this.base64ToArrayBuffer(data);
      
      // Check minimum length (salt + IV + some encrypted data)
      return decoded.length >= 28; // 16 (salt) + 12 (IV) + minimum encrypted data
    } catch (error) {
      return false;
    }
  }
} 