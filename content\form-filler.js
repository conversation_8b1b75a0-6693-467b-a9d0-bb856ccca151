/**
 * Auto Form Fill Pro - Form Filler
 * Handles the actual filling of form fields with data from API
 */

class FormFiller {
  constructor() {
    this.logger = console; // Will be replaced with proper logger
    this.fillQueue = [];
    this.isProcessing = false;
    
    // Field type handlers
    this.fieldHandlers = {
      text: this.fillTextInput.bind(this),
      email: this.fillTextInput.bind(this),
      password: this.fillTextInput.bind(this),
      number: this.fillNumberInput.bind(this),
      tel: this.fillTextInput.bind(this),
      url: this.fillTextInput.bind(this),
      search: this.fillTextInput.bind(this),
      textarea: this.fillTextarea.bind(this),
      select: this.fillSelect.bind(this),
      'select-one': this.fillSelect.bind(this),
      'select-multiple': this.fillSelectMultiple.bind(this),
      checkbox: this.fillCheckbox.bind(this),
      radio: this.fillRadio.bind(this),
      date: this.fillDateInput.bind(this),
      time: this.fillTimeInput.bind(this),
      'datetime-local': this.fillDateTimeInput.bind(this),
      range: this.fillRangeInput.bind(this),
      color: this.fillColorInput.bind(this)
    };
  }

  /**
   * Fill all forms on the page with provided data
   */
  async fillForms(formData, options = {}) {
    try {
      if (!formData || typeof formData !== 'object') {
        throw new Error('Invalid form data provided');
      }

      const {
        fields = {},
        mappings = {},
        confirmBeforeFill = false,
        highlightFields = true,
        animationSpeed = 'medium'
      } = { ...formData, ...options };

      this.logger.info('Starting form fill process:', {
        fieldsCount: Object.keys(fields).length,
        mappingsCount: Object.keys(mappings).length
      });

      // Get all fillable forms and fields
      const forms = this.detectFillableForms();
      if (forms.length === 0) {
        throw new Error('No fillable forms found on page');
      }

      // Build fill queue
      this.buildFillQueue(forms, fields, mappings);

      if (this.fillQueue.length === 0) {
        throw new Error('No matching fields found to fill');
      }

      // Confirm with user if required
      if (confirmBeforeFill) {
        const confirmed = await this.confirmFillAction(this.fillQueue);
        if (!confirmed) {
          return { success: false, message: 'User cancelled form fill' };
        }
      }

      // Execute fill process
      const result = await this.executeFillProcess({
        highlightFields,
        animationSpeed
      });

      return result;
    } catch (error) {
      this.logger.error('Form fill error:', error);
      throw error;
    }
  }

  /**
   * Detect all fillable forms on the page
   */
  detectFillableForms() {
    const forms = [];
    
    // Get forms using FormDetector if available
    if (window.FormDetector) {
      const detector = new window.FormDetector();
      detector.detectExistingForms();
      return detector.getForms();
    }

    // Fallback: manual form detection
    const formElements = document.querySelectorAll('form');
    formElements.forEach(formElement => {
      const fields = this.getFormFields(formElement);
      if (fields.length > 0) {
        forms.push({
          element: formElement,
          fields: new Map(fields.map(field => [field.id, field]))
        });
      }
    });

    // Also detect formless fields
    const formlessFields = this.getFormlessFields();
    if (formlessFields.length > 0) {
      forms.push({
        element: null,
        fields: new Map(formlessFields.map(field => [field.id, field])),
        isFormless: true
      });
    }

    return forms;
  }

  /**
   * Get all fillable fields in a form
   */
  getFormFields(form) {
    const selector = 'input:not([type="submit"]):not([type="button"]):not([type="reset"]):not([type="image"]):not([type="hidden"]), select, textarea';
    const fieldElements = form.querySelectorAll(selector);
    
    return Array.from(fieldElements)
      .filter(element => this.isFieldFillable(element))
      .map(element => this.createFieldData(element));
  }

  /**
   * Get formless fields (not inside any form)
   */
  getFormlessFields() {
    const selector = 'input:not([type="submit"]):not([type="button"]):not([type="reset"]):not([type="image"]):not([type="hidden"]), select, textarea';
    const allFields = document.querySelectorAll(selector);
    
    return Array.from(allFields)
      .filter(element => !element.closest('form'))
      .filter(element => this.isFieldFillable(element))
      .map(element => this.createFieldData(element));
  }

  /**
   * Check if a field is fillable
   */
  isFieldFillable(element) {
    // Skip if disabled or readonly
    if (element.disabled || element.readOnly) {
      return false;
    }

    // Skip if not visible
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden' || 
        style.opacity === '0' || element.offsetWidth === 0 || element.offsetHeight === 0) {
      return false;
    }

    return true;
  }

  /**
   * Create field data object
   */
  createFieldData(element) {
    return {
      id: element.id || element.name || this.generateFieldId(element),
      element: element,
      type: this.getFieldType(element),
      name: element.name || '',
      placeholder: element.placeholder || '',
      semanticType: this.detectSemanticType(element),
      value: element.value || ''
    };
  }

  /**
   * Generate unique field ID
   */
  generateFieldId(element) {
    const type = element.type || element.tagName.toLowerCase();
    const position = Array.from(document.querySelectorAll(element.tagName.toLowerCase())).indexOf(element);
    return `${type}_field_${position}`;
  }

  /**
   * Get field type
   */
  getFieldType(element) {
    if (element.tagName.toLowerCase() === 'select') {
      return element.multiple ? 'select-multiple' : 'select';
    }
    return element.type || 'text';
  }

  /**
   * Detect semantic type of field
   */
  detectSemanticType(element) {
    const text = [
      element.name || '',
      element.id || '',
      element.placeholder || '',
      element.className || ''
    ].join(' ').toLowerCase();

    const patterns = {
      name: /name|fullname|full.name|first.name|last.name/i,
      email: /email|e.mail|mail/i,
      phone: /phone|tel|mobile|cell/i,
      address: /address|street|addr/i,
      city: /city|town/i,
      state: /state|province|region/i,
      zip: /zip|postal|postcode/i,
      country: /country|nation/i,
      company: /company|organization|org|employer/i,
      job: /job|title|position|role/i,
      website: /website|url|site/i,
      age: /age|birth|dob/i
    };

    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(text)) {
        return type;
      }
    }

    return 'text';
  }

  /**
   * Build queue of fields to fill
   */
  buildFillQueue(forms, fields, mappings) {
    this.fillQueue = [];

    forms.forEach(form => {
      form.fields.forEach(field => {
        const matchedData = this.findMatchingData(field, fields, mappings);
        if (matchedData) {
          this.fillQueue.push({
            field: field,
            data: matchedData,
            form: form
          });
        }
      });
    });

    // Sort by priority (required fields first, then by field type)
    this.fillQueue.sort((a, b) => {
      const aRequired = a.field.element.required ? 1 : 0;
      const bRequired = b.field.element.required ? 1 : 0;
      return bRequired - aRequired;
    });
  }

  /**
   * Find matching data for a field
   */
  findMatchingData(field, fields, mappings) {
    // Check direct mappings first
    if (mappings[field.id] && fields[mappings[field.id]]) {
      return fields[mappings[field.id]];
    }

    // Check by field name
    if (field.name && fields[field.name]) {
      return fields[field.name];
    }

    // Check by semantic type
    if (field.semanticType && fields[field.semanticType]) {
      return fields[field.semanticType];
    }

    // Check by ID
    if (field.id && fields[field.id]) {
      return fields[field.id];
    }

    return null;
  }

  /**
   * Execute the fill process
   */
  async executeFillProcess(options = {}) {
    this.isProcessing = true;
    const {
      highlightFields = true,
      animationSpeed = 'medium'
    } = options;

    const results = {
      success: true,
      filled: 0,
      failed: 0,
      errors: []
    };

    const delay = this.getAnimationDelay(animationSpeed);

    for (const item of this.fillQueue) {
      try {
        // Highlight field if enabled
        if (highlightFields && window.FieldHighlighter) {
          window.FieldHighlighter.highlightField(item.field.element, 'filling');
        }

        // Fill the field
        await this.fillField(item.field, item.data);
        
        // Update highlight to success
        if (highlightFields && window.FieldHighlighter) {
          window.FieldHighlighter.highlightField(item.field.element, 'success');
        }

        results.filled++;
        
        // Add delay for animation
        if (delay > 0) {
          await this.delay(delay);
        }
      } catch (error) {
        this.logger.error('Failed to fill field:', item.field.id, error);
        
        // Update highlight to error
        if (highlightFields && window.FieldHighlighter) {
          window.FieldHighlighter.highlightField(item.field.element, 'error');
        }

        results.failed++;
        results.errors.push({
          field: item.field.id,
          error: error.message
        });
      }
    }

    this.isProcessing = false;

    // Clear highlights after a delay
    if (highlightFields && window.FieldHighlighter) {
      setTimeout(() => {
        window.FieldHighlighter.clearAllHighlights();
      }, 2000);
    }

    return results;
  }

  /**
   * Fill an individual field
   */
  async fillField(field, data) {
    const handler = this.fieldHandlers[field.type];
    if (!handler) {
      throw new Error(`No handler for field type: ${field.type}`);
    }

    const value = this.extractValue(data);
    if (value === null || value === undefined) {
      throw new Error('No value to fill');
    }

    return await handler(field.element, value, data);
  }

  /**
   * Extract value from data object
   */
  extractValue(data) {
    if (typeof data === 'string') {
      return data;
    }
    
    if (typeof data === 'object' && data !== null) {
      return data.value || data.text || data.default || null;
    }

    return data;
  }

  /**
   * Fill text input field
   */
  async fillTextInput(element, value) {
    if (typeof value !== 'string') {
      value = String(value);
    }

    // Focus the element
    element.focus();
    
    // Clear existing value
    element.value = '';
    
    // Trigger input event for frameworks
    element.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Set new value
    element.value = value;
    
    // Trigger change event
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Blur the element
    element.blur();
  }

  /**
   * Fill textarea field
   */
  async fillTextarea(element, value) {
    return this.fillTextInput(element, value);
  }

  /**
   * Fill number input field
   */
  async fillNumberInput(element, value) {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      throw new Error('Invalid number value');
    }
    
    return this.fillTextInput(element, numValue.toString());
  }

  /**
   * Fill select field
   */
  async fillSelect(element, value) {
    const options = Array.from(element.options);
    
    // Find matching option by value or text
    const matchingOption = options.find(option => 
      option.value === value || 
      option.text === value ||
      option.value.toLowerCase() === String(value).toLowerCase() ||
      option.text.toLowerCase() === String(value).toLowerCase()
    );

    if (!matchingOption) {
      throw new Error(`No matching option found for value: ${value}`);
    }

    element.value = matchingOption.value;
    element.dispatchEvent(new Event('change', { bubbles: true }));
  }

  /**
   * Fill multiple select field
   */
  async fillSelectMultiple(element, value) {
    let values = Array.isArray(value) ? value : [value];
    const options = Array.from(element.options);
    
    // Clear all selections
    options.forEach(option => option.selected = false);
    
    // Select matching options
    values.forEach(val => {
      const matchingOption = options.find(option => 
        option.value === val || 
        option.text === val ||
        option.value.toLowerCase() === String(val).toLowerCase() ||
        option.text.toLowerCase() === String(val).toLowerCase()
      );
      
      if (matchingOption) {
        matchingOption.selected = true;
      }
    });

    element.dispatchEvent(new Event('change', { bubbles: true }));
  }

  /**
   * Fill checkbox field
   */
  async fillCheckbox(element, value) {
    const shouldCheck = this.parseBoolean(value);
    element.checked = shouldCheck;
    element.dispatchEvent(new Event('change', { bubbles: true }));
  }

  /**
   * Fill radio button field
   */
  async fillRadio(element, value) {
    const shouldCheck = element.value === String(value) || this.parseBoolean(value);
    element.checked = shouldCheck;
    
    if (shouldCheck) {
      element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  /**
   * Fill date input field
   */
  async fillDateInput(element, value) {
    const dateValue = this.formatDate(value);
    return this.fillTextInput(element, dateValue);
  }

  /**
   * Fill time input field
   */
  async fillTimeInput(element, value) {
    const timeValue = this.formatTime(value);
    return this.fillTextInput(element, timeValue);
  }

  /**
   * Fill datetime-local input field
   */
  async fillDateTimeInput(element, value) {
    const dateTimeValue = this.formatDateTime(value);
    return this.fillTextInput(element, dateTimeValue);
  }

  /**
   * Fill range input field
   */
  async fillRangeInput(element, value) {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      throw new Error('Invalid range value');
    }
    
    const min = parseFloat(element.min) || 0;
    const max = parseFloat(element.max) || 100;
    const clampedValue = Math.max(min, Math.min(max, numValue));
    
    return this.fillTextInput(element, clampedValue.toString());
  }

  /**
   * Fill color input field
   */
  async fillColorInput(element, value) {
    const colorValue = this.formatColor(value);
    return this.fillTextInput(element, colorValue);
  }

  /**
   * Parse boolean value
   */
  parseBoolean(value) {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true' || value === '1' || value.toLowerCase() === 'yes';
    }
    return !!value;
  }

  /**
   * Format date value
   */
  formatDate(value) {
    try {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date');
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      throw new Error(`Invalid date format: ${value}`);
    }
  }

  /**
   * Format time value
   */
  formatTime(value) {
    try {
      if (typeof value === 'string' && /^\d{2}:\d{2}(:\d{2})?$/.test(value)) {
        return value.substring(0, 5); // HH:MM format
      }
      
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid time');
      }
      
      return date.toTimeString().substring(0, 5);
    } catch (error) {
      throw new Error(`Invalid time format: ${value}`);
    }
  }

  /**
   * Format datetime value
   */
  formatDateTime(value) {
    try {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid datetime');
      }
      
      return date.toISOString().slice(0, 16);
    } catch (error) {
      throw new Error(`Invalid datetime format: ${value}`);
    }
  }

  /**
   * Format color value
   */
  formatColor(value) {
    if (typeof value === 'string' && value.match(/^#[0-9a-fA-F]{6}$/)) {
      return value;
    }
    
    // Convert RGB to hex if needed
    if (typeof value === 'string' && value.match(/^rgb\(/)) {
      const matches = value.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
      if (matches) {
        const r = parseInt(matches[1]).toString(16).padStart(2, '0');
        const g = parseInt(matches[2]).toString(16).padStart(2, '0');
        const b = parseInt(matches[3]).toString(16).padStart(2, '0');
        return `#${r}${g}${b}`;
      }
    }
    
    throw new Error(`Invalid color format: ${value}`);
  }

  /**
   * Get animation delay based on speed setting
   */
  getAnimationDelay(speed) {
    const delays = {
      fast: 50,
      medium: 150,
      slow: 300
    };
    return delays[speed] || delays.medium;
  }

  /**
   * Confirm fill action with user
   */
  async confirmFillAction(queue) {
    const message = `Fill ${queue.length} fields on this page?`;
    return window.confirm(message);
  }

  /**
   * Delay utility
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get fill queue for debugging
   */
  getFillQueue() {
    return this.fillQueue;
  }

  /**
   * Clear fill queue
   */
  clearFillQueue() {
    this.fillQueue = [];
  }
}

// Export for use in other modules
window.FormFiller = FormFiller; 