{"sampleFlows": {"basicAttendanceEntry": {"name": "Basic Attendance Entry", "description": "Simple flow for entering employee attendance data into a web form", "events": [{"id": 1, "type": "click", "selector": "#employee-search-btn", "description": "Click employee search button"}, {"id": 2, "type": "input", "selector": "#employee-id-field", "value": "{employeeId}", "description": "Enter employee ID"}, {"id": 3, "type": "click", "selector": "#search-submit", "description": "Submit employee search"}, {"id": 4, "type": "wait", "duration": 2000, "description": "Wait for search results to load"}, {"id": 5, "type": "input", "selector": "#attendance-date", "value": "{today}", "description": "Enter attendance date"}, {"id": 6, "type": "input", "selector": "#regular-hours", "value": "{day1.regular}", "description": "Enter regular working hours"}, {"id": 7, "type": "input", "selector": "#overtime-hours", "value": "{day1.overtime}", "description": "Enter overtime hours"}, {"id": 8, "type": "click", "selector": "#save-attendance", "description": "Save attendance record"}, {"id": 9, "type": "wait", "duration": 1000, "description": "Wait for save confirmation"}]}, "enhancedAttendanceWithConditionalLogic": {"name": "Enhanced Attendance Entry with Conditional Logic", "description": "Advanced attendance entry with conditional processing and error handling", "events": [{"id": 1, "type": "open_to", "url": "http://millwarep3.rebinmas.com:8003/attendance", "waitForLoad": true, "description": "Navigate to attendance system"}, {"id": 2, "type": "wait_for_element", "selector": "#login-form", "timeout": 10000, "expectVisible": true, "condition": {"type": "url_contains", "value": "login"}, "description": "Wait for login form if on login page"}, {"id": 3, "type": "if_then_else", "condition": {"type": "element_exists", "selector": "#username", "visible": true}, "thenEvents": [{"type": "form_fill", "fields": [{"selector": "#username", "value": "adm075", "required": true}, {"selector": "#password", "value": "adm075", "required": true}], "fieldDelay": 200}, {"type": "click", "selector": "#login-button"}, {"type": "wait_for_element", "selector": "#dashboard", "timeout": 15000}], "description": "Login if required"}, {"id": 4, "type": "screenshot", "name": "after_login", "description": "Take screenshot after login"}, {"id": 5, "type": "loop", "iterations": 5, "iterationDelay": 1000, "continueOnError": true, "events": [{"type": "variable_set", "variableName": "currentEmployee", "dataMapping": "employees[extracted.loopIndex].employee_id", "description": "Set current employee ID"}, {"type": "hover", "selector": "#employee-dropdown", "duration": 500, "description": "Hover over employee dropdown"}, {"type": "select_option", "selector": "#employee-select", "value": "{currentEmployee}", "description": "Select employee from dropdown"}, {"type": "data_extract_multiple", "extractions": [{"name": "employeeName", "selector": "#selected-employee-name", "attribute": "text", "transform": {"type": "trim"}, "storeGlobally": true}, {"name": "currentDate", "selector": "#current-date", "attribute": "value", "storeGlobally": true}], "description": "Extract employee details"}, {"type": "if_then_else", "condition": {"type": "element_text_contains", "selector": "#status-message", "value": "No attendance record"}, "thenEvents": [{"type": "form_fill", "fields": [{"selector": "#regular-hours", "dataMapping": "employees[extracted.loopIndex].regular_hours"}, {"selector": "#overtime-hours", "dataMapping": "employees[extracted.loopIndex].overtime_hours"}]}, {"type": "click", "selector": "#save-attendance"}, {"type": "alert_handle", "action": "accept", "timeout": 3000}], "elseEvents": [{"type": "variable_set", "variableName": "skipR<PERSON>on", "value": "Record already exists"}], "description": "Process attendance if no existing record"}], "description": "Process multiple employees"}, {"id": 6, "type": "screenshot", "name": "completion_summary", "description": "Take final screenshot"}]}, "monthlyAttendanceEntry": {"name": "Monthly Attendance Entry", "description": "Complete flow for entering a full month of attendance data", "events": [{"id": 1, "type": "click", "selector": "#new-employee-btn", "description": "Click new employee button"}, {"id": 2, "type": "input", "selector": "#employee-id", "value": "{employeeId}", "description": "Enter employee ID"}, {"id": 3, "type": "input", "selector": "#employee-name", "value": "{employeeName}", "description": "Enter employee name"}, {"id": 4, "type": "click", "selector": "#month-selector", "description": "Open month selector"}, {"id": 5, "type": "select_option", "selector": "#month-dropdown", "text": "May 2025", "description": "Select May 2025"}, {"id": 6, "type": "input", "selector": "#day-1-regular", "value": "{day1.regular}", "description": "Enter day 1 regular hours"}, {"id": 7, "type": "input", "selector": "#day-1-overtime", "value": "{day1.overtime}", "description": "Enter day 1 overtime hours"}, {"id": 8, "type": "input", "selector": "#day-2-regular", "value": "{day2.regular}", "description": "Enter day 2 regular hours"}, {"id": 9, "type": "input", "selector": "#day-2-overtime", "value": "{day2.overtime}", "description": "Enter day 2 overtime hours"}, {"id": 10, "type": "scroll_to", "selector": "#day-15-section", "block": "center", "smooth": true, "description": "Scroll to mid-month section"}, {"id": 11, "type": "input", "selector": "#day-15-regular", "value": "{day15.regular}", "description": "Enter day 15 regular hours"}, {"id": 12, "type": "input", "selector": "#day-15-overtime", "value": "{day15.overtime}", "description": "Enter day 15 overtime hours"}, {"id": 13, "type": "scroll_to", "selector": "#day-31-section", "block": "center", "smooth": true, "description": "Scroll to end of month section"}, {"id": 14, "type": "input", "selector": "#day-31-regular", "value": "{day31.regular}", "description": "Enter day 31 regular hours"}, {"id": 15, "type": "input", "selector": "#day-31-overtime", "value": "{day31.overtime}", "description": "Enter day 31 overtime hours"}, {"id": 16, "type": "click", "selector": "#calculate-totals", "description": "Calculate monthly totals"}, {"id": 17, "type": "wait", "duration": 2000, "description": "Wait for calculations to complete"}, {"id": 18, "type": "click", "selector": "#submit-monthly-data", "description": "Submit monthly attendance data"}, {"id": 19, "type": "wait", "duration": 3000, "description": "Wait for submission confirmation"}]}, "intelligentFormProcessing": {"name": "Intelligent Form Processing with Error Recovery", "description": "Advanced form processing with intelligent error handling and recovery", "events": [{"id": 1, "type": "open_to", "url": "{targetUrl}", "newTab": false, "waitForLoad": true, "description": "Open target form URL"}, {"id": 2, "type": "wait_for_element", "selector": "form", "timeout": 15000, "expectVisible": true, "description": "Wait for form to load"}, {"id": 3, "type": "variable_set", "variableName": "formStartTime", "expression": "Date.now()", "description": "Record form start time"}, {"id": 4, "type": "loop", "iterations": 10, "iterationDelay": 2000, "continueOnError": true, "events": [{"type": "if_then_else", "condition": {"type": "element_exists", "selector": ".error-message", "visible": true}, "thenEvents": [{"type": "screenshot", "name": "error_detected", "description": "Capture error state"}, {"type": "data_extract_multiple", "extractions": [{"name": "errorMessage", "selector": ".error-message", "attribute": "text", "transform": {"type": "trim"}, "storeGlobally": true}]}, {"type": "alert_handle", "action": "accept", "timeout": 2000}, {"type": "wait", "duration": 1000}], "elseEvents": [{"type": "form_fill", "fields": [{"selector": "#employee-id", "dataMapping": "employees[extracted.loopIndex].employee_id", "required": true}, {"selector": "#hours-worked", "dataMapping": "employees[extracted.loopIndex].total_hours", "required": true}], "fieldDelay": 300}, {"type": "click", "selector": "#submit-btn"}, {"type": "wait_for_element", "selector": ".success-message", "timeout": 5000, "expectVisible": true}], "description": "Handle errors or process form"}], "description": "Process records with error handling"}, {"id": 5, "type": "variable_set", "variableName": "formEndTime", "expression": "Date.now()", "description": "Record form end time"}, {"id": 6, "type": "variable_set", "variableName": "processingDuration", "expression": "extracted.formEndTime - extracted.formStartTime", "description": "Calculate processing duration"}, {"id": 7, "type": "screenshot", "name": "final_result", "description": "Take final screenshot"}]}, "multiTabWorkflow": {"name": "Multi-Tab Workflow Management", "description": "Complex workflow spanning multiple browser tabs", "events": [{"id": 1, "type": "open_to", "url": "http://system1.example.com/login", "newTab": false, "description": "Open first system"}, {"id": 2, "type": "form_fill", "fields": [{"selector": "#username", "value": "admin"}, {"selector": "#password", "value": "password"}], "description": "Login to first system"}, {"id": 3, "type": "tab_switch", "url": "http://system2.example.com/data", "description": "Open second system in new tab"}, {"id": 4, "type": "wait_for_element", "selector": "#data-table", "timeout": 10000, "description": "Wait for data table to load"}, {"id": 5, "type": "data_extract_multiple", "extractions": [{"name": "extractedData", "selector": "#data-table tbody tr", "attribute": "text", "transform": {"type": "trim"}, "storeGlobally": true}], "description": "Extract data from second system"}, {"id": 6, "type": "tab_switch", "tabIndex": 0, "description": "Switch back to first tab"}, {"id": 7, "type": "input", "selector": "#import-data", "value": "{extractedData}", "description": "Import extracted data"}]}, "loginAndNavigate": {"name": "Login and Navigate", "description": "Login to the system and navigate to attendance entry page", "events": [{"id": 1, "type": "input", "selector": "#username", "value": "{configUsername}", "description": "Enter username from configuration"}, {"id": 2, "type": "input", "selector": "#password", "value": "{configPassword}", "description": "Enter password from configuration"}, {"id": 3, "type": "click", "selector": "#login-button", "description": "Click login button"}, {"id": 4, "type": "wait", "duration": 3000, "description": "Wait for login to complete"}, {"id": 5, "type": "click", "selector": "#attendance-menu", "description": "Click attendance menu"}, {"id": 6, "type": "click", "selector": "#data-entry-submenu", "description": "Click data entry submenu"}, {"id": 7, "type": "wait", "duration": 2000, "description": "Wait for page to load"}]}, "advancedFormFilling": {"name": "Advanced Form Filling", "description": "Complex form filling with multiple field types and validation", "events": [{"id": 1, "type": "click", "selector": "button[data-action='new-record']", "description": "Click new record button using attribute selector"}, {"id": 2, "type": "input", "selector": "input[placeholder='Employee ID']", "value": "{employeeId}", "description": "Find input by placeholder text"}, {"id": 3, "type": "click", "selector": "//button[contains(text(), 'Search')]", "description": "Find button by text content using XPath"}, {"id": 4, "type": "wait", "duration": 1500, "description": "Wait for search results"}, {"id": 5, "type": "select", "selector": "#department-select", "value": "Human Resources", "description": "Select department from dropdown"}, {"id": 6, "type": "input", "selector": "#start-date", "value": "2025-05-01", "description": "Enter start date"}, {"id": 7, "type": "input", "selector": "#end-date", "value": "2025-05-31", "description": "Enter end date"}, {"id": 8, "type": "click", "selector": "#generate-calendar", "description": "Generate attendance calendar"}, {"id": 9, "type": "wait", "duration": 3000, "description": "Wait for calendar generation"}, {"id": 10, "type": "scroll", "y": 500, "description": "Scroll down to calendar section"}]}}, "placeholderReference": {"employeeData": {"{employeeId}": "Employee ID from the data source", "{employeeName}": "Employee name from the data source", "{recordIndex}": "Current record index (0-based)"}, "attendanceData": {"{day1.regular}": "Regular hours for day 1", "{day1.overtime}": "Overtime hours for day 1", "{day2.regular}": "Regular hours for day 2", "{day2.overtime}": "Overtime hours for day 2", "{dayN.regular}": "Regular hours for day N (where N is 1-31)", "{dayN.overtime}": "Overtime hours for day N (where N is 1-31)"}, "dateTime": {"{today}": "Current date in YYYY-MM-DD format", "{timestamp}": "Current timestamp in milliseconds", "{year}": "Current year", "{month}": "Current month (1-12)", "{day}": "Current day of month"}, "configuration": {"{configUsername}": "Username from extension configuration", "{configPassword}": "Password from extension configuration", "{configTargetUrl}": "Target URL from extension configuration"}}, "selectorExamples": {"cssSelectors": {"id": "#element-id", "class": ".class-name", "attribute": "input[name='fieldname']", "multiple_attributes": "button[type='submit'][class='primary']", "descendant": "#parent .child", "child": "#parent > .direct-child", "nth_child": "tr:nth-child(2)", "pseudo_selectors": "input:focus, button:hover"}, "xpathSelectors": {"text_content": "//button[text()='Submit']", "contains_text": "//div[contains(text(), 'Employee')]", "attribute": "//input[@placeholder='Enter ID']", "position": "//tr[position()=2]", "complex": "//div[@class='form-group']//input[@type='text']"}}, "bestPractices": {"eventOrdering": ["Always start with navigation events (clicks to reach the form)", "Add wait events after navigation to ensure page loads", "Group related input events together", "Add validation waits after form submissions", "End with confirmation or navigation events"], "selectorTips": ["Use ID selectors when available (most reliable)", "Avoid selectors based on dynamic classes or generated IDs", "Test selectors using browser developer tools", "Use the 'Check' button to validate element selection", "Consider using attribute selectors for stable identification"], "timingTips": ["Add wait events after page navigation", "Use longer waits for AJAX requests or dynamic content", "Add small delays between rapid input events", "Wait for animations or transitions to complete"]}}