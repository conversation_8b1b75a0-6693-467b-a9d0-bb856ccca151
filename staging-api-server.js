/**
 * Simple Staging API Server untuk Venus-Millware AutoFill Testing
 * Run with: node staging-api-server.js
 * Accessible at: http://localhost:5173/api
 * 
 * Developer: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)
 */

const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 5173;

// Enhanced CORS configuration for Chrome extensions
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps, curl, etc.)
        if (!origin) return callback(null, true);
        
        // Allow Chrome extension origins
        if (origin.startsWith('chrome-extension://')) {
            return callback(null, true);
        }
        
        // Allow localhost and local IP addresses
        if (origin.includes('localhost') || 
            origin.includes('127.0.0.1') || 
            origin.includes('**********') ||
            origin.includes('file://')) {
            return callback(null, true);
        }
        
        console.log('🔍 CORS Request from origin:', origin);
        callback(null, true); // Allow all origins for development
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'User-Agent', 'X-Requested-With']
};

// Middleware
app.use(cors(corsOptions));
app.use(express.json());

// Add request logging middleware
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    console.log(`📝 [${timestamp}] ${req.method} ${req.originalUrl} - Origin: ${req.headers.origin || 'None'}`);
    console.log(`📋 Headers:`, {
        'user-agent': req.headers['user-agent'],
        'content-type': req.headers['content-type'],
        'origin': req.headers.origin,
        'referer': req.headers.referer
    });
    next();
});

// Sample timesheet data
const sampleTimesheetData = [
    {
        employee_id: 'EMP001',
        employee_name: 'John Doe',
        date: '2024-01-15',
        check_in: '08:00',
        check_out: '17:30',
        regular_hours: 8.0,
        overtime_hours: 1.5,
        task_code: 'PROJ001',
        machine_code: 'MAC001',
        expense_code: 'EXP001'
    },
    {
        employee_id: 'EMP002',
        employee_name: 'Jane Smith',
        date: '2024-01-15',
        check_in: '09:00',
        check_out: '18:00',
        regular_hours: 8.0,
        overtime_hours: 1.0,
        task_code: 'PROJ002',
        machine_code: 'MAC002',
        expense_code: 'EXP002'
    },
    {
        employee_id: 'EMP003',
        employee_name: 'Mike Johnson',
        date: '2024-01-15',
        check_in: '07:30',
        check_out: '16:30',
        regular_hours: 8.0,
        overtime_hours: 1.0,
        task_code: 'PROJ001',
        machine_code: 'MAC003',
        expense_code: 'EXP001'
    },
    {
        employee_id: 'EMP004',
        employee_name: 'Sarah Wilson',
        date: '2024-01-15',
        check_in: '08:30',
        check_out: '17:00',
        regular_hours: 8.0,
        overtime_hours: 0.5,
        task_code: 'PROJ003',
        machine_code: 'MAC004',
        expense_code: 'EXP003'
    },
    {
        employee_id: 'EMP005',
        employee_name: 'David Brown',
        date: '2024-01-15',
        check_in: '08:00',
        check_out: '18:30',
        regular_hours: 8.0,
        overtime_hours: 2.5,
        task_code: 'PROJ002',
        machine_code: 'MAC005',
        expense_code: 'EXP002'
    }
];

// Routes

// Health check endpoint
app.get('/api/health', (req, res) => {
    console.log('🔍 Health check requested from:', req.headers.origin || 'Direct');
    res.json({
        success: true,
        data: {
            name: 'Venus-Millware AutoFill Staging API',
            status: 'healthy',
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            server: {
                host: '**********',
                port: PORT,
                cors: 'enabled'
            },
            endpoints: [
                'GET /api/health',
                'GET /api/staging/data',
                'POST /api/automation/results',
                'POST /api/auth/login'
            ]
        }
    });
});

// Get staging timesheet data
app.get('/api/staging/data', (req, res) => {
    console.log('📊 Staging data requested from:', req.headers.origin || 'Direct');
    console.log('🔍 User-Agent:', req.headers['user-agent']);
    
    // Add CORS headers explicitly
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, User-Agent');
    
    // Simulate some processing delay
    setTimeout(() => {
        const response = {
            success: true,
            data: sampleTimesheetData,
            total_records: sampleTimesheetData.length,
            returned_records: sampleTimesheetData.length,
            metadata: {
                totalRecords: sampleTimesheetData.length,
                lastUpdated: new Date().toISOString(),
                source: 'staging_database',
                apiVersion: '1.0.0',
                server: {
                    host: '**********',
                    port: PORT,
                    requestTime: new Date().toISOString()
                }
            }
        };
        
        console.log('✅ Sending response with', sampleTimesheetData.length, 'records');
        res.json(response);
    }, 500);
});

// Handle OPTIONS requests for CORS preflight
app.options('/api/*', (req, res) => {
    console.log('🔧 CORS Preflight request for:', req.originalUrl);
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, User-Agent, X-Requested-With');
    res.header('Access-Control-Max-Age', '86400'); // 24 hours
    res.status(200).send();
});

// Alternative endpoint that returns data directly as array
app.get('/api/staging/data/raw', (req, res) => {
    console.log('📊 Raw staging data requested from:', req.headers.origin || 'Direct');
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.json(sampleTimesheetData);
});

// Submit automation results
app.post('/api/automation/results', (req, res) => {
    console.log('📝 Automation results submitted from:', req.headers.origin || 'Direct');
    console.log('📋 Result data:', req.body);
    
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.json({
        success: true,
        message: 'Results submitted successfully',
        submissionId: 'SUB_' + Date.now(),
        data: {
            processed: true,
            timestamp: new Date().toISOString(),
            recordsProcessed: req.body.processedRecords || 0
        }
    });
});

// Authentication endpoint (optional)
app.post('/api/auth/login', (req, res) => {
    console.log('🔐 Authentication requested for:', req.body.username);
    
    const { username, password, apiKey } = req.body;
    
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    
    // Simple auth validation
    if ((username && password) || apiKey) {
        res.json({
            success: true,
            token: 'jwt_token_' + Date.now(),
            expiresIn: 3600,
            user: {
                username: username || 'api_user',
                role: 'automation',
                permissions: ['read_timesheet', 'submit_results']
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: 'Invalid credentials'
        });
    }
});

// Get available endpoints
app.get('/api/endpoints', (req, res) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.json({
        success: true,
        data: [
            {
                method: 'GET',
                path: '/api/health',
                description: 'Health check endpoint'
            },
            {
                method: 'GET',
                path: '/api/staging/data',
                description: 'Get timesheet staging data'
            },
            {
                method: 'POST',
                path: '/api/automation/results',
                description: 'Submit automation results'
            },
            {
                method: 'POST',
                path: '/api/auth/login',
                description: 'Authenticate user'
            }
        ]
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('❌ Server error:', err);
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: err.message,
        timestamp: new Date().toISOString()
    });
});

// 404 handler
app.use('*', (req, res) => {
    console.log(`❓ Unknown endpoint requested: ${req.method} ${req.originalUrl} from: ${req.headers.origin || 'Direct'}`);
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        message: `${req.method} ${req.originalUrl} is not a valid endpoint`,
        availableEndpoints: [
            'GET /api/health',
            'GET /api/staging/data',
            'POST /api/automation/results',
            'POST /api/auth/login'
        ],
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, () => {
    console.log('\n' + '='.repeat(60));
    console.log('🚀 Venus-Millware AutoFill Staging API Server');
    console.log('='.repeat(60));
    console.log(`📡 Server running on: http://localhost:${PORT}`);
    console.log(`🌐 API Base URL: http://localhost:${PORT}/api`);
    console.log(`🔗 Health Check: http://localhost:${PORT}/api/health`);
    console.log(`📊 Staging Data: http://localhost:${PORT}/api/staging/data`);
    console.log('='.repeat(60));
    console.log('🎯 Siap untuk Venus-Millware AutoFill!');
    console.log('📝 Developer: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)');
    console.log('='.repeat(60) + '\n');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down staging API server...');
    process.exit(0);
});

module.exports = app; 