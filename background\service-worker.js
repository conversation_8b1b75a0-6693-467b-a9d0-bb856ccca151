/**
 * Auto Form Fill Pro - Background Service Worker
 * Handles API communication, storage, and coordinates extension functionality
 */

import { ApiService } from './api-service.js';
import { StorageManager } from './storage-manager.js';
import { Logger } from '../utils/logger.js';

class AutoFillServiceWorker {
  constructor() {
    this.apiService = new ApiService();
    this.storageManager = new StorageManager();
    this.logger = new Logger('ServiceWorker');
    this.isEnabled = true;
    
    this.init();
  }

  /**
   * Initialize the service worker
   */
  async init() {
    try {
      // Load initial configuration
      await this.loadConfiguration();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Initialize API service
      await this.apiService.initialize();
      
      this.logger.info('Service worker initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize service worker:', error);
    }
  }

  /**
   * Set up all event listeners
   */
  setupEventListeners() {
    // Extension installation/update
    chrome.runtime.onInstalled.addListener(this.onInstalled.bind(this));
    
    // Commands (keyboard shortcuts)
    chrome.commands.onCommand.addListener(this.onCommand.bind(this));
    
    // Messages from content scripts and popup
    chrome.runtime.onMessage.addListener(this.onMessage.bind(this));
    
    // Tab updates for context awareness
    chrome.tabs.onUpdated.addListener(this.onTabUpdated.bind(this));
    
    // Storage changes
    chrome.storage.onChanged.addListener(this.onStorageChanged.bind(this));
  }

  /**
   * Handle extension installation
   */
  async onInstalled(details) {
    try {
      if (details.reason === 'install') {
        // Set default configuration on first install
        await this.storageManager.setDefaultConfiguration();
        this.logger.info('Extension installed with default configuration');
      } else if (details.reason === 'update') {
        // Handle updates
        await this.handleUpdate(details.previousVersion);
        this.logger.info(`Extension updated from ${details.previousVersion}`);
      }
    } catch (error) {
      this.logger.error('Installation handler error:', error);
    }
  }

  /**
   * Handle keyboard commands
   */
  async onCommand(command) {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        this.logger.warn('No active tab found for command:', command);
        return;
      }

      switch (command) {
        case 'trigger-autofill':
          await this.triggerAutoFill(tab.id);
          break;
        case 'toggle-extension':
          await this.toggleExtension();
          break;
        default:
          this.logger.warn('Unknown command:', command);
      }
    } catch (error) {
      this.logger.error('Command handler error:', error);
    }
  }

  /**
   * Handle messages from other parts of the extension
   */
  async onMessage(message, sender, sendResponse) {
    try {
      const { action, data } = message;
      
      switch (action) {
        case 'GET_FORM_DATA':
          const formData = await this.apiService.getFormData(data);
          sendResponse({ success: true, data: formData });
          break;
          
        case 'TRIGGER_AUTOFILL':
          await this.triggerAutoFill(sender.tab.id, data);
          sendResponse({ success: true });
          break;
          
        case 'GET_CONFIGURATION':
          const config = await this.storageManager.getConfiguration();
          sendResponse({ success: true, data: config });
          break;
          
        case 'UPDATE_CONFIGURATION':
          await this.storageManager.updateConfiguration(data);
          sendResponse({ success: true });
          break;
          
        case 'TEST_API_CONNECTION':
          const testResult = await this.apiService.testConnection();
          sendResponse({ success: true, data: testResult });
          break;
          
        case 'GET_EXTENSION_STATUS':
          sendResponse({ 
            success: true, 
            data: { 
              isEnabled: this.isEnabled,
              apiStatus: this.apiService.getStatus()
            }
          });
          break;
          
        case 'TOGGLE_EXTENSION':
          await this.toggleExtension();
          sendResponse({ success: true, data: { isEnabled: this.isEnabled } });
          break;
          
        default:
          this.logger.warn('Unknown message action:', action);
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      this.logger.error('Message handler error:', error);
      sendResponse({ success: false, error: error.message });
    }
    
    return true; // Keep message channel open for async response
  }

  /**
   * Handle tab updates
   */
  async onTabUpdated(tabId, changeInfo, tab) {
    try {
      if (changeInfo.status === 'complete' && this.isEnabled) {
        // Check if this domain is whitelisted
        const config = await this.storageManager.getConfiguration();
        const isAllowed = this.isDomainAllowed(tab.url, config);
        
        if (isAllowed) {
          // Inject content scripts if not already present
          await this.ensureContentScriptsInjected(tabId);
          
          // Notify content script that page is ready
          chrome.tabs.sendMessage(tabId, { 
            action: 'PAGE_READY',
            config: config 
          }).catch(() => {
            // Ignore errors if content script isn't ready yet
          });
        }
      }
    } catch (error) {
      this.logger.error('Tab update handler error:', error);
    }
  }

  /**
   * Handle storage changes
   */
  async onStorageChanged(changes, namespace) {
    try {
      if (namespace === 'sync' || namespace === 'local') {
        // Reload configuration if it changed
        if (changes.configuration) {
          await this.loadConfiguration();
          
          // Notify all tabs about configuration change
          const tabs = await chrome.tabs.query({});
          tabs.forEach(tab => {
            chrome.tabs.sendMessage(tab.id, {
              action: 'CONFIGURATION_UPDATED',
              data: changes.configuration.newValue
            }).catch(() => {
              // Ignore errors for tabs without content scripts
            });
          });
        }
      }
    } catch (error) {
      this.logger.error('Storage change handler error:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  async loadConfiguration() {
    try {
      const config = await this.storageManager.getConfiguration();
      this.isEnabled = config.isEnabled !== false;
      
      // Update API service configuration
      this.apiService.updateConfiguration(config.api);
      
      this.logger.info('Configuration loaded:', config);
    } catch (error) {
      this.logger.error('Failed to load configuration:', error);
    }
  }

  /**
   * Trigger auto-fill on specified tab
   */
  async triggerAutoFill(tabId, options = {}) {
    try {
      if (!this.isEnabled) {
        throw new Error('Extension is disabled');
      }

      // Get form data from API
      const formData = await this.apiService.getFormData(options);
      
      if (!formData) {
        throw new Error('No form data available');
      }

      // Send auto-fill command to content script
      await chrome.tabs.sendMessage(tabId, {
        action: 'EXECUTE_AUTOFILL',
        data: formData
      });
      
      this.logger.info('Auto-fill triggered successfully on tab:', tabId);
    } catch (error) {
      this.logger.error('Auto-fill trigger error:', error);
      throw error;
    }
  }

  /**
   * Toggle extension on/off
   */
  async toggleExtension() {
    try {
      this.isEnabled = !this.isEnabled;
      
      // Save new state
      await this.storageManager.updateConfiguration({ isEnabled: this.isEnabled });
      
      // Update icon
      await this.updateIcon();
      
      // Notify all tabs
      const tabs = await chrome.tabs.query({});
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          action: 'EXTENSION_TOGGLED',
          data: { isEnabled: this.isEnabled }
        }).catch(() => {
          // Ignore errors for tabs without content scripts
        });
      });
      
      this.logger.info('Extension toggled:', this.isEnabled ? 'enabled' : 'disabled');
    } catch (error) {
      this.logger.error('Toggle extension error:', error);
    }
  }

  /**
   * Update extension icon based on state
   */
  async updateIcon() {
    try {
      const iconPath = this.isEnabled ? 'icons/icon-32.png' : 'icons/icon-32-disabled.png';
      await chrome.action.setIcon({ path: iconPath });
    } catch (error) {
      this.logger.error('Icon update error:', error);
    }
  }

  /**
   * Check if domain is allowed based on configuration
   */
  isDomainAllowed(url, config) {
    try {
      if (!url || !config) return false;
      
      const domain = new URL(url).hostname;
      const { whitelist = [], blacklist = [] } = config.domains || {};
      
      // Check blacklist first
      if (blacklist.some(blocked => domain.includes(blocked))) {
        return false;
      }
      
      // If whitelist exists and is not empty, domain must be whitelisted
      if (whitelist.length > 0) {
        return whitelist.some(allowed => domain.includes(allowed));
      }
      
      // Default: allow all domains if no restrictions
      return true;
    } catch (error) {
      this.logger.error('Domain check error:', error);
      return false;
    }
  }

  /**
   * Ensure content scripts are injected
   */
  async ensureContentScriptsInjected(tabId) {
    try {
      // Test if content script is already present
      const response = await chrome.tabs.sendMessage(tabId, { action: 'PING' });
      if (response?.success) {
        return; // Content script already present
      }
    } catch (error) {
      // Content script not present, inject it
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: [
            'content/form-detector.js',
            'content/form-filler.js',
            'content/field-highlighter.js',
            'content/content-main.js'
          ]
        });
        
        await chrome.scripting.insertCSS({
          target: { tabId },
          files: ['content/styles.css']
        });
        
        this.logger.info('Content scripts injected into tab:', tabId);
      } catch (injectError) {
        this.logger.error('Failed to inject content scripts:', injectError);
      }
    }
  }

  /**
   * Handle extension updates
   */
  async handleUpdate(previousVersion) {
    try {
      // Perform any necessary migration based on version
      const config = await this.storageManager.getConfiguration();
      
      // Add any new default settings
      const updatedConfig = await this.storageManager.migrateConfiguration(config, previousVersion);
      
      if (updatedConfig !== config) {
        await this.storageManager.updateConfiguration(updatedConfig);
      }
      
      this.logger.info('Extension updated successfully');
    } catch (error) {
      this.logger.error('Update handler error:', error);
    }
  }
}

// Initialize the service worker
new AutoFillServiceWorker(); 