<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venus Auto Fill - Extension Integration Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        
        .extension-check {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .status {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        
        .results {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .integration-steps {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .integration-steps h3 {
            color: #1976d2;
            margin: 0 0 15px 0;
        }
        
        .integration-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .integration-steps li {
            margin: 10px 0;
            line-height: 1.5;
        }
        
        .success-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            background: #4CAF50;
            border-radius: 50%;
            color: white;
            text-align: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .error-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            background: #f44336;
            border-radius: 50%;
            color: white;
            text-align: center;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Venus Auto Fill</h1>
            <p>Chrome Extension Integration Test</p>
        </div>

        <div class="extension-check">
            <h3>📋 Extension Status Check</h3>
            <p>This page will verify that your Chrome extension is properly configured with the new Google Apps Script API.</p>
            <button class="btn btn-success" onclick="checkExtensionStatus()">🔍 Check Extension Status</button>
        </div>

        <div class="integration-steps">
            <h3>🚀 Integration Steps Completed</h3>
            <ol>
                <li><span class="success-indicator">✓</span><strong>Phase 1:</strong> Created standalone HTML test file (<code>test-new-api.html</code>)</li>
                <li><span class="success-indicator">✓</span><strong>Phase 2:</strong> Updated Chrome extension popup.js with new API URL</li>
                <li><span class="success-indicator">✓</span><strong>Phase 3:</strong> Updated API service module with new endpoint handling</li>
                <li><span class="success-indicator">✓</span><strong>Phase 4:</strong> Enhanced data processing for employee timesheet format</li>
                <li><span id="extensionIndicator" class="error-indicator">?</span><strong>Phase 5:</strong> Extension integration verification (testing now)</li>
            </ol>
        </div>

        <div id="statusDisplay"></div>
        <div id="resultsDisplay" class="results" style="display: none;"></div>

        <div style="background: #e8f5e8; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #2e7d32; margin: 0 0 15px 0;">✅ New API Configuration</h3>
            <p><strong>NEW Google Apps Script URL:</strong></p>
            <code style="display: block; background: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all; font-family: monospace; font-size: 12px;">
                https://script.google.com/macros/s/AKfycbyZoY78RbqqX_YyxSdQrVZzGHU80Go5syIjDKZpMYAXwMkdfTino4mrQqNKK8kOyBttIA/exec
            </code>
            <p><strong>Target Sheet:</strong> <code>monthlyGridData_May_2025</code></p>
            <p><strong>Expected Response Format:</strong></p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li><code>success</code>: Boolean indicating API call success</li>
                <li><code>data</code>: Array of employee records with daily attendance data</li>
                <li><code>headers</code>: Column headers for the timesheet data</li>
                <li><code>metadata</code>: Information about the source spreadsheet</li>
            </ul>
        </div>
    </div>

    <script>
        async function checkExtensionStatus() {
            showStatus('🔄 Checking Chrome extension status...', 'info');
            
            const results = {
                extensionAvailable: false,
                apiServiceAvailable: false,
                configurationLoaded: false,
                connectionTest: null,
                dataFetchTest: null,
                timestamp: new Date().toISOString()
            };

            try {
                // Check if extension APIs are available
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    results.extensionAvailable = true;
                    console.log('✅ Chrome extension APIs available');
                } else {
                    throw new Error('Chrome extension APIs not available - this page must be opened from within the extension context');
                }

                // Check if API service is available
                if (typeof VenusApiService !== 'undefined') {
                    results.apiServiceAvailable = true;
                    console.log('✅ Venus API Service available');
                    
                    // Create API service instance
                    const apiService = new VenusApiService();
                    const serviceStatus = apiService.getStatus();
                    console.log('📊 API Service status:', serviceStatus);
                    
                    // Test configuration loading
                    try {
                        const config = await apiService.getConfiguration();
                        results.configurationLoaded = true;
                        results.configuration = config;
                        console.log('✅ Configuration loaded:', config);
                    } catch (error) {
                        console.error('❌ Configuration loading failed:', error);
                        results.configurationError = error.message;
                    }
                    
                    // Test connection
                    try {
                        showStatus('🔄 Testing connection to new Google Apps Script...', 'info');
                        const connectionResult = await apiService.testConnection();
                        results.connectionTest = connectionResult;
                        console.log('✅ Connection test result:', connectionResult);
                    } catch (error) {
                        console.error('❌ Connection test failed:', error);
                        results.connectionTest = { success: false, error: error.message };
                    }
                    
                    // Test data fetching
                    try {
                        showStatus('🔄 Testing employee data fetch...', 'info');
                        const dataResult = await apiService.fetchData();
                        results.dataFetchTest = dataResult;
                        console.log('✅ Data fetch test result:', dataResult);
                    } catch (error) {
                        console.error('❌ Data fetch test failed:', error);
                        results.dataFetchTest = { success: false, error: error.message };
                    }
                    
                } else {
                    results.apiServiceError = 'VenusApiService not found - ensure api-service.js is loaded';
                }

                // Determine overall status
                const allTestsPassed = results.extensionAvailable && 
                                     results.apiServiceAvailable && 
                                     results.configurationLoaded &&
                                     results.connectionTest?.success === true &&
                                     results.dataFetchTest?.success === true;

                if (allTestsPassed) {
                    showStatus('🎉 <strong>Integration Successful!</strong><br>All tests passed. Your Chrome extension is properly configured with the new Google Apps Script API.', 'success');
                    document.getElementById('extensionIndicator').className = 'success-indicator';
                    document.getElementById('extensionIndicator').textContent = '✓';
                } else {
                    let errorDetails = '';
                    if (!results.extensionAvailable) errorDetails += 'Extension APIs not available. ';
                    if (!results.apiServiceAvailable) errorDetails += 'API Service not loaded. ';
                    if (!results.configurationLoaded) errorDetails += 'Configuration loading failed. ';
                    if (results.connectionTest?.success !== true) errorDetails += 'Connection test failed. ';
                    if (results.dataFetchTest?.success !== true) errorDetails += 'Data fetch test failed. ';
                    
                    showStatus(`⚠️ <strong>Integration Issues Detected</strong><br>${errorDetails}`, 'error');
                }

                showResults(results);

            } catch (error) {
                console.error('❌ Extension status check failed:', error);
                showStatus(`❌ <strong>Extension Status Check Failed</strong><br>Error: ${error.message}`, 'error');
                results.overallError = error.message;
                showResults(results);
            }
        }

        function showStatus(message, type) {
            const element = document.getElementById('statusDisplay');
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showResults(data) {
            const element = document.getElementById('resultsDisplay');
            element.innerHTML = `<h4>🔍 Detailed Test Results</h4><pre>${JSON.stringify(data, null, 2)}</pre>`;
            element.style.display = 'block';
        }

        // Auto-check on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Extension Integration Test Page Loaded');
            setTimeout(() => {
                checkExtensionStatus();
            }, 1000);
        });
    </script>
    
    <!-- Load API service for testing -->
    <script src="scripts/api-service.js"></script>
</body>
</html> 