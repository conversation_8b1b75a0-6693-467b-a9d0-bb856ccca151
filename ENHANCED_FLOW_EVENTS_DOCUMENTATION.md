# 🚀 Enhanced Flow Events Documentation

## 📋 Overview

Enhanced Flow Definition mendukung berbagai jenis event untuk otomatisasi web yang lebih kompleks dan fleksibel. Setiap event dapat memiliki conditional logic opsional untuk mengatur alur eksekusi.

---

## 🎯 Struktur Event

Setiap event memiliki struktur dasar:

```json
{
  "step_id": 1,
  "name": "nama_event",
  "type": "jenis_event",
  "description": "Deskripsi event",
  "parameters": {
    // Parameter spesifik untuk jenis event
  },
  "visual_node": {
    "title": "🎯 Judul Visual",
    "subtitle": "Subtitle event",
    "icon": "🎯",
    "color": "#3b82f6"
  },
  "conditional": {
    "enabled": true/false,
    "condition": {
      "type": "jenis_kondisi",
      // Parameter kondisi
    },
    "on_true": "continue|skip_step|jump_to_step",
    "on_false": "continue|skip_step|jump_to_step",
    "jump_target": 5  // Nomor step tujuan jika jump
  },
  "visual_feedback": {
    // Pengaturan visual feedback
  },
  "error_handling": {
    // Pengaturan error handling
  }
}
```

---

## 🌐 Navigation Events

### 1. `open_url`
Membuka URL tertentu di tab/window

```json
{
  "type": "open_url",
  "parameters": {
    "url": "https://example.com",
    "new_tab": false,
    "wait_for_load": true,
    "timeout": 30000
  }
}
```

### 2. `navigate_to` 
Navigasi ke halaman (alias untuk open_url)

### 3. `go_back`
Kembali ke halaman sebelumnya

```json
{
  "type": "go_back",
  "parameters": {
    "wait_after": 2000
  }
}
```

### 4. `go_forward`
Maju ke halaman berikutnya

### 5. `refresh_page`
Refresh halaman saat ini

```json
{
  "type": "refresh_page",
  "parameters": {
    "wait_for_load": true,
    "timeout": 30000
  }
}
```

---

## 🖱️ Interaction Events

### 1. `click`
Klik pada element

```json
{
  "type": "click",
  "parameters": {
    "selector": "#button",
    "selector_alternatives": [".btn", "button"],
    "click_type": "left|right|middle",
    "wait_after_click": 2000,
    "scroll_to_element": true
  }
}
```

### 2. `double_click`
Double click pada element

```json
{
  "type": "double_click",
  "parameters": {
    "selector": "#element",
    "delay_between_clicks": 100,
    "wait_after_click": 2000
  }
}
```

### 3. `right_click`
Klik kanan (context menu)

### 4. `hover`
Hover mouse ke element

```json
{
  "type": "hover",
  "parameters": {
    "selector": "#element",
    "duration": 1000
  }
}
```

### 5. `drag_drop`
Drag and drop element

```json
{
  "type": "drag_drop",
  "parameters": {
    "source_selector": "#draggable",
    "target_selector": "#dropzone",
    "offset_x": 0,
    "offset_y": 0
  }
}
```

---

## ⌨️ Input Events

### 1. `type_text`
Ketik teks ke element input

```json
{
  "type": "type_text",
  "parameters": {
    "selector": "#input",
    "text": "Teks yang akan diketik",
    "clear_first": true,
    "simulate_typing": true,
    "typing_speed": 100,
    "mask_in_logs": false,
    "trigger_events": ["input", "change"]
  }
}
```

### 2. `clear_field`
Bersihkan field input

```json
{
  "type": "clear_field",
  "parameters": {
    "selector": "#input",
    "force_clear": true
  }
}
```

### 3. `select_dropdown`
Pilih option dari dropdown

```json
{
  "type": "select_dropdown",
  "parameters": {
    "selector": "#select",
    "option_value": "value1",
    "option_text": "Text Option",
    "option_index": 2
  }
}
```

### 4. `check_checkbox`
Centang checkbox

```json
{
  "type": "check_checkbox",
  "parameters": {
    "selector": "#checkbox",
    "force_check": true
  }
}
```

### 5. `uncheck_checkbox`
Hapus centang checkbox

### 6. `select_radio`
Pilih radio button

```json
{
  "type": "select_radio",
  "parameters": {
    "selector": "input[name='radio_group'][value='option1']"
  }
}
```

### 7. `upload_file`
Upload file

```json
{
  "type": "upload_file",
  "parameters": {
    "selector": "input[type='file']",
    "file_path": "/path/to/file.jpg",
    "wait_after_upload": 3000
  }
}
```

---

## ✅ Verification Events

### 1. `verify_text`
Verifikasi teks pada halaman

```json
{
  "type": "verify_text",
  "parameters": {
    "text": "Expected text",
    "selector": "#element",
    "exact_match": false,
    "case_sensitive": false,
    "timeout": 10000
  }
}
```

### 2. `verify_element_exists`
Verifikasi element ada

```json
{
  "type": "verify_element_exists",
  "parameters": {
    "selector": "#element",
    "timeout": 10000,
    "must_be_visible": true
  }
}
```

### 3. `verify_element_visible`
Verifikasi element terlihat

### 4. `verify_url_contains`
Verifikasi URL mengandung teks tertentu

```json
{
  "type": "verify_url_contains",
  "parameters": {
    "expected_url_part": "/dashboard",
    "timeout": 5000,
    "case_sensitive": false
  }
}
```

### 5. `verify_page_title`
Verifikasi judul halaman

```json
{
  "type": "verify_page_title",
  "parameters": {
    "expected_title": "Dashboard",
    "partial_match": true,
    "timeout": 5000
  }
}
```

---

## 🛠️ Utility Events

### 1. `wait_seconds`
Tunggu beberapa detik

```json
{
  "type": "wait_seconds",
  "parameters": {
    "duration": 5,
    "show_countdown": true
  }
}
```

### 2. `wait_for_element`
Tunggu hingga element muncul

```json
{
  "type": "wait_for_element",
  "parameters": {
    "selector": "#element",
    "selectors": ["#elem1", "#elem2"],  // Multiple selectors
    "timeout": 10000,
    "visible": true,
    "interactable": false,
    "any_selector": false  // true = tunggu salah satu, false = tunggu semua
  }
}
```

### 3. `scroll_to_element`
Scroll ke element tertentu

```json
{
  "type": "scroll_to_element",
  "parameters": {
    "selector": "#element",
    "behavior": "smooth|auto",
    "block": "start|center|end|nearest",
    "inline": "start|center|end|nearest"
  }
}
```

### 4. `scroll_page`
Scroll halaman

```json
{
  "type": "scroll_page",
  "parameters": {
    "direction": "up|down|left|right|top|bottom",
    "amount": 500,  // pixel atau percentage
    "smooth": true,
    "duration": 1000
  }
}
```

### 5. `capture_screenshot`
Ambil screenshot

```json
{
  "type": "capture_screenshot",
  "parameters": {
    "filename": "screenshot_{{timestamp}}.png",
    "quality": 90,
    "full_page": true,
    "element_selector": "#specific-element"  // Screenshot element tertentu
  }
}
```

### 6. `execute_script`
Jalankan JavaScript

```json
{
  "type": "execute_script",
  "parameters": {
    "script": "console.log('Hello World'); return document.title;",
    "wait_for_result": true,
    "timeout": 5000,
    "store_result_in": "script_result"
  }
}
```

### 7. `set_variable`
Set variabel global

```json
{
  "type": "set_variable",
  "parameters": {
    "variable": "my_var",
    "value": "Hello World",
    "type": "string|number|boolean"
  }
}
```

### 8. `get_text`
Ambil teks dari element

```json
{
  "type": "get_text",
  "parameters": {
    "selector": "#element",
    "store_in": "element_text",
    "trim": true
  }
}
```

---

## 🔀 Conditional Events

### 1. `if_element_exists`
Conditional berdasarkan keberadaan element

```json
{
  "type": "if_element_exists",
  "parameters": {
    "condition": {
      "selector": "#element",
      "timeout": 5000,
      "visible": true
    },
    "true_actions": [
      {
        "type": "click",
        "selector": "#element"
      }
    ],
    "false_actions": [
      {
        "type": "set_variable",
        "variable": "element_found",
        "value": false
      }
    ]
  }
}
```

### 2. `if_text_contains`
Conditional berdasarkan teks

```json
{
  "type": "if_text_contains",
  "parameters": {
    "selector": "#element",
    "text": "Expected text",
    "case_sensitive": false,
    "true_actions": [...],
    "false_actions": [...]
  }
}
```

### 3. `if_url_contains`
Conditional berdasarkan URL

### 4. `if_variable_equals`
Conditional berdasarkan variabel

```json
{
  "type": "if_variable_equals",
  "parameters": {
    "variable": "my_var",
    "value": "expected_value",
    "true_actions": [...],
    "false_actions": [...]
  }
}
```

### 5. `switch_case`
Switch case berdasarkan variabel

```json
{
  "type": "switch_case",
  "parameters": {
    "variable": "status",
    "cases": {
      "success": [
        {"type": "click", "selector": "#continue"}
      ],
      "error": [
        {"type": "click", "selector": "#retry"}
      ],
      "default": [
        {"type": "wait_seconds", "duration": 2}
      ]
    }
  }
}
```

---

## 🎛️ Conditional Logic

Setiap event dapat memiliki conditional logic opsional:

### Format Conditional

```json
{
  "conditional": {
    "enabled": true,
    "condition": {
      "type": "verify_element_exists|verify_text|verify_url_contains|verify_variable_equals",
      "selector": "#element",  // untuk element-based conditions
      "text": "expected",       // untuk text-based conditions
      "value": "expected",     // untuk url/variable conditions
      "variable": "var_name",  // untuk variable conditions
      "timeout": 5000,
      "negate": false          // untuk membalik kondisi
    },
    "on_true": "continue|skip_step|jump_to_step",
    "on_false": "continue|skip_step|jump_to_step",
    "jump_target": 10  // step ID tujuan untuk jump
  }
}
```

### Condition Types

1. **`verify_element_exists`** - Cek apakah element ada
2. **`verify_element_visible`** - Cek apakah element terlihat
3. **`verify_text`** - Cek teks pada element/halaman
4. **`verify_url_contains`** - Cek URL mengandung teks
5. **`verify_variable_equals`** - Cek nilai variabel

### Action Types

1. **`continue`** - Lanjut ke step berikutnya
2. **`skip_step`** - Skip step ini, lanjut ke step setelahnya
3. **`jump_to_step`** - Lompat ke step tertentu (butuh `jump_target`)

---

## 🎨 Visual Feedback

Setiap event dapat memiliki visual feedback:

```json
{
  "visual_feedback": {
    "highlight_element": true,
    "highlight_color": "#3b82f6",
    "highlight_duration": 2000,
    "show_page_highlight": false,
    "click_animation": true,
    "show_typing_indicator": false
  }
}
```

---

## 🛡️ Error Handling

Setiap event dapat memiliki error handling:

```json
{
  "error_handling": {
    "continue_on_error": false,
    "retry_attempts": 3,
    "retry_delay": 2000,
    "on_error_action": "stop_execution|continue|retry|skip_step"
  }
}
```

---

## 🌍 Global Variables

Flow dapat menggunakan global variables:

```json
{
  "global_variables": {
    "username": "admin",
    "password": "secret",
    "base_url": "https://example.com",
    "timeout": 10000,
    "enable_screenshots": false
  }
}
```

Variables dapat digunakan dengan format `{{variable_name}}` dalam parameters.

---

## 📝 Contoh Penggunaan

### Login Flow dengan Conditional

```json
{
  "step_id": 1,
  "name": "check_already_logged_in",
  "type": "verify_element_exists",
  "description": "Cek apakah sudah login",
  "parameters": {
    "selector": ".user-profile",
    "timeout": 3000
  },
  "conditional": {
    "enabled": true,
    "condition": {
      "type": "verify_element_exists",
      "selector": ".user-profile",
      "timeout": 3000
    },
    "on_true": "jump_to_step",
    "jump_target": 10,  // Skip ke dashboard
    "on_false": "continue"  // Lanjut login
  }
}
```

### Form Filling dengan Validation

```json
{
  "step_id": 5,
  "name": "fill_username",
  "type": "type_text",
  "parameters": {
    "selector": "#username",
    "text": "{{username}}",
    "simulate_typing": true
  },
  "conditional": {
    "enabled": true,
    "condition": {
      "type": "verify_element_visible",
      "selector": "#username",
      "timeout": 5000
    },
    "on_true": "continue",
    "on_false": "skip_step"
  }
}
```

---

Dokumentasi ini memberikan panduan lengkap untuk menggunakan semua event types yang tersedia dalam Enhanced Flow Definition. Setiap event dapat dikombinasikan dengan conditional logic untuk membuat automation flow yang powerful dan fleksibel. 