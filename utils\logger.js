/**
 * Auto Form Fill Pro - Logger Utility
 * Provides structured logging with different levels and contexts
 */

export class Logger {
  constructor(context = 'App') {
    this.context = context;
    this.levels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    
    // Default log level (can be configured)
    this.currentLevel = this.levels.INFO;
    
    // Load log level from storage if available
    this.loadLogLevel();
  }

  /**
   * Load log level from Chrome storage
   */
  async loadLogLevel() {
    try {
      const stored = await chrome.storage.local.get(['logLevel']);
      if (stored.logLevel !== undefined) {
        this.currentLevel = stored.logLevel;
      }
    } catch (error) {
      // Ignore errors when loading log level
    }
  }

  /**
   * Set log level and persist to storage
   */
  async setLogLevel(level) {
    try {
      this.currentLevel = level;
      await chrome.storage.local.set({ logLevel: level });
    } catch (error) {
      console.warn('Failed to save log level:', error);
    }
  }

  /**
   * Log error messages
   */
  error(message, ...args) {
    if (this.currentLevel >= this.levels.ERROR) {
      this.log('ERROR', message, ...args);
    }
  }

  /**
   * Log warning messages
   */
  warn(message, ...args) {
    if (this.currentLevel >= this.levels.WARN) {
      this.log('WARN', message, ...args);
    }
  }

  /**
   * Log info messages
   */
  info(message, ...args) {
    if (this.currentLevel >= this.levels.INFO) {
      this.log('INFO', message, ...args);
    }
  }

  /**
   * Log debug messages
   */
  debug(message, ...args) {
    if (this.currentLevel >= this.levels.DEBUG) {
      this.log('DEBUG', message, ...args);
    }
  }

  /**
   * Core logging method
   */
  log(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level}] [${this.context}]`;
    
    // Choose appropriate console method
    const consoleMethod = this.getConsoleMethod(level);
    
    // Format the message
    const formattedMessage = `${prefix} ${message}`;
    
    // Log to console
    if (args.length > 0) {
      consoleMethod(formattedMessage, ...args);
    } else {
      consoleMethod(formattedMessage);
    }
    
    // Store log entry for debugging if enabled
    this.storeLogEntry(level, message, args);
  }

  /**
   * Get appropriate console method for log level
   */
  getConsoleMethod(level) {
    switch (level) {
      case 'ERROR':
        return console.error;
      case 'WARN':
        return console.warn;
      case 'DEBUG':
        return console.debug;
      case 'INFO':
      default:
        return console.log;
    }
  }

  /**
   * Store log entry for debugging (if enabled)
   */
  async storeLogEntry(level, message, args) {
    try {
      // Check if log storage is enabled
      const stored = await chrome.storage.local.get(['enableLogStorage']);
      if (!stored.enableLogStorage) {
        return;
      }

      const logEntry = {
        timestamp: new Date().toISOString(),
        level,
        context: this.context,
        message,
        args: args.length > 0 ? this.sanitizeArgs(args) : undefined
      };

      // Get existing logs
      const logs = await this.getStoredLogs();
      
      // Add new entry and keep only last 500 entries
      logs.push(logEntry);
      const trimmedLogs = logs.slice(-500);
      
      await chrome.storage.local.set({ debugLogs: trimmedLogs });
    } catch (error) {
      // Ignore errors when storing logs to prevent infinite loops
    }
  }

  /**
   * Sanitize arguments for storage
   */
  sanitizeArgs(args) {
    return args.map(arg => {
      if (arg instanceof Error) {
        return {
          name: arg.name,
          message: arg.message,
          stack: arg.stack
        };
      } else if (typeof arg === 'object') {
        try {
          return JSON.parse(JSON.stringify(arg));
        } catch {
          return '[Object]';
        }
      } else {
        return arg;
      }
    });
  }

  /**
   * Get stored logs
   */
  async getStoredLogs() {
    try {
      const stored = await chrome.storage.local.get(['debugLogs']);
      return stored.debugLogs || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Clear stored logs
   */
  async clearStoredLogs() {
    try {
      await chrome.storage.local.remove(['debugLogs']);
      this.info('Stored logs cleared');
    } catch (error) {
      this.error('Failed to clear stored logs:', error);
    }
  }

  /**
   * Get logs for debugging
   */
  async getDebugLogs(levelFilter = null, limit = 100) {
    try {
      const logs = await this.getStoredLogs();
      
      let filteredLogs = logs;
      
      // Filter by level if specified
      if (levelFilter) {
        filteredLogs = logs.filter(log => log.level === levelFilter);
      }
      
      // Return last N entries
      return filteredLogs.slice(-limit);
    } catch (error) {
      this.error('Failed to get debug logs:', error);
      return [];
    }
  }

  /**
   * Export logs as JSON string
   */
  async exportLogs() {
    try {
      const logs = await this.getStoredLogs();
      return JSON.stringify(logs, null, 2);
    } catch (error) {
      this.error('Failed to export logs:', error);
      return '[]';
    }
  }

  /**
   * Create a performance timer
   */
  createTimer(name) {
    const startTime = performance.now();
    
    return {
      end: () => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.debug(`Timer [${name}]: ${duration.toFixed(2)}ms`);
        return duration;
      }
    };
  }

  /**
   * Log with performance timing
   */
  timeLog(name, fn) {
    return async (...args) => {
      const timer = this.createTimer(name);
      try {
        const result = await fn(...args);
        timer.end();
        return result;
      } catch (error) {
        timer.end();
        this.error(`Error in timed function [${name}]:`, error);
        throw error;
      }
    };
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext) {
    const childLogger = new Logger(`${this.context}:${additionalContext}`);
    childLogger.currentLevel = this.currentLevel;
    return childLogger;
  }

  /**
   * Log API request details
   */
  logApiRequest(method, url, options = {}) {
    this.debug(`API Request: ${method} ${url}`, {
      headers: options.headers,
      body: options.body ? '[body data]' : undefined
    });
  }

  /**
   * Log API response details
   */
  logApiResponse(url, status, data = null) {
    const level = status >= 400 ? 'WARN' : 'DEBUG';
    this.log(level, `API Response: ${status} ${url}`, data ? '[response data]' : undefined);
  }

  /**
   * Log user action
   */
  logUserAction(action, details = {}) {
    this.info(`User Action: ${action}`, details);
  }

  /**
   * Log extension lifecycle events
   */
  logLifecycle(event, details = {}) {
    this.info(`Lifecycle: ${event}`, details);
  }

  /**
   * Log performance metrics
   */
  logPerformance(operation, metrics) {
    this.debug(`Performance [${operation}]:`, metrics);
  }

  /**
   * Static method to create logger instances
   */
  static create(context) {
    return new Logger(context);
  }

  /**
   * Static method to get log levels enum
   */
  static get LogLevels() {
    return {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
  }
} 