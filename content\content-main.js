/**
 * Auto Form Fill Pro - Content Script Main
 * Coordinates all content script functionality and communication
 */

class ContentScriptMain {
  constructor() {
    this.formDetector = null;
    this.formFiller = null;
    this.fieldHighlighter = null;
    this.isInitialized = false;
    this.configuration = null;
    this.lastDetectionResults = null;
    
    this.init();
  }

  /**
   * Initialize the content script
   */
  async init() {
    try {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // Initialize components
      this.initializeComponents();
      
      // Set up message listeners
      this.setupMessageListeners();
      
      // Load configuration
      await this.loadConfiguration();
      
      // Start form detection if enabled
      if (this.configuration?.autoFill?.enabled) {
        this.startFormDetection();
      }
      
      this.isInitialized = true;
      console.log('Auto Form Fill Pro: Content script initialized');
      
    } catch (error) {
      console.error('Failed to initialize content script:', error);
    }
  }

  /**
   * Initialize component instances
   */
  initializeComponents() {
    // Initialize form detector
    if (window.FormDetector) {
      this.formDetector = new window.FormDetector();
    }
    
    // Initialize form filler
    if (window.FormFiller) {
      this.formFiller = new window.FormFiller();
    }
    
    // Field highlighter should already be initialized as singleton
    this.fieldHighlighter = window.FieldHighlighter;
  }

  /**
   * Set up message listeners for background script communication
   */
  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender)
        .then(response => {
          sendResponse(response);
        })
        .catch(error => {
          console.error('Message handler error:', error);
          sendResponse({ success: false, error: error.message });
        });
      
      return true; // Keep message channel open for async response
    });
  }

  /**
   * Handle messages from background script
   */
  async handleMessage(message, sender) {
    const { action, data } = message;
    
    switch (action) {
      case 'PING':
        return { success: true, message: 'Content script active' };
        
      case 'EXECUTE_AUTOFILL':
        return await this.executeAutoFill(data);
        
      case 'DETECT_FORMS':
        return await this.detectForms();
        
      case 'GET_PAGE_STATS':
        return await this.getPageStats();
        
      case 'HIGHLIGHT_FIELDS':
        return await this.highlightFields(data);
        
      case 'CLEAR_HIGHLIGHTS':
        return await this.clearHighlights();
        
      case 'PAGE_READY':
        return await this.handlePageReady(data);
        
      case 'CONFIGURATION_UPDATED':
        return await this.handleConfigurationUpdate(data);
        
      case 'EXTENSION_TOGGLED':
        return await this.handleExtensionToggle(data);
        
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }

  /**
   * Execute auto-fill process
   */
  async executeAutoFill(formData) {
    try {
      if (!this.formFiller) {
        throw new Error('Form filler not available');
      }

      if (!formData) {
        throw new Error('No form data provided');
      }

      // Get configuration for fill options
      const config = this.configuration?.autoFill || {};
      
      const fillOptions = {
        confirmBeforeFill: config.confirmBeforeFill || false,
        highlightFields: config.highlightFields !== false,
        animationSpeed: config.animationSpeed || 'medium'
      };

      // Execute the fill
      const result = await this.formFiller.fillForms(formData, fillOptions);
      
      // Log the result
      console.log('Auto-fill completed:', result);
      
      return {
        success: true,
        data: result
      };
      
    } catch (error) {
      console.error('Auto-fill execution error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Detect forms on the current page
   */
  async detectForms() {
    try {
      if (!this.formDetector) {
        throw new Error('Form detector not available');
      }

      // Start detection
      this.formDetector.startDetection();
      
      // Get detected forms
      const forms = this.formDetector.getForms();
      
      // Calculate stats
      const stats = this.formDetector.getStats();
      
      // Store results
      this.lastDetectionResults = {
        forms,
        stats,
        timestamp: Date.now()
      };
      
      console.log('Forms detected:', stats);
      
      return {
        success: true,
        data: {
          forms: stats.formsDetected,
          fields: stats.fieldsDetected,
          confidence: stats.averageConfidence,
          formless: stats.formlessFields
        }
      };
      
    } catch (error) {
      console.error('Form detection error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get page statistics
   */
  async getPageStats() {
    try {
      // Run detection if not already done
      if (!this.lastDetectionResults || 
          Date.now() - this.lastDetectionResults.timestamp > 30000) {
        await this.detectForms();
      }
      
      const stats = this.lastDetectionResults?.stats || {
        formsDetected: 0,
        fieldsDetected: 0,
        averageConfidence: 0,
        formlessFields: 0
      };
      
      return {
        success: true,
        data: {
          formsCount: stats.formsDetected,
          fieldsCount: stats.fieldsDetected,
          confidence: stats.averageConfidence,
          formlessFields: stats.formlessFields,
          url: window.location.href,
          title: document.title
        }
      };
      
    } catch (error) {
      console.error('Get page stats error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Highlight specified fields
   */
  async highlightFields(options = {}) {
    try {
      if (!this.fieldHighlighter) {
        throw new Error('Field highlighter not available');
      }

      const {
        fieldSelectors = [],
        highlightType = 'preview',
        duration = 3000
      } = options;

      let fieldsHighlighted = 0;

      if (fieldSelectors.length > 0) {
        // Highlight specific fields
        fieldSelectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            this.fieldHighlighter.highlightField(element, highlightType, { duration });
            fieldsHighlighted++;
          });
        });
      } else {
        // Highlight all fillable fields
        const forms = this.formDetector?.getForms() || [];
        forms.forEach(form => {
          if (form.fields) {
            form.fields.forEach(field => {
              this.fieldHighlighter.highlightField(field.element, highlightType, { duration });
              fieldsHighlighted++;
            });
          }
        });
      }

      return {
        success: true,
        data: { fieldsHighlighted }
      };
      
    } catch (error) {
      console.error('Highlight fields error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Clear all field highlights
   */
  async clearHighlights() {
    try {
      if (this.fieldHighlighter) {
        this.fieldHighlighter.clearAllHighlights();
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('Clear highlights error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle page ready event
   */
  async handlePageReady(config) {
    try {
      // Update configuration
      if (config) {
        this.configuration = config;
      }
      
      // Start form detection if enabled
      if (this.configuration?.autoFill?.enabled) {
        this.startFormDetection();
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('Page ready handler error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle configuration update
   */
  async handleConfigurationUpdate(newConfig) {
    try {
      this.configuration = newConfig;
      
      // Restart form detection if needed
      if (this.formDetector) {
        if (newConfig?.autoFill?.enabled) {
          this.startFormDetection();
        } else {
          this.stopFormDetection();
        }
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('Configuration update handler error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle extension toggle
   */
  async handleExtensionToggle(data) {
    try {
      const { isEnabled } = data;
      
      if (isEnabled) {
        this.startFormDetection();
      } else {
        this.stopFormDetection();
        this.clearHighlights();
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('Extension toggle handler error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Start form detection
   */
  startFormDetection() {
    if (this.formDetector && !this.formDetector.observer) {
      this.formDetector.startDetection();
      console.log('Form detection started');
    }
  }

  /**
   * Stop form detection
   */
  stopFormDetection() {
    if (this.formDetector) {
      this.formDetector.stopDetection();
      console.log('Form detection stopped');
    }
  }

  /**
   * Load configuration from background script
   */
  async loadConfiguration() {
    try {
      const response = await this.sendMessageToBackground({
        action: 'GET_CONFIGURATION'
      });
      
      if (response.success) {
        this.configuration = response.data;
        console.log('Configuration loaded:', this.configuration);
      }
      
    } catch (error) {
      console.error('Failed to load configuration:', error);
    }
  }

  /**
   * Send message to background script
   */
  async sendMessageToBackground(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, resolve);
    });
  }

  /**
   * Get detected forms data
   */
  getDetectedForms() {
    return this.lastDetectionResults?.forms || [];
  }

  /**
   * Check if content script is ready
   */
  isReady() {
    return this.isInitialized && this.formDetector && this.formFiller;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    try {
      if (this.formDetector) {
        this.formDetector.stopDetection();
      }
      
      if (this.fieldHighlighter) {
        this.fieldHighlighter.cleanup();
      }
      
      console.log('Content script cleaned up');
      
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  }
}

// Global keyboard shortcut handler
function setupKeyboardShortcuts() {
  document.addEventListener('keydown', async (event) => {
    // Ctrl+Shift+F (or Cmd+Shift+F on Mac) - trigger auto-fill
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F') {
      event.preventDefault();
      
      try {
        const response = await chrome.runtime.sendMessage({
          action: 'TRIGGER_AUTOFILL',
          data: { source: 'keyboard' }
        });
        
        if (!response.success) {
          console.error('Keyboard auto-fill failed:', response.error);
        }
      } catch (error) {
        console.error('Keyboard shortcut error:', error);
      }
    }
  });
}

// Initialize content script when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    // Small delay to ensure all scripts are loaded
    setTimeout(() => {
      window.contentScriptMain = new ContentScriptMain();
      setupKeyboardShortcuts();
    }, 100);
  });
} else {
  // DOM already ready
  setTimeout(() => {
    window.contentScriptMain = new ContentScriptMain();
    setupKeyboardShortcuts();
  }, 100);
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (window.contentScriptMain) {
    window.contentScriptMain.cleanup();
  }
}); 