<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Form Fill Pro - Settings</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <img src="../icons/icon-32.png" alt="Auto Form Fill Pro" class="logo">
                <div class="header-text">
                    <h1>Auto Form Fill Pro</h1>
                    <p>Extension Settings</p>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="general">General</button>
            <button class="nav-tab" data-tab="api">API Configuration</button>
            <button class="nav-tab" data-tab="autofill">Auto-Fill Settings</button>
            <button class="nav-tab" data-tab="domains">Domains</button>
            <button class="nav-tab" data-tab="mappings">Field Mappings</button>
            <button class="nav-tab" data-tab="security">Security</button>
            <button class="nav-tab" data-tab="advanced">Advanced</button>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- General Tab -->
            <section class="tab-content active" id="general">
                <div class="section-header">
                    <h2>General Settings</h2>
                    <p>Basic configuration options for the extension</p>
                </div>

                <div class="settings-group">
                    <h3>Extension Status</h3>
                    <div class="setting-item">
                        <label class="toggle-container">
                            <input type="checkbox" id="extensionEnabled">
                            <span class="toggle-slider"></span>
                            <div class="toggle-info">
                                <span class="toggle-label">Enable Extension</span>
                                <span class="toggle-description">Turn the auto-fill extension on or off globally</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>User Interface</h3>
                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="showNotifications">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Show Notifications</span>
                                <span class="checkbox-description">Display success and error notifications</span>
                            </div>
                        </label>
                    </div>
                    
                    <div class="setting-item">
                        <label class="select-container">
                            <span class="select-label">Theme</span>
                            <select id="theme" class="select-input">
                                <option value="auto">Auto (System)</option>
                                <option value="light">Light</option>
                                <option value="dark">Dark</option>
                            </select>
                            <span class="select-description">Choose the extension's color theme</span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="compactMode">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Compact Mode</span>
                                <span class="checkbox-description">Use a more compact popup interface</span>
                            </div>
                        </label>
                    </div>
                </div>
            </section>

            <!-- API Configuration Tab -->
            <section class="tab-content" id="api">
                <div class="section-header">
                    <h2>API Configuration</h2>
                    <p>Configure connection to your local API server</p>
                </div>

                <div class="settings-group">
                    <h3>Connection Settings</h3>
                    <div class="setting-item">
                        <label class="input-container">
                            <span class="input-label">API Base URL</span>
                            <input type="url" id="apiBaseUrl" class="input-field" placeholder="http://localhost:3000">
                            <span class="input-description">The base URL of your local API server</span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="input-container">
                            <span class="input-label">Request Timeout (ms)</span>
                            <input type="number" id="apiTimeout" class="input-field" min="1000" max="60000" step="1000">
                            <span class="input-description">Maximum time to wait for API responses</span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="input-container">
                            <span class="input-label">Retry Attempts</span>
                            <input type="number" id="retryAttempts" class="input-field" min="1" max="10" step="1">
                            <span class="input-description">Number of retry attempts for failed requests</span>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Authentication</h3>
                    <div class="setting-item">
                        <label class="input-container">
                            <span class="input-label">Username</span>
                            <input type="text" id="apiUsername" class="input-field" autocomplete="username">
                            <span class="input-description">Your API username</span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="input-container">
                            <span class="input-label">Password</span>
                            <input type="password" id="apiPassword" class="input-field" autocomplete="current-password">
                            <span class="input-description">Your API password (securely encrypted)</span>
                        </label>
                    </div>

                    <div class="setting-actions">
                        <button type="button" class="btn secondary" id="testConnection">Test Connection</button>
                        <button type="button" class="btn secondary" id="clearCredentials">Clear Credentials</button>
                    </div>
                </div>

                <div class="status-panel" id="apiStatus">
                    <div class="status-icon" id="statusIcon">❓</div>
                    <div class="status-text">
                        <div class="status-title" id="statusTitle">Connection Status Unknown</div>
                        <div class="status-message" id="statusMessage">Click "Test Connection" to check API availability</div>
                    </div>
                </div>
            </section>

            <!-- Auto-Fill Settings Tab -->
            <section class="tab-content" id="autofill">
                <div class="section-header">
                    <h2>Auto-Fill Settings</h2>
                    <p>Configure how forms are automatically filled</p>
                </div>

                <div class="settings-group">
                    <h3>Fill Behavior</h3>
                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="autoFillEnabled">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Enable Auto-Fill</span>
                                <span class="checkbox-description">Automatically detect and fill forms on web pages</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="confirmBeforeFill">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Confirm Before Fill</span>
                                <span class="checkbox-description">Ask for confirmation before filling forms</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="smartFieldDetection">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Smart Field Detection</span>
                                <span class="checkbox-description">Use intelligent algorithms to detect field types</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Visual Feedback</h3>
                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="highlightFields">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Highlight Fields</span>
                                <span class="checkbox-description">Visually highlight fields as they are being filled</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="select-container">
                            <span class="select-label">Animation Speed</span>
                            <select id="animationSpeed" class="select-input">
                                <option value="fast">Fast</option>
                                <option value="medium">Medium</option>
                                <option value="slow">Slow</option>
                            </select>
                            <span class="select-description">Speed of field filling animations</span>
                        </label>
                    </div>
                </div>
            </section>

            <!-- Domains Tab -->
            <section class="tab-content" id="domains">
                <div class="section-header">
                    <h2>Domain Management</h2>
                    <p>Control which websites the extension works on</p>
                </div>

                <div class="settings-group">
                    <h3>Allowed Domains (Whitelist)</h3>
                    <p class="group-description">If specified, the extension will only work on these domains. Leave empty to allow all domains.</p>
                    
                    <div class="domain-list-container">
                        <div class="domain-input-container">
                            <input type="text" id="whitelistInput" class="input-field" placeholder="example.com">
                            <button type="button" class="btn secondary" id="addWhitelist">Add</button>
                        </div>
                        <ul class="domain-list" id="whitelistDomains"></ul>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Blocked Domains (Blacklist)</h3>
                    <p class="group-description">The extension will never work on these domains.</p>
                    
                    <div class="domain-list-container">
                        <div class="domain-input-container">
                            <input type="text" id="blacklistInput" class="input-field" placeholder="example.com">
                            <button type="button" class="btn secondary" id="addBlacklist">Add</button>
                        </div>
                        <ul class="domain-list" id="blacklistDomains"></ul>
                    </div>
                </div>
            </section>

            <!-- Field Mappings Tab -->
            <section class="tab-content" id="mappings">
                <div class="section-header">
                    <h2>Field Mappings</h2>
                    <p>Customize how form fields are mapped to your data</p>
                </div>

                <div class="settings-group">
                    <h3>Default Field Mappings</h3>
                    <p class="group-description">Configure default mappings for common field types.</p>
                    
                    <div class="mapping-grid">
                        <div class="mapping-item">
                            <label class="input-container">
                                <span class="input-label">Name Fields</span>
                                <input type="text" id="nameMapping" class="input-field" placeholder="fullName">
                                <span class="input-description">API field for name data</span>
                            </label>
                        </div>

                        <div class="mapping-item">
                            <label class="input-container">
                                <span class="input-label">Email Fields</span>
                                <input type="text" id="emailMapping" class="input-field" placeholder="email">
                                <span class="input-description">API field for email data</span>
                            </label>
                        </div>

                        <div class="mapping-item">
                            <label class="input-container">
                                <span class="input-label">Phone Fields</span>
                                <input type="text" id="phoneMapping" class="input-field" placeholder="phone">
                                <span class="input-description">API field for phone data</span>
                            </label>
                        </div>

                        <div class="mapping-item">
                            <label class="input-container">
                                <span class="input-label">Address Fields</span>
                                <input type="text" id="addressMapping" class="input-field" placeholder="address">
                                <span class="input-description">API field for address data</span>
                            </label>
                        </div>

                        <div class="mapping-item">
                            <label class="input-container">
                                <span class="input-label">Company Fields</span>
                                <input type="text" id="companyMapping" class="input-field" placeholder="company">
                                <span class="input-description">API field for company data</span>
                            </label>
                        </div>

                        <div class="mapping-item">
                            <label class="input-container">
                                <span class="input-label">Website Fields</span>
                                <input type="text" id="websiteMapping" class="input-field" placeholder="website">
                                <span class="input-description">API field for website data</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Custom Mappings</h3>
                    <p class="group-description">Add custom field mappings for specific forms or websites.</p>
                    
                    <div class="custom-mapping-container">
                        <div class="custom-mapping-input">
                            <input type="text" id="customFieldSelector" class="input-field" placeholder="CSS selector or field name">
                            <input type="text" id="customApiField" class="input-field" placeholder="API field name">
                            <button type="button" class="btn secondary" id="addCustomMapping">Add Mapping</button>
                        </div>
                        <div class="custom-mappings-list" id="customMappingsList"></div>
                    </div>
                </div>
            </section>

            <!-- Security Tab -->
            <section class="tab-content" id="security">
                <div class="section-header">
                    <h2>Security Settings</h2>
                    <p>Configure security and privacy options</p>
                </div>

                <div class="settings-group">
                    <h3>Data Encryption</h3>
                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="encryptData">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Encrypt Stored Data</span>
                                <span class="checkbox-description">Encrypt sensitive data before storing locally</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="clearDataOnLogout">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Clear Data on Logout</span>
                                <span class="checkbox-description">Automatically clear stored data when logging out</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Session Management</h3>
                    <div class="setting-item">
                        <label class="input-container">
                            <span class="input-label">Session Timeout (minutes)</span>
                            <input type="number" id="sessionTimeout" class="input-field" min="5" max="1440" step="5">
                            <span class="input-description">Automatically logout after this period of inactivity</span>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Activity Logging</h3>
                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="logActivity">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Enable Activity Logging</span>
                                <span class="checkbox-description">Log extension activities for debugging purposes</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-actions">
                        <button type="button" class="btn secondary" id="viewLogs">View Logs</button>
                        <button type="button" class="btn secondary" id="exportLogs">Export Logs</button>
                        <button type="button" class="btn danger" id="clearLogs">Clear Logs</button>
                    </div>
                </div>
            </section>

            <!-- Advanced Tab -->
            <section class="tab-content" id="advanced">
                <div class="section-header">
                    <h2>Advanced Settings</h2>
                    <p>Advanced configuration options for power users</p>
                </div>

                <div class="settings-group">
                    <h3>Debug Options</h3>
                    <div class="setting-item">
                        <label class="select-container">
                            <span class="select-label">Log Level</span>
                            <select id="logLevel" class="select-input">
                                <option value="0">Error</option>
                                <option value="1">Warning</option>
                                <option value="2">Info</option>
                                <option value="3">Debug</option>
                            </select>
                            <span class="select-description">Amount of detail in console logs</span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-container">
                            <input type="checkbox" id="enableLogStorage">
                            <span class="checkbox-checkmark"></span>
                            <div class="checkbox-info">
                                <span class="checkbox-label">Store Debug Logs</span>
                                <span class="checkbox-description">Keep debug logs in browser storage (uses more space)</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Performance</h3>
                    <div class="setting-item">
                        <label class="input-container">
                            <span class="input-label">Cache TTL (seconds)</span>
                            <input type="number" id="cacheTtl" class="input-field" min="30" max="3600" step="30">
                            <span class="input-description">How long to cache API responses</span>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Data Management</h3>
                    <div class="setting-actions">
                        <button type="button" class="btn secondary" id="exportSettings">Export Settings</button>
                        <button type="button" class="btn secondary" id="importSettings">Import Settings</button>
                        <input type="file" id="importFile" accept=".json" style="display: none;">
                    </div>
                    
                    <div class="setting-actions">
                        <button type="button" class="btn danger" id="resetSettings">Reset to Defaults</button>
                        <button type="button" class="btn danger" id="clearAllData">Clear All Data</button>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Storage Usage</h3>
                    <div class="storage-stats" id="storageStats">
                        <div class="storage-item">
                            <span class="storage-label">Sync Storage:</span>
                            <span class="storage-value" id="syncStorageUsage">Loading...</span>
                        </div>
                        <div class="storage-item">
                            <span class="storage-label">Local Storage:</span>
                            <span class="storage-value" id="localStorageUsage">Loading...</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="save-section">
                <button type="button" class="btn primary" id="saveSettings">Save Settings</button>
                <button type="button" class="btn secondary" id="resetSettings">Reset</button>
                <div class="save-status" id="saveStatus"></div>
            </div>
            
            <div class="footer-info">
                <p>Auto Form Fill Pro v1.0.0</p>
                <div class="footer-links">
                    <a href="#" id="helpLink">Help</a>
                    <span>•</span>
                    <a href="#" id="feedbackLink">Feedback</a>
                </div>
            </div>
        </footer>

        <!-- Modals -->
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal" id="confirmModal">
                <div class="modal-header">
                    <h3 id="modalTitle">Confirm Action</h3>
                </div>
                <div class="modal-body">
                    <p id="modalMessage">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" id="modalCancel">Cancel</button>
                    <button type="button" class="btn primary" id="modalConfirm">Confirm</button>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <!-- Scripts -->
    <script src="options.js"></script>
</body>
</html> 