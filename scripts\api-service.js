/**
 * API Service untuk Venus-Millware AutoFill
 * <PERSON><PERSON><PERSON> komunikas<PERSON> dengan staging API lokal
 * 
 * <AUTHOR> - <PERSON> Rebinmas (Delloyd Group)
 * @version 1.0.0
 */

class ApiService {
    constructor() {
        this.baseUrl = 'http://localhost:5173/api'; // Corrected staging API endpoint
        this.credentials = null;
        this.retryAttempts = 3;
        this.retryDelay = 1000;

        this.init();
    }

    async init() {
        // Load saved credentials and API configuration
        await this.loadConfiguration();
    }

    /**
     * Load API configuration and credentials from storage
     */
    async loadConfiguration() {
        try {
            const result = await chrome.storage.local.get(['apiConfig', 'apiCredentials']);
            
            if (result.apiConfig) {
                this.baseUrl = result.apiConfig.baseUrl || this.baseUrl;
            }
            
            if (result.apiCredentials) {
                this.credentials = result.apiCredentials;
            }
        } catch (error) {
            console.error('Failed to load API configuration:', error);
        }
    }

    /**
     * Save API configuration to storage
     * @param {Object} config - API configuration
     */
    async saveConfiguration(config) {
        try {
            await chrome.storage.local.set({ apiConfig: config });
            this.baseUrl = config.baseUrl || this.baseUrl;
            return { success: true };
        } catch (error) {
            console.error('Failed to save API configuration:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Save API credentials securely
     * @param {Object} credentials - API credentials
     */
    async saveCredentials(credentials) {
        try {
            // Encrypt sensitive data before storing
            const encryptedCredentials = await this.encryptCredentials(credentials);
            await chrome.storage.local.set({ apiCredentials: encryptedCredentials });
            this.credentials = encryptedCredentials;
            return { success: true };
        } catch (error) {
            console.error('Failed to save API credentials:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Simple credential encryption (basic implementation)
     * In production, use proper encryption libraries
     * @param {Object} credentials 
     */
    async encryptCredentials(credentials) {
        // Basic encoding - in production, use proper encryption
        const encoded = btoa(JSON.stringify(credentials));
        return { encoded, timestamp: Date.now() };
    }

    /**
     * Decrypt credentials
     * @param {Object} encryptedCredentials 
     */
    async decryptCredentials(encryptedCredentials) {
        try {
            if (!encryptedCredentials || !encryptedCredentials.encoded) {
                return null;
            }
            return JSON.parse(atob(encryptedCredentials.encoded));
        } catch (error) {
            console.error('Failed to decrypt credentials:', error);
            return null;
        }
    }

    /**
     * Test API connection
     * @returns {Promise<Object>} Connection test result
     */
    async testConnection() {
        const testResults = {
            overall: false,
            baseUrl: this.baseUrl,
            timestamp: new Date().toISOString(),
            tests: []
        };

        // Test 1: Health check endpoint
        try {
            console.log('🔍 Testing health endpoint...');
            const healthResponse = await this.makeRequest('/health', {
                method: 'GET',
                timeout: 10000
            });

            testResults.tests.push({
                name: 'Health Check',
                url: `${this.baseUrl}/health`,
                status: 'success',
                response: healthResponse,
                message: 'Health endpoint accessible'
            });

        } catch (error) {
            testResults.tests.push({
                name: 'Health Check',
                url: `${this.baseUrl}/health`,
                status: 'error',
                error: error.message,
                message: 'Health endpoint failed'
            });
        }

        // Test 2: Staging data endpoint
        try {
            console.log('🔍 Testing staging data endpoint...');
            const stagingResponse = await this.makeRequest('/staging/data', {
                method: 'GET',
                timeout: 15000
            });

            testResults.tests.push({
                name: 'Staging Data',
                url: `${this.baseUrl}/staging/data`,
                status: 'success',
                response: stagingResponse,
                dataCount: stagingResponse.data ? stagingResponse.data.length : 0,
                message: `Staging data accessible (${stagingResponse.data ? stagingResponse.data.length : 0} records)`
            });

        } catch (error) {
            testResults.tests.push({
                name: 'Staging Data',
                url: `${this.baseUrl}/staging/data`,
                status: 'error',
                error: error.message,
                message: 'Staging data endpoint failed'
            });
        }

        // Test 3: Try alternative localhost URL if using IP
        if (this.baseUrl.includes('**********')) {
            try {
                console.log('🔍 Testing localhost fallback...');
                const localhostUrl = this.baseUrl.replace('**********', 'localhost');
                const localhostResponse = await fetch(`${localhostUrl}/health`, {
                    method: 'GET',
                    mode: 'cors',
                    timeout: 5000
                });

                testResults.tests.push({
                    name: 'Localhost Fallback',
                    url: `${localhostUrl}/health`,
                    status: localhostResponse.ok ? 'success' : 'error',
                    message: localhostResponse.ok ? 'Localhost accessible' : `HTTP ${localhostResponse.status}`
                });

            } catch (error) {
                testResults.tests.push({
                    name: 'Localhost Fallback',
                    url: `${this.baseUrl.replace('**********', 'localhost')}/health`,
                    status: 'error',
                    error: error.message,
                    message: 'Localhost not accessible'
                });
            }
        }

        // Determine overall status
        const successfulTests = testResults.tests.filter(test => test.status === 'success');
        testResults.overall = successfulTests.length > 0;
        testResults.successCount = successfulTests.length;
        testResults.totalTests = testResults.tests.length;

        if (testResults.overall) {
            return {
                success: true,
                status: 'connected',
                serverInfo: testResults,
                timestamp: testResults.timestamp,
                message: `Successfully connected to staging API (${successfulTests.length}/${testResults.tests.length} tests passed)`
            };
        } else {
            return {
                success: false,
                status: 'failed',
                error: 'All connection tests failed',
                details: testResults,
                timestamp: testResults.timestamp,
                suggestions: this.getConnectionSuggestions()
            };
        }
    }

    /**
     * Get connection troubleshooting suggestions
     * @returns {Array} Array of suggestions
     */
    getConnectionSuggestions() {
        return [
            'Check if staging API server is running: npm start',
            'Verify server is accessible in browser: http://localhost:5173/api/health',
            'Check Windows Firewall settings for port 5173',
            'Try restarting the staging API server',
            'Ensure no antivirus is blocking the connection',
            'Check if another application is using port 5173'
        ];
    }

    /**
     * Fetch employee timesheet data from staging API
     * @returns {Promise<Object>} Employee timesheet data
     */
    async fetchStagingData() {
        try {
            console.log('🔄 Fetching staging data from:', `${this.baseUrl}/staging/data`);

            const response = await this.makeRequest('/staging/data', {
                method: 'GET',
                timeout: 15000
            });

            console.log('📋 API Service received response:', response);

            // Handle the correct API response format
            if (response && response.success && response.data && Array.isArray(response.data)) {
                const processedData = this.processEmployeeTimesheetData(response.data);

                return {
                    success: true,
                    data: processedData,
                    rawData: response.data,
                    metadata: {
                        totalRecords: response.total_records || response.data.length,
                        returnedRecords: response.returned_records || response.data.length,
                        processedRecords: processedData.length,
                        timestamp: new Date().toISOString(),
                        source: 'staging_api',
                        pagination: response.pagination || null
                    }
                };
            } else if (response && Array.isArray(response)) {
                // Fallback for direct array response
                const processedData = this.processEmployeeTimesheetData(response);

                return {
                    success: true,
                    data: processedData,
                    rawData: response,
                    metadata: {
                        totalRecords: response.length,
                        processedRecords: processedData.length,
                        timestamp: new Date().toISOString(),
                        source: 'staging_api'
                    }
                };
            } else {
                throw new Error(`Invalid response format from staging API. Expected object with success and data properties, got: ${typeof response}`);
            }

        } catch (error) {
            console.error('❌ Error fetching staging data:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString(),
                endpoint: `${this.baseUrl}/staging/data`
            };
        }
    }

    /**
     * Process employee timesheet data into standardized format
     * @param {Array} rawData - Raw data from staging API
     * @returns {Array} Processed employee data
     */
    processEmployeeTimesheetData(rawData) {
        if (!Array.isArray(rawData)) {
            console.warn('Expected array data, got:', typeof rawData);
            return [];
        }

        return rawData.map((record, index) => {
            try {
                // Normalize field names (handle different possible field naming)
                const employeeId = record.employee_id || record.employeeId || record.id || `EMP_${index + 1}`;
                const employeeName = record.employee_name || record.employeeName || record.name || 'Unknown Employee';
                const date = record.date || record.workDate || new Date().toISOString().split('T')[0];
                
                // Time fields
                const checkIn = record.check_in || record.checkIn || record.timeIn || '';
                const checkOut = record.check_out || record.checkOut || record.timeOut || '';
                const regularHours = parseFloat(record.regular_hours || record.regularHours || 0);
                const overtimeHours = parseFloat(record.overtime_hours || record.overtimeHours || 0);
                
                // Additional fields
                const taskCode = record.task_code || record.taskCode || record.task || '';
                const machineCode = record.machine_code || record.machineCode || record.machine || '';
                const expenseCode = record.expense_code || record.expenseCode || record.expense || '';
                
                return {
                    employeeId,
                    employeeName,
                    date,
                    checkIn,
                    checkOut,
                    regularHours,
                    overtimeHours,
                    totalHours: regularHours + overtimeHours,
                    taskCode,
                    machineCode,
                    expenseCode,
                    // Keep original data for reference
                    _original: record
                };
            } catch (error) {
                console.error('Error processing record:', record, error);
                return {
                    employeeId: `ERROR_${index}`,
                    employeeName: 'Processing Error',
                    date: new Date().toISOString().split('T')[0],
                    checkIn: '',
                    checkOut: '',
                    regularHours: 0,
                    overtimeHours: 0,
                    totalHours: 0,
                    taskCode: '',
                    machineCode: '',
                    expenseCode: '',
                    _error: error.message,
                    _original: record
                };
            }
        });
    }

    /**
     * Submit automation results back to staging API
     * @param {Object} results - Automation execution results
     * @returns {Promise<Object>} Submission result
     */
    async submitAutomationResults(results) {
        try {
            const submissionData = {
                executionId: results.executionId || this.generateExecutionId(),
                timestamp: new Date().toISOString(),
                success: results.success || false,
                eventsExecuted: results.eventsExecuted || 0,
                duration: results.duration || 0,
                processedRecords: results.processedRecords || 0,
                extractedData: results.extractedData || {},
                errors: results.errors || [],
                metadata: {
                    targetWebsite: 'millwarep3.rebinmas.com',
                    automationType: 'timesheet_entry',
                    version: '1.0.0'
                }
            };

            const response = await this.makeRequest('/automation/results', {
                method: 'POST',
                body: submissionData,
                timeout: 10000
            });

            return {
                success: true,
                message: 'Results submitted successfully',
                data: response.data || {},
                submissionId: response.submissionId
            };
        } catch (error) {
            console.error('Failed to submit automation results:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Authenticate with the API (if required)
     * @param {Object} credentials - Login credentials
     * @returns {Promise<Object>} Authentication result
     */
    async authenticate(credentials) {
        try {
            const response = await this.makeRequest('/auth/login', {
                method: 'POST',
                body: credentials,
                timeout: 10000
            });

            if (response.success && response.token) {
                const authData = {
                    token: response.token,
                    refreshToken: response.refreshToken,
                    expiresAt: Date.now() + (response.expiresIn * 1000),
                    userInfo: response.user || {}
                };

                await this.saveCredentials(authData);
                
                return {
                    success: true,
                    message: 'Authentication successful',
                    userInfo: authData.userInfo
                };
            } else {
                throw new Error(response.message || 'Authentication failed');
            }
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Make authenticated API request with retry logic
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise<Object>} API response
     */
    async makeRequest(endpoint, options = {}) {
        const {
            method = 'GET',
            body = null,
            headers = {},
            requiresAuth = false,
            timeout = 30000,
            retryCount = 0
        } = options;

        try {
            // Build full URL
            const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;

            // Prepare headers
            const requestHeaders = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'ChromeExtensionBot/1.0',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                ...headers
            };

            // Add authentication if required
            if (requiresAuth && this.credentials) {
                const decryptedCreds = await this.decryptCredentials(this.credentials);
                if (decryptedCreds && decryptedCreds.token) {
                    if (decryptedCreds.expiresAt && Date.now() > decryptedCreds.expiresAt) {
                        await this.refreshAuthToken();
                        const newCreds = await this.decryptCredentials(this.credentials);
                        if (newCreds && newCreds.token) {
                            requestHeaders['Authorization'] = `Bearer ${newCreds.token}`;
                        }
                    } else {
                        requestHeaders['Authorization'] = `Bearer ${decryptedCreds.token}`;
                    }
                }
            }

            // Create abort controller for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            console.log(`🌐 Making ${method} request to: ${url}`);
            console.log(`🔧 Request headers:`, requestHeaders);

            // Prepare fetch options
            const fetchOptions = {
                method,
                headers: requestHeaders,
                signal: controller.signal,
                mode: 'cors',
                credentials: 'omit',
                cache: 'no-cache'
            };

            // Add body if provided
            if (body) {
                fetchOptions.body = JSON.stringify(body);
            }

            // Make request
            const response = await fetch(url, fetchOptions);

            clearTimeout(timeoutId);

            console.log(`📡 Response status: ${response.status} ${response.statusText}`);
            console.log(`📡 Response headers:`, Object.fromEntries(response.headers.entries()));

            // Handle response
            if (!response.ok) {
                if (response.status === 401 && requiresAuth) {
                    if (retryCount < 1) {
                        await this.refreshAuthToken();
                        return this.makeRequest(endpoint, { ...options, retryCount: retryCount + 1 });
                    }
                }
                
                // Try to get error message from response
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                try {
                    const errorText = await response.text();
                    if (errorText) {
                        const errorJson = JSON.parse(errorText);
                        errorMessage = errorJson.message || errorJson.error || errorMessage;
                    }
                } catch (parseError) {
                    // Use default error message
                }
                
                throw new Error(errorMessage);
            }

            const responseText = await response.text();
            
            if (!responseText) {
                return { success: true, data: null };
            }

            try {
                const jsonResponse = JSON.parse(responseText);
                console.log(`✅ Successfully parsed JSON response:`, {
                    success: jsonResponse.success,
                    dataLength: Array.isArray(jsonResponse.data) ? jsonResponse.data.length : 'not-array',
                    hasMetadata: !!jsonResponse.metadata
                });
                return jsonResponse;
            } catch (parseError) {
                console.warn('⚠️ Response is not JSON, returning as text');
                return { success: true, data: responseText };
            }

        } catch (error) {
            console.error(`❌ Request failed:`, {
                url: endpoint,
                error: error.message,
                name: error.name,
                retryCount
            });

            // Enhanced error handling
            if (error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeout}ms - Server may be down or unreachable`);
            }

            if (error.name === 'TypeError') {
                if (error.message.includes('Failed to fetch')) {
                    // Try fallback URLs
                    if (retryCount === 0 && this.baseUrl.includes('**********')) {
                        console.log('🔄 Trying localhost fallback...');
                        const fallbackService = new ApiService();
                        fallbackService.baseUrl = this.baseUrl.replace('**********', 'localhost');
                        try {
                            return await fallbackService.makeRequest(endpoint, { ...options, retryCount: 1 });
                        } catch (fallbackError) {
                            console.error('❌ Localhost fallback also failed:', fallbackError.message);
                        }
                    }
                    
                    if (retryCount < this.retryAttempts) {
                        console.log(`🔄 Retrying request (${retryCount + 1}/${this.retryAttempts})...`);
                        await this.delay(this.retryDelay * (retryCount + 1));
                        return this.makeRequest(endpoint, { ...options, retryCount: retryCount + 1 });
                    }
                    
                    throw new Error(`Network error - unable to reach staging API server at ${this.baseUrl}. Please check if the server is running.`);
                }
            }

            throw error;
        }
    }

    /**
     * Refresh authentication token
     */
    async refreshAuthToken() {
        try {
            if (!this.credentials) {
                throw new Error('No credentials available for refresh');
            }

            const decryptedCreds = await this.decryptCredentials(this.credentials);
            if (!decryptedCreds || !decryptedCreds.refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await this.makeRequest('/auth/refresh', {
                method: 'POST',
                body: { refreshToken: decryptedCreds.refreshToken },
                timeout: 10000
            });

            if (response.success && response.token) {
                const newAuthData = {
                    ...decryptedCreds,
                    token: response.token,
                    expiresAt: Date.now() + (response.expiresIn * 1000)
                };

                await this.saveCredentials(newAuthData);
                return true;
            } else {
                throw new Error('Token refresh failed');
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            await chrome.storage.local.remove(['apiCredentials']);
            this.credentials = null;
            throw error;
        }
    }

    /**
     * Get current authentication status
     * @returns {Promise<Object>} Auth status
     */
    async getAuthStatus() {
        try {
            if (!this.credentials) {
                return { authenticated: false, reason: 'No credentials stored' };
            }

            const decryptedCreds = await this.decryptCredentials(this.credentials);
            if (!decryptedCreds || !decryptedCreds.token) {
                return { authenticated: false, reason: 'Invalid credentials' };
            }

            if (decryptedCreds.expiresAt && Date.now() > decryptedCreds.expiresAt) {
                return { authenticated: false, reason: 'Token expired' };
            }

            return {
                authenticated: true,
                userInfo: decryptedCreds.userInfo || {},
                expiresAt: decryptedCreds.expiresAt
            };
        } catch (error) {
            return { authenticated: false, reason: error.message };
        }
    }

    /**
     * Logout and clear credentials
     */
    async logout() {
        try {
            if (this.credentials) {
                try {
                    await this.makeRequest('/auth/logout', {
                        method: 'POST',
                        requiresAuth: true
                    });
                } catch (error) {
                    console.warn('Logout endpoint error:', error.message);
                }
            }

            await chrome.storage.local.remove(['apiCredentials']);
            this.credentials = null;

            return { success: true };
        } catch (error) {
            console.error('Logout error:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate unique execution ID
     * @returns {string} Execution ID
     */
    generateExecutionId() {
        return 'exec_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    /**
     * Utility delay function
     * @param {number} ms - Milliseconds to delay
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Validate staging data structure
     * @param {Array} data - Staging data
     * @returns {Object} Validation result
     */
    validateStagingData(data) {
        if (!Array.isArray(data)) {
            return { valid: false, error: 'Data must be an array' };
        }

        if (data.length === 0) {
            return { valid: false, error: 'No data records found' };
        }

        const requiredFields = ['employee_id', 'employee_name'];
        const missingFields = [];

        // Check first record for required fields
        const firstRecord = data[0];
        requiredFields.forEach(field => {
            const hasField = field in firstRecord || 
                             field.replace('_', '') in firstRecord ||
                             field.replace('_', '').toLowerCase() + 'id' in firstRecord ||
                             field.replace('_', '').toLowerCase() + 'name' in firstRecord;
            
            if (!hasField) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            return { 
                valid: false, 
                error: `Missing required fields: ${missingFields.join(', ')}`,
                availableFields: Object.keys(firstRecord)
            };
        }

        return { 
            valid: true, 
            recordCount: data.length,
            fields: Object.keys(firstRecord)
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiService;
} else {
    window.ApiService = ApiService;
} 