<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venus-Millware AutoFill</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <img src="icons/icon32.png" alt="Venus-Millware AutoFill" class="logo">
            <h1>Venus-Millware AutoFill</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Siap</span>
            </div>
        </div>

        <!-- Quick Run Section -->
        <div class="quick-run-section">
            <h3>🚀 Otomatisasi Timesheet</h3>
            <div class="quick-run-info">
                <div class="info-item">
                    <strong>Target:</strong> millwarep3.rebinmas.com:8003
                </div>
                <div class="info-item">
                    <strong>API:</strong> localhost:5173/api/staging/data
                </div>
            </div>
            <button class="btn btn-success btn-large btn-primary-action" id="runTimesheetAutomation" title="Jalankan otomatisasi timesheet lengkap: login + pemrosesan data">
                ⚡ Jalankan Otomatisasi
            </button>
            <small style="display: block; margin-top: 8px; color: #666; font-size: 12px;">
                Login otomatis ke sistem timesheet dan proses data staging
            </small>
        </div>
 
        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="tab-button active" data-tab="config">Konfigurasi</button>
            <button class="tab-button" data-tab="data">Pratinjau Data</button>
            <button class="tab-button" data-tab="flow">Definisi Flow</button>
            <button class="tab-button" data-tab="execution">Eksekusi</button>
        </div>

        <!-- Configuration Tab -->
        <div class="tab-content active" id="config">
            <div class="section">
                <h3>Konfigurasi Website Target</h3>
                <div class="form-group">
                    <label for="targetUrl">Target Website URL:</label>
                    <input type="url" id="targetUrl" 
                           value="http://millwarep3.rebinmas.com:8003/"
                           placeholder="http://millwarep3.rebinmas.com:8003/">
                </div>
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" 
                           value="adm075"
                           placeholder="adm075">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" 
                           value="adm075"
                           placeholder="Masukkan password">
                </div>
                <button class="btn btn-primary" id="saveConfig">Simpan Konfigurasi</button>
            </div>

            <div class="section">
                <h3>Konfigurasi Staging API</h3>
                <div class="form-group">
                    <label for="scriptUrl">API Base URL:</label>
                    <input type="url" id="scriptUrl" 
                           value="http://localhost:5173/api"
                           placeholder="http://localhost:5173/api">
                </div>
                <div class="form-group">
                    <label for="sheetName">API Key (Opsional):</label>
                    <input type="text" id="sheetName" placeholder="Masukkan API key jika diperlukan">
                </div>
                <div class="form-group">
                    <label for="delayInterval">Jeda Langkah (ms):</label>
                    <input type="number" id="delayInterval" 
                           value="1000" 
                           min="500" 
                           max="5000"
                           placeholder="1000">
                    <small>Jeda antara langkah otomatisasi (default: 1000ms)</small>
                </div>
                <button class="btn btn-secondary" id="testConnection">Test Koneksi</button>
                <button class="btn btn-secondary" id="debugConnection">Debug Koneksi</button>
            </div>

            <div class="section">
                <h3>Informasi Debug</h3>
                <div class="debug-info" id="debugInfo" style="display: none;">
                    <div class="form-group">
                        <label>Konfigurasi Saat Ini:</label>
                        <textarea id="debugOutput" readonly style="width: 100%; height: 120px; font-family: monospace; font-size: 11px; background: #f8f9fa; border: 1px solid #ddd; padding: 8px;"></textarea>
                    </div>
                    <button class="btn btn-secondary" id="copyDebugInfo">Salin Info Debug</button>
                </div>
            </div>
        </div>

        <!-- Data Preview Tab -->
        <div class="tab-content" id="data">
            <div class="section">
                <h3>Pratinjau Data Timesheet</h3>
                <div class="data-controls">
                    <button class="btn btn-primary" id="fetchData">Ambil Data Staging</button>
                    <button class="btn btn-secondary" id="refreshData">Refresh</button>
                </div>
                <div class="data-status" id="dataStatus">
                    <span>Tidak ada data timesheet yang dimuat</span>
                </div>
                <div class="data-preview" id="dataPreview">
                    <!-- Data will be populated here -->
                </div>
            </div>
        </div>

        <!-- Flow Definition Tab -->
        <div class="tab-content" id="flow">
            <div class="section">
                <h3>Definisi Alur Otomatisasi</h3>
                <div class="flow-controls">
                    <button class="btn btn-primary" id="addEvent">Tambah Event</button>
                    <button class="btn btn-secondary" id="saveFlow">Simpan Flow</button>
                    <button class="btn btn-secondary" id="loadFlow">Muat Flow</button>
                    <button class="btn btn-warning" id="loadPredefinedFlow">Muat Flow Timesheet</button>
                </div>
                <div class="flow-list" id="flowList">
                    <!-- Flow events will be listed here -->
                </div>
                <div class="flow-quick-actions" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                    <h4 style="margin-bottom: 10px; font-size: 14px; color: #333;">Aksi Cepat</h4>
                    <button class="btn btn-success btn-large" id="startFlow" title="Eksekusi alur otomatisasi yang telah didefinisikan dengan navigasi otomatis">
                        🚀 Mulai Alur Otomatisasi
                    </button>
                    <small style="display: block; margin-top: 8px; color: #666; font-size: 12px;">
                        Ini akan secara otomatis navigasi ke URL target dan menjalankan urutan otomatisasi yang telah didefinisikan.
                    </small>
                </div>
            </div>
        </div>

        <!-- Execution Tab -->
        <div class="tab-content" id="execution">
            <div class="section">
                <h3>Kontrol Eksekusi Otomatisasi</h3>
                <div class="execution-controls">
                    <button class="btn btn-success" id="runExecution">Jalankan Otomatisasi</button>
                    <button class="btn btn-warning" id="pauseExecution">Jeda</button>
                    <button class="btn btn-danger" id="stopExecution">Berhenti</button>
                </div>
                <div class="execution-status" id="executionStatus">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="status-text" id="executionStatusText">Siap untuk menjalankan otomatisasi</div>
                </div>
                <div class="execution-log" id="executionLog">
                    <!-- Execution logs will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/api-service.js"></script>
    <script src="scripts/popup.js"></script>
</body>
</html>
