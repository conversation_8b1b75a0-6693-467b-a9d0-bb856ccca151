# 🔧 Troubleshooting Guide - Chrome Extension Automation Bot

## ❌ "Fetch Failed" Error Solutions

### **Langkah 1: Pastikan Server API Berjalan**

```bash
# 1. Buka terminal/command prompt
# 2. Masuk ke folder ekstensi
cd "D:\Gawean Rebinmas\Venus Auto Fill\Ekstensi_auto_fill"

# 3. Install dependencies (jika belum)
npm install

# 4. Jalankan server
npm start
```

**✅ Server berjalan dengan benar jika muncul:**
```
🚀 Timesheet Staging API Server Started
📍 Server running at: http://localhost:5173
🔗 API Base URL: http://localhost:5173/api
🎯 Ready for Chrome Extension Automation Bot!
```

### **Langkah 2: Test API di Browser**

Buka URL berikut di browser:
- `http://localhost:5173/api/health` - Untuk test koneksi
- `http://localhost:5173/api/staging/data` - Untuk test data

**✅ Berhasil jika muncul JSON response seperti:**
```json
{
  "success": true,
  "data": [
    {
      "employee_id": "EMP001",
      "employee_name": "<PERSON>",
      ...
    }
  ]
}
```

### **<PERSON>kah 3: Reload Chrome Extension**

1. Buka `chrome://extensions/`
2. Temukan "Chrome Extension Automation Bot"
3. Klik tombol **🔄 Reload**
4. Klik icon ekstensi di toolbar

### **Langkah 4: Test Koneksi di Ekstensi**

1. Buka popup ekstensi
2. Pergi ke tab **Configuration**
3. Pastikan **API Base URL**: `http://localhost:5173/api`
4. Klik **Test Connection**

**✅ Berhasil jika muncul:** "✅ Staging API connection successful!"

### **Langkah 5: Fetch Data**

1. Pergi ke tab **Data Preview**
2. Klik **Fetch Staging Data**

**✅ Berhasil jika muncul tabel dengan data timesheet**

---

## 🚫 Masalah Umum & Solusi

### **1. "Network error - unable to reach staging API server"**

**Penyebab:**
- Server API belum berjalan
- Port 5173 sedang digunakan aplikasi lain
- Windows Firewall memblokir koneksi

**Solusi:**
```bash
# Stop semua proses Node.js
taskkill /F /IM node.exe

# Restart server
npm start

# Atau gunakan port berbeda
set PORT=5174 && npm start
```

### **2. "CORS Error" atau "Access to fetch blocked"**

**Penyebab:**
- Chrome extension permissions tidak tepat
- CORS configuration server bermasalah

**Solusi:**
1. Pastikan `manifest.json` memiliki permissions yang benar:
```json
"host_permissions": [
  "http://localhost:*/*",
  "http://127.0.0.1:*/*"
]
```

2. Reload extension di `chrome://extensions/`

### **3. "HTTP 404 - Endpoint not found"**

**Penyebab:**
- URL API salah
- Server tidak berjalan di port yang benar

**Solusi:**
```bash
# Check apakah server berjalan
netstat -an | findstr :5173

# Pastikan menggunakan URL yang benar
http://localhost:5173/api/staging/data
```

### **4. "Connection Timeout"**

**Penyebab:**
- Server lambat merespons
- Network connectivity issues

**Solusi:**
1. Increase timeout di Configuration:
   - **Step Delay**: 2000ms atau lebih

2. Restart server:
```bash
taskkill /F /IM node.exe
npm start
```

### **5. "Invalid response format"**

**Penyebab:**
- Server mengembalikan data dalam format yang tidak diharapkan

**Solusi:**
1. Test manual di browser: `http://localhost:5173/api/staging/data`
2. Pastikan response berupa JSON dengan format:
```json
{
  "success": true,
  "data": [...],
  "metadata": {...}
}
```

---

## 🔍 Tools Debugging

### **1. Test Script Manual**
```bash
node test-connection.js
```

### **2. Debug di Chrome DevTools**
1. Klik kanan icon ekstensi → **Inspect popup**
2. Buka tab **Console**
3. Cari error messages berwarna merah

### **3. Network Tab**
1. Buka **Developer Tools** (F12)
2. Tab **Network**
3. Filter: **Fetch/XHR**
4. Jalankan fetch data dan lihat request yang gagal

### **4. Extension Logs**
1. Buka `chrome://extensions/`
2. Klik **Details** pada extension
3. Klik **Inspect views: popup**
4. Lihat Console untuk error messages

---

## ⚙️ Konfigurasi Lanjutan

### **Ganti Port Server**
```bash
# Set environment variable
set PORT=5174 && npm start

# Atau edit package.json
"scripts": {
  "start": "set PORT=5174 && node staging-api-server.js"
}
```

### **Allow Firewall (Windows)**
```powershell
# Buka PowerShell as Administrator
netsh advfirewall firewall add rule name="Node.js 5173" dir=in action=allow protocol=TCP localport=5173
```

### **Menggunakan IP Address**
Jika ingin menggunakan IP address:

1. **Update server** untuk listen di semua interface:
```javascript
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running at: http://**********:${PORT}`);
});
```

2. **Update manifest.json**:
```json
"host_permissions": [
  "http://**********:*/*"
]
```

3. **Update configuration**:
```javascript
this.baseUrl = 'http://**********:5173/api';
```

---

## 📋 Checklist Troubleshooting

Ikuti checklist ini step-by-step:

- [ ] ✅ Server API berjalan (`npm start`)
- [ ] ✅ URL `http://localhost:5173/api/staging/data` bisa dibuka di browser
- [ ] ✅ Chrome extension di-reload
- [ ] ✅ Test Connection berhasil
- [ ] ✅ Fetch Data berhasil
- [ ] ✅ Data preview menampilkan tabel timesheet

---

## 🆘 Bantuan Lebih Lanjut

Jika masih ada masalah:

1. **Screenshot error message** yang muncul
2. **Copy logs** dari Console (F12 → Console)
3. **Jalankan** `node test-connection.js` dan copy output
4. **Check** apakah ada antivirus yang memblokir koneksi

---

## 🎯 Quick Fix Commands

```bash
# Stop semua proses dan restart
taskkill /F /IM node.exe
npm start

# Test koneksi
node test-connection.js

# Check port usage
netstat -an | findstr :5173
```

**Semoga membantu! 🚀** 